How To
======

### API and Swagger documentation
The API traits are located in the `nl.dpes.core.api` package. Each API has its own sub-package.
An API consists of three files:
1. Controller
2. Protocol
3. Documentation

#### Controller
The *Controller* is the trait that has all the logic, although very few. It should be just handling a request and creating a response.
It's preferably done by either sending a command (for write operations), or retrieving information from an index projection (for read operations).

#### Protocol
The *Protocol* contains all formatting of the API models that the controller is able to handle.
It contains JSON formats for marshalling/unmarshalling the API models.

#### Documentation
The *Documentation* defines the interface of the Controller and has no concrete implementation.
It does contain the Swagger annotations to generate documentation.

It is an important part of the API as it forms the basis for the API deployment in the AWS API Gateway.

Specify clearly the operation and the path, along with the default response.
Every response needs to be documented for the API Gateway to pass it through.
Unknown responses will be converted to a 500 error, so be sure you have all of them documented.

**IMPORTANT**:
Empty responses need to be defined with `response = classOf[String]` in the ApiOperation annotation,
but need to be defined on the ApiResponse annotation as `response = classOf[Void]`.
Do this to prevent model pollution, as it may generate `Function1` type with `Function1ContextFutureRouteResult`.
