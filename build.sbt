import sbt.Keys.parallelExecution
import spray.revolver.DebugSettings
import Dependencies._

lazy val javaMemory = settingKey[String]("The amount of memory to give to the JVM")

val Revision = Option(System.getProperty("revision")).getOrElse("0")

lazy val commonSettings = Seq(
  organizationName := "de Persgroep Employment Solutions",
  organizationHomepage := Some(url("http://www.persgroepemploymentsolutions.nl")),
  organization := "nl.dpes",
  startYear := Some(2017),
  scalaVersion := "2.13.10",
  updateOptions := updateOptions.value
    .withLatestSnapshots(true)
    .withGigahorse(false),
  javaMemory := "384m",
  resolvers ++= Seq(
    DefaultMavenRepository,
    Resolver.typesafeRepo("releases"),
    Resolver.sonatypeRepo("public"),
    Resolver.sbtPluginRepo("releases"),
    "snapshots" at "https://archiva.persgroep.digital/repository/snapshots",
    "internal" at "https://archiva.persgroep.digital/repository/internal",
    "releases" at "https://archiva.persgroep.digital/repository/releases"
  ),
  scalacOptions ++= Seq(
    "-feature",
    "-language:implicitConversions",
    "-language:postfixOps",
    "-Wconf:cat=other-match-analysis:error,any:wv"
  ),
  run / fork := true,
  run / javaOptions ++= Seq(
    "--add-opens",
    "java.base/java.util.concurrent=ALL-UNNAMED"
  ),
  Test / fork := true,
  Test / parallelExecution := false,
  Test / publishArtifact := false,
  Test / logBuffered := false,
  ThisBuild / evictionErrorLevel := Level.Info,
  akkaGrpcGeneratedSources := Seq(AkkaGrpc.Client)
)

lazy val `platform-core` = (project in file("."))
  .enablePlugins(AkkaGrpcPlugin)
  .settings(
    commonSettings,
    name := "platform.core",
    version := s"1.0.$Revision",
    description := "Core component of the dPES platform",
    javaMemory := "384m",
    libraryDependencies ++= Seq(
      Dpes.axon4s,
      Dpes.kinesis,
      Dpes.eventDefinitions,
      Dpes.serviceDefinitions,
      Dpes.domain,
      Axoniq.axon4,
      Axoniq.axon4javaDoc,
      Akka.slf4j,
      Akka.testkit,
      Akka.streamTestkit,
      Pekko.actor,
      Pekko.protobuf,
      Pekko.stream,
      Pekko.slf4j,
      PekkoHttp.http,
      PekkoHttp.cors,
      PekkoHttp.spray,
      Http4s.nettyServer,
      Http4s.server,
      Http4s.circe,
      Axon.config,
      Axon.eventSourcing,
      Axon.legacy,
      Axon.messaging,
      Axon.modelling,
      Axon.test,
      ScalikeJdbc.core,
      ScalikeJdbc.test,
      Sttp.core,
      Sttp.cats,
      Sttp.spray,
      Kamon.bundle,
      Kamon.prometheus,
      Elastic4s.core,
      Elastic4s.http,
      Logback.classic,
      Logback.json,
      Logback.jackson,
      Netty.codecHttp,
      Netty.handler,
      Netty.codec,
      Netty.transport,
      Netty.resolver,
      Netty.buffer,
      Netty.common,
      Other.grpcNettyShaded,
      Other.xstream,
      Other.akkaCors,
      Other.scalatest,
      Other.awsSdk,
      Other.mysql,
      Other.hamcrest,
      Other.mockito,
      Other.rxscala,
      Other.javaJwt,
      Other.jbCrypt,
      Other.quartz,
      Other.jaxbRuntime,
      Other.scalacheck,
      Other.scalacheckToolbox,
      Other.scalacheckShapeless,
      Other.scalatestDiffx,
      Other.sslConfig,
      Other.checkers,
      Tapir.core,
      Tapir.json,
      Tapir.swagger,
      Tapir.pekko,
      Tapir.http4s,
      Circe.core,
      Circe.generic,
      Circe.extras,
      Doobie.`doobie-core`,
      Doobie.`doobie-hikari`,
      Doobie.`doobie-postgres`,
      Doobie.`doobie-postgres-circe`,
      MySql.`mysql-connector-j`,
      Weaver.`weaver-cats`,
      Weaver.`weaver-scalacheck`,
      TestContainers.`mysql`,
      TestContainers.`testcontainers`
    ),
    dependencyOverrides ++= depsOverride,
    sonarProperties ++= Map(
      "sonar.host.url"             -> "https://sonar.persgroep.digital",
      "sonar.projectVersion"       -> version.value,
      "sonar.projectKey"           -> Seq(organization.value, name.value).mkString("."),
      "sonar.projectName"          -> "Core service",
      "sonar.scoverage.reportPath" -> (crossTarget.value / "scoverage-report" / "scoverage.xml").getPath,
      "sonar.sourceEncoding"       -> "UTF-8"
    ),
    Universal / javaOptions ++= Seq(
      "-Xmx" + javaMemory.value,
      "-Dlogback.configurationFile=conf/logback.xml"
    ),
    Universal / mappings += {
      file("src/main/resources/logback-pro.xml") -> "conf/logback.xml"
    },
    reStart / javaOptions ++= Seq(
      "-Dcom.amazonaws.sdk.disableCbor=1"
    ),
    reStart / mainClass := Some("nl.dpes.core.WebServer"),
    reStart / debugSettings := Some(DebugSettings(7007)),
    reStart / javaOptions ++= Seq(
      "--add-opens",
      "java.base/java.util.concurrent=ALL-UNNAMED"
    ),
    coverageMinimumStmtTotal := 50,
    coverageFailOnMinimum := true,
    testFrameworks += new TestFramework("weaver.framework.CatsEffect")
  )
