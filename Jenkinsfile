pipeline {
  agent {
    docker {
        label 'alpakka_java21'
        image '674201978047.dkr.ecr.eu-west-1.amazonaws.com/jenkins:jdk-11'
        args '-u root -v $HOME/.kube:/root/.kube -v /var/run/docker.sock:/var/run/docker.sock --network host'
    }
  }
  stages {
    stage('Test') {
      steps {
        ansiColor('xterm') {
          sh "sbt -Drevision=${BUILD_NUMBER} clean cleanFiles scalafmtAll startTestingContainers coverage test coverageReport"
        }
      }
      post {
        always {
          ansiColor('xterm') {
            sh "sbt stopTestingContainers"
          }
        }
      }
    }
    stage('Build') {
      steps {
        ansiColor('xterm') {
          sh "aws ecr get-login-password --region eu-west-1 | docker login --username AWS --password-stdin 674201978047.dkr.ecr.eu-west-1.amazonaws.com"
          sh "sbt -Dversion=${BUILD_NUMBER} docker:publish"
        }
      }
    }
    stage('Deploy Acceptance') {
      steps {
        ansiColor('xterm') {
          sh "sbt \"deploy acc ${BUILD_NUMBER}\""
        }
      }
      post {
        failure {
          ansiColor('xterm') {
            sh "sbt \"rollback acc\""
          }
        }
      }
    }
    stage('K8S config sync - ACC') {
      steps {
        build job: 'kops.cloud.sync.acc', wait: false
      }
    }
    stage('Deploy Production') {
      steps {
        ansiColor('xterm') {
          sh "sbt \"deploy pro ${BUILD_NUMBER}\""
        }
      }
      post {
        failure {
          ansiColor('xterm') {
            sh "sbt \"rollback pro\""
          }
        }
      }
    }
    stage('K8S config sync - PRO') {
      steps {
        build job: 'kops.cloud.sync.pro', wait: false
      }
    }
  }
}
