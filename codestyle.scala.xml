<code_scheme name="dPES">
  <option name="LINE_SEPARATOR" value="&#xA;" />
  <option name="RIGHT_MARGIN" value="160" />
  <JSCodeStyleSettings>
    <option name="INDENT_CHAINED_CALLS" value="false" />
  </JSCodeStyleSettings>
  <ScalaCodeStyleSettings>
    <option name="USE_ALTERNATE_CONTINUATION_INDENT_FOR_PARAMS" value="true" />
    <option name="ALTERNATE_CONTINUATION_INDENT_FOR_PARAMS" value="2" />
  </ScalaCodeStyleSettings>
  <XML>
    <option name="XML_LEGACY_SETTINGS_IMPORTED" value="true" />
  </XML>
  <codeStyleSettings language="Groovy">
    <option name="RIGHT_MARGIN" value="120" />
  </codeStyleSettings>
  <codeStyleSettings language="HTML">
    <indentOptions>
      <option name="INDENT_SIZE" value="2" />
      <option name="CONTINUATION_INDENT_SIZE" value="2" />
      <option name="TAB_SIZE" value="2" />
    </indentOptions>
  </codeStyleSettings>
  <codeStyleSettings language="JAVA">
    <option name="RIGHT_MARGIN" value="120" />
  </codeStyleSettings>
  <codeStyleSettings language="JSON">
    <option name="RIGHT_MARGIN" value="120" />
  </codeStyleSettings>
  <codeStyleSettings language="Jade">
    <indentOptions>
      <option name="INDENT_SIZE" value="2" />
      <option name="TAB_SIZE" value="2" />
    </indentOptions>
  </codeStyleSettings>
  <codeStyleSettings language="JavaScript">
    <option name="RIGHT_MARGIN" value="120" />
    <indentOptions>
      <option name="INDENT_SIZE" value="2" />
      <option name="CONTINUATION_INDENT_SIZE" value="2" />
      <option name="TAB_SIZE" value="2" />
    </indentOptions>
  </codeStyleSettings>
  <codeStyleSettings language="PHP">
    <option name="RIGHT_MARGIN" value="120" />
  </codeStyleSettings>
  <codeStyleSettings language="Scala">
    <option name="KEEP_BLANK_LINES_IN_DECLARATIONS" value="1" />
    <option name="KEEP_BLANK_LINES_IN_CODE" value="1" />
    <option name="KEEP_BLANK_LINES_BEFORE_RBRACE" value="0" />
  </codeStyleSettings>
  <codeStyleSettings language="TypeScript">
    <option name="RIGHT_MARGIN" value="160" />
    <option name="KEEP_BLANK_LINES_IN_CODE" value="1" />
    <indentOptions>
      <option name="INDENT_SIZE" value="2" />
      <option name="CONTINUATION_INDENT_SIZE" value="2" />
      <option name="TAB_SIZE" value="2" />
    </indentOptions>
  </codeStyleSettings>
</code_scheme>
