import sbt.*

object Dependencies {

  val AkkaVersion      = "2.7.0"
  val PekkoVersion     = "1.0.3"
  val PekkoHttpVersion = "1.0.1"
  val JacksonVersion   = "2.13.3"

  private val excludeAkka                = ExclusionRule(organization = "com.typesafe.akka")
  private val excludeSlf4jApi            = ExclusionRule("org.slf4j", "slf4j-api")
  private val excludeTypesafeConfig      = ExclusionRule("com.typesafe", "config")
  private val excludeAxonServerConnector = ExclusionRule("org.axonframework", "axon-server-connector")

  object Akka {
    val slf4j         = "com.typesafe.akka" %% "akka-slf4j"          % AkkaVersion excludeAll excludeSlf4jApi
    val testkit       = "com.typesafe.akka" %% "akka-testkit"        % AkkaVersion % Test
    val streamTestkit = "com.typesafe.akka" %% "akka-stream-testkit" % AkkaVersion % Test
  }

  object Pekko {
    val actor    = "org.apache.pekko" %% "pekko-actor"    % PekkoVersion
    val slf4j    = "org.apache.pekko" %% "pekko-slf4j"    % PekkoVersion
    val protobuf = "org.apache.pekko" %% "pekko-protobuf" % PekkoVersion
    val stream   = "org.apache.pekko" %% "pekko-stream"   % PekkoVersion

  }

  object PekkoHttp {
    val http  = "org.apache.pekko" %% "pekko-http"            % PekkoHttpVersion
    val spray = "org.apache.pekko" %% "pekko-http-spray-json" % PekkoHttpVersion
    val cors  = "org.apache.pekko" %% "pekko-http-cors"       % PekkoHttpVersion
  }

  object Axon {
    private val version = "4.6.3"

    val config        = "org.axonframework" % "axon-configuration" % version
    val eventSourcing = "org.axonframework" % "axon-eventsourcing" % version
    val legacy        = "org.axonframework" % "axon-legacy"        % version
    val messaging     = "org.axonframework" % "axon-messaging"     % version
    val modelling     = "org.axonframework" % "axon-modelling"     % version
    val test          = "org.axonframework" % "axon-test"          % version % Test
  }

  object Axoniq {
    private val version = "1.4"

    val axon4        = "io.axoniq" % "axoniq-gdpr-axon4" % version
    val axon4javaDoc = "io.axoniq" % "axoniq-gdpr-axon4" % version classifier "javadoc"
  }

  object Circe {
    private val version = "0.14.3"

    val core    = "io.circe" %% "circe-core"           % version
    val generic = "io.circe" %% "circe-generic"        % version
    val parser  = "io.circe" %% "circe-parser"         % version
    val extras  = "io.circe" %% "circe-generic-extras" % version
  }

  object Http4s {
    private val version      = "0.23.24"
    private val nettyVersion = "0.5.16"

    val nettyServer = "org.http4s" %% "http4s-netty-server" % nettyVersion
    val client      = "org.http4s" %% "http4s-netty-client" % nettyVersion
    val server      = "org.http4s" %% "http4s-server"       % version
    val circe       = "org.http4s" %% "http4s-circe"        % version
  }

  object Kamon {
    private val version = "2.7.7"

    val bundle     = "io.kamon" %% "kamon-bundle"     % version
    val prometheus = "io.kamon" %% "kamon-prometheus" % version
  }

  object Dpes {
    private val b2bVersion = "8712"

    val axon4s             = "nl.dpes"     %% "axon4s"              % "5.0.30" withSources () withJavadoc () excludeAll excludeAxonServerConnector
    val kinesis            = "nl.dpes"     %% "kinesis"             % "4.0.87" withSources () withJavadoc ()
    val eventDefinitions   = "nl.dpes"     %% "event-definitions"   % "8.2.11"
    val serviceDefinitions = "nl.dpes.b2b" %% "service-definitions" % b2bVersion
    val domain             = "nl.dpes.b2b" %% "domain"              % b2bVersion
  }

  object Elastic4s {
    private val version = "6.7.8"

    val core = "com.sksamuel.elastic4s" %% "elastic4s-core" % version
    val http = "com.sksamuel.elastic4s" %% "elastic4s-http" % version
  }

  object Logback {
    val classic = "ch.qos.logback"         % "logback-classic"      % "1.2.11"
    val json    = "ch.qos.logback.contrib" % "logback-json-classic" % "0.1.5"
    val jackson = "ch.qos.logback.contrib" % "logback-jackson"      % "0.1.5"
  }

  object Other {
    val xstream             = "com.thoughtworks.xstream"    % "xstream"                     % "1.4.19"
    val akkaCors            = "ch.megard"                  %% "akka-http-cors"              % "1.1.3" excludeAll excludeAkka
    val scalatest           = "org.scalatest"              %% "scalatest"                   % "3.1.4"   % Test
    val awsSdk              = "com.amazonaws"               % "aws-java-sdk-bundle"         % "1.12.767"
    val mysql               = "mysql"                       % "mysql-connector-java"        % "8.0.29"
    val hamcrest            = "org.hamcrest"                % "hamcrest-core"               % "1.3"     % Test
    val mockito             = "org.scalatestplus"          %% "mockito-3-2"                 % "3.1.2.0" % Test
    val checkers            = "org.scalatestplus"          %% "scalacheck-1-14"             % "3.1.2.0" % Test
    val rxscala             = "io.reactivex"               %% "rxscala"                     % "0.27.0"
    val grpcNettyShaded     = "io.grpc"                     % "grpc-netty-shaded"           % "1.48.1"
    val javaJwt             = "com.auth0"                   % "java-jwt"                    % "3.3.0"
    val jbCrypt             = "org.mindrot"                 % "jbcrypt"                     % "0.4"
    val quartz              = "org.quartz-scheduler"        % "quartz"                      % "2.3.2"
    val jaxbRuntime         = "org.glassfish.jaxb"          % "jaxb-runtime"                % "2.3.1"
    val scalacheck          = "org.scalacheck"             %% "scalacheck"                  % "1.14.1"  % Test
    val scalacheckToolbox   = "com.47deg"                  %% "scalacheck-toolbox-datetime" % "0.3.5"   % Test
    val scalacheckShapeless = "com.github.alexarchambault" %% "scalacheck-shapeless_1.15"   % "1.3.0"   % Test
    val scalatestDiffx      = "com.softwaremill.diffx"     %% "diffx-scalatest"             % "0.3.12"  % Test
    val sslConfig           = "com.typesafe"               %% "ssl-config-core"             % "0.6.1"
  }

  object Sttp {
    private val version = "3.9.5"

    val core  = "com.softwaremill.sttp.client3" %% "core"                           % version
    val spray = "com.softwaremill.sttp.client3" %% "spray-json"                     % version
    val cats  = "com.softwaremill.sttp.client3" %% "async-http-client-backend-cats" % version
  }

  object ScalikeJdbc {
    private val version = "3.4.2"

    val core = "org.scalikejdbc" %% "scalikejdbc"      % version
    val test = "org.scalikejdbc" %% "scalikejdbc-test" % version % Test
  }

  object Tapir {
    private val version = "1.2.5"

    val core = "com.softwaremill.sttp.tapir" %% "tapir-core"       % version
    val json = "com.softwaremill.sttp.tapir" %% "tapir-json-circe" % version

    val http4s = "com.softwaremill.sttp.tapir" %% "tapir-http4s-server"     % version
    val akka   = "com.softwaremill.sttp.tapir" %% "tapir-akka-http-server"  % version exclude ("com.typesafe.akka", "akka-stream_2.13")
    val pekko  = "com.softwaremill.sttp.tapir" %% "tapir-pekko-http-server" % "1.7.3" exclude ("org.apache.pekko", "pekko-stream_2.12")

    val swagger = "com.softwaremill.sttp.tapir" %% "tapir-swagger-ui-bundle" % version
  }

  object Netty {
    private val version = "4.1.79.Final"

    val codecHttp = "io.netty" % "netty-codec-http" % version
    val handler   = "io.netty" % "netty-handler"    % version
    val codec     = "io.netty" % "netty-codec"      % version
    val transport = "io.netty" % "netty-transport"  % version
    val resolver  = "io.netty" % "netty-resolver"   % version
    val buffer    = "io.netty" % "netty-buffer"     % version
    val common    = "io.netty" % "netty-common"     % version
  }

  val depsOverride = Seq(
    "com.typesafe.akka"             %% "akka-actor"                 % AkkaVersion,
    "com.typesafe.akka"             %% "akka-actor-typed"           % AkkaVersion excludeAll excludeSlf4jApi,
    "com.typesafe.akka"             %% "akka-protobuf-v3"           % AkkaVersion excludeAll excludeSlf4jApi,
    "com.typesafe.akka"             %% "akka-serialization-jackson" % AkkaVersion excludeAll excludeSlf4jApi,
    "com.typesafe.akka"             %% "akka-discovery"             % AkkaVersion,
    "com.typesafe.akka"             %% "akka-stream"                % AkkaVersion excludeAll excludeTypesafeConfig,
    "com.typesafe.akka"             %% "akka-stream-typed"          % AkkaVersion excludeAll excludeTypesafeConfig,
    "com.fasterxml.jackson.core"     % "jackson-annotations"        % JacksonVersion,
    "com.fasterxml.jackson.core"     % "jackson-core"               % JacksonVersion,
    "com.fasterxml.jackson.core"     % "jackson-databind"           % JacksonVersion,
    "com.fasterxml.jackson.datatype" % "jackson-datatype-jdk8"      % JacksonVersion,
    "com.fasterxml.jackson.datatype" % "jackson-datatype-jsr310"    % JacksonVersion,
    "com.fasterxml.jackson.module"  %% "jackson-module-scala"       % JacksonVersion
  )

  object Doobie {
    val DoobieVersion = "1.0.0-RC9"

    val `doobie-core`           = "org.tpolecat" %% "doobie-core"           % DoobieVersion
    val `doobie-hikari`         = "org.tpolecat" %% "doobie-hikari"         % DoobieVersion
    val `doobie-postgres`       = "org.tpolecat" %% "doobie-postgres"       % DoobieVersion
    val `doobie-postgres-circe` = "org.tpolecat" %% "doobie-postgres-circe" % DoobieVersion
  }

  object MySql {
    val MySqlVersion = "9.3.0"

    val `mysql-connector-j` = "com.mysql" % "mysql-connector-j" % MySqlVersion
  }

  object Weaver {
    val WeaverVersion = "0.8.4"

    val `weaver-cats`       = "com.disneystreaming" %% "weaver-cats"       % WeaverVersion % Test
    val `weaver-scalacheck` = "com.disneystreaming" %% "weaver-scalacheck" % WeaverVersion % Test
  }

  object TestContainers {
    val TestContainerVersion = "1.21.1"

    val `testcontainers` = "org.testcontainers" % "testcontainers" % TestContainerVersion % Test
    val `mysql`          = "org.testcontainers" % "mysql"          % TestContainerVersion % Test
  }
}
