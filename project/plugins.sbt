resolvers ++= Seq(
  DefaultMavenRepository,
  Resolver.typesafeRepo("releases"),
  Resolver.sonatypeRepo("public"),
  Resolver.sbtPluginRepo("releases")
)

libraryDependencies += "com.amazonaws"      % "aws-java-sdk-bundle" % "1.12.767"
libraryDependencies += "com.typesafe.play" %% "twirl-api"           % "1.5.2"

addSbtPlugin("io.spray"                % "sbt-revolver"             % "0.9.1")
addSbtPlugin("com.github.sbt"          % "sbt-native-packager"      % "1.9.11")
addSbtPlugin("org.scoverage"           % "sbt-scoverage"            % "2.0.5")
addSbtPlugin("com.olaq"                % "sbt-sonar-scanner-plugin" % "1.3.0")
addSbtPlugin("com.lightbend.akka.grpc" % "sbt-akka-grpc"            % "2.2.1")
addSbtPlugin("org.scalameta"           % "sbt-scalafmt"             % "2.4.0")
addSbtPlugin("io.kamon"                % "sbt-kanela-runner"        % "2.0.14")
addDependencyTreePlugin
