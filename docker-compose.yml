version: '2'
services:
  swagger-ui:
    image: swaggerapi/swagger-ui:v3.1.6
    ports:
      - 5555:8080
    environment:
      - API_URL=http://localhost:8999/api/docs/docs.yaml

  mysql:
    image: mysql:8.0-debian
    ports:
     - 8998:3306
    environment:
     - MYSQL_DATABASE=platform_core
     - MYSQL_USER=platform_core
     - MYSQL_PASSWORD=password
     - MYSQL_RANDOM_ROOT_PASSWORD=yes

  php:
    image: php:5.6-alpine

  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:6.2.3
    ports:
     - 8996:9200
     - 8995:9300
    environment:
      - discovery.type=single-node
      - cluster.name=platform_cluster
      - xpack.security.enabled=false
      - ES_JAVA_OPTS=-Xmx2g -Xms2g
    ulimits:
      nofile:
        soft: 65536
        hard: 65536

  aws_localstack:
    image: localstack/localstack
    ports:
      - 4666:4566
    environment:
     - "SERVICES=kinesis,sqs,dynamodb"
     - "DEFAULT_REGION=eu-west-1"
     - AWS_CBOR_DISABLE=1
