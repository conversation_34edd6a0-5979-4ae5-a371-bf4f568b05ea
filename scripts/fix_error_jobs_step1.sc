import coursier.core.Authentication, coursier.MavenRepository

interp.repositories() ++= Seq(MavenRepository(
  "https://archiva.persgroep.digital/repository/snapshots",
  authentication = Some(Authentication("dpes-dev", sys.env("ARCHIVA_PASSWORD")))
))
interp.repositories() ++= Seq(MavenRepository(
  "https://archiva.persgroep.digital/repository/internal",
  authentication = Some(Authentication("dpes-dev", sys.env("ARCHIVA_PASSWORD")))
))

@

import $ivy.`mysql:mysql-connector-java:5.1.46`
import $ivy.`org.scalikejdbc:scalikejdbc_2.12:3.2.3`
import $ivy.`org.axonframework:axon-core:3.0.7`
import $ivy.`org.quartz-scheduler:quartz:2.3.0`
import $cp.^.target.`scala-2.12`.`platform-core_2.12-1.0.0.jar`
import $ivy.`nl.dpes:axon4s_2.12:0.1.1-SNAPSHOT`
import $ivy.`io.spray:spray-json_2.12:1.3.3`

@

import scalikejdbc._
import java.sql._
import java.io._
import scala.util.{Try, Success, Failure}
import org.quartz.utils.ConnectionProvider
import org.quartz.JobDataMap
import org.axonframework.eventhandling.GenericEventMessage
import nl.dpes.core.domain.OpgeslagenZoekopdrachtSaga.FrequentietijdVerstreken
import nl.dpes.core.domain.werkzoekende.VerificatieSaga.VerificatiePeriodeVerstreken
import nl.dpes.core.domain.werkzoekende.VerificatieSaga.VerificatieHerinneringGewenst
import spray.json._

import scala.io._

implicit val session: AutoSession = AutoSession

class QuartzConnectionProvider extends ConnectionProvider {
  override def getConnection: Connection = {
    val connection = ConnectionPool.borrow()
    connection.setReadOnly(false)
    connection
  }

  override def shutdown(): Unit = ()

  override def initialize(): Unit = ()
}

val EVENT_KEY = "org.axonframework.eventhandling.EventMessage"

val connections = new QuartzConnectionProvider

var succeeded: Int = 0
var invalid: Int = 0
var other: Int = 0

object JsonProtocol extends DefaultJsonProtocol {
  case class EventTrigger(job: String, payloadType: String, payload: String)

  implicit val eventFormat = jsonFormat3(EventTrigger)
  implicit val frequentietijdVerstrekenFormat = jsonFormat2(FrequentietijdVerstreken)
  implicit val verificatieperiodeVerstrekenFormat = jsonFormat1(VerificatiePeriodeVerstreken)
  implicit val verificatieHerinneringGewenstFormat = jsonFormat1(VerificatieHerinneringGewenst)
}

@main
def main(dbHost: String, password: String, file: String): Unit = {
  import JsonProtocol._

  GlobalSettings.loggingSQLAndTime = LoggingSQLAndTimeSettings(enabled = false)
  Class.forName("com.mysql.jdbc.Driver")
  ConnectionPool.singleton(s"****************************************************************************************", "platform_core", password)

  val events = sql"""
     SELECT QRTZ_JOB_DETAILS.JOB_DATA AS blobje, QRTZ_JOB_DETAILS.JOB_NAME as name
     FROM QRTZ_JOB_DETAILS, QRTZ_TRIGGERS
     WHERE QRTZ_JOB_DETAILS.JOB_NAME = QRTZ_TRIGGERS.JOB_NAME
   """.map(rs => rs.string("name") -> rs.binaryStream("blobje")).list().apply().flatMap { values =>
    Try {
      val ois = new ObjectInputStream(values._2)
      val obj: JobDataMap = ois.readObject().asInstanceOf[JobDataMap]
      ois.close()

      obj
    } match {
      case Success(obj) =>
        succeeded += 1
        Some(
          (values._1, obj.get(EVENT_KEY).asInstanceOf[GenericEventMessage[_]].getPayload)
        )
      case Failure(_: InvalidClassException) =>
        invalid += 1
        None
      case Failure(e) =>
        other += 1
        sys.error(e.getMessage)
        None
    }
  }

  Try {
    val fos = new FileOutputStream(file)

    events.foreach {
      case (key, event: FrequentietijdVerstreken) =>
        val eventTrigger = EventTrigger(key, event.getClass.getName, event.toJson.toString)
        val json = eventTrigger.toJson
        fos.write((json.toString + "\n").getBytes("UTF-8"))
      case (key, event: VerificatiePeriodeVerstreken) =>
        val eventTrigger = EventTrigger(key, event.getClass.getName, event.toJson.toString)
        val json = eventTrigger.toJson
        fos.write((json.toString + "\n").getBytes("UTF-8"))
      case (key, event: VerificatieHerinneringGewenst) =>
        val eventTrigger = EventTrigger(key, event.getClass.getName, event.toJson.toString)
        val json = eventTrigger.toJson
        fos.write((json.toString + "\n").getBytes("UTF-8"))
      case (_, event) =>
        sys.error(s"Unknown event type: ${event.getClass.getName}")
    }

    fos.close()
  } match {
    case Success(_) =>
      println(s"Successfully exported $succeeded events, $invalid invalids, $other failures")
    case Failure(e) =>
      sys.error(e.getMessage)
  }
}
