import $ivy.`io.get-coursier::coursier:1.0.1`

@

import coursier.core.Authentication, coursier.MavenRepository

interp.repositories() ++= Seq(MavenRepository(
  "https://archiva.persgroep.digital/repository/snapshots",
  authentication = Some(Authentication("dpes-dev", sys.env("ARCHIVA_PASSWORD")))
))
interp.repositories() ++= Seq(MavenRepository(
  "https://archiva.persgroep.digital/repository/internal",
  authentication = Some(Authentication("dpes-dev", sys.env("ARCHIVA_PASSWORD")))
))

@

import java.sql.{Connection, SQLException}

import $cp.^.target.`scala-2.12`.`platform-core_2.12-1.0.0.jar`
import $ivy.`com.typesafe.akka::akka-http-core:10.1.1`
import $ivy.`com.typesafe.akka::akka-actor:2.5.11`
import $ivy.`com.typesafe.akka::akka-stream:2.5.11`
import $ivy.`com.typesafe.akka::akka-slf4j:2.5.11`
import $ivy.`org.scalikejdbc::scalikejdbc:3.2.3`
import $ivy.`org.scalikejdbc::scalikejdbc-streams:3.2.3`
import $ivy.`ch.qos.logback:logback-classic:1.2.3`
import $ivy.`mysql:mysql-connector-java:5.1.46`
import $ivy.`io.axoniq:axoniq-gdpr-axon3:1.1`
import $ivy.`org.axonframework:axon-core:3.0.7`
import $ivy.`nl.dpes::common:0.1.15-SNAPSHOT`
import org.slf4j.LoggerFactory
import ch.qos.logback.classic.{Logger, Level}
import scalikejdbc._
import scalikejdbc.streams._
import akka.stream.scaladsl.{Source, Sink}
import org.axonframework.eventsourcing.eventstore.jdbc.{EventSchema, JdbcEventStorageEngine, JdbcSQLErrorCodesResolver}
import nl.dpes.common.serialization.xml.XStreamSerializer
import nl.dpes.utils.events.upcasting.zoekopdracht.{AangemaaktDoorRecruiterUpcaster, OpgeslagenDoorRecruiterUpcaster}
import org.axonframework.common.jdbc.JdbcUtils.executeBatch
import org.axonframework.common.jdbc.{ConnectionProvider, PersistenceExceptionResolver, UnitOfWorkAwareConnectionProviderWrapper}
import org.axonframework.common.transaction.{NoTransactionManager,TransactionManager}
import org.axonframework.eventsourcing.DomainEventMessage
import org.axonframework.serialization.MessageSerializer.{serializeMetaData, serializePayload}
import org.axonframework.serialization.Serializer
import org.axonframework.serialization.upcasting.event.{EventUpcaster, EventUpcasterChain}
import io.axoniq.gdpr.api.FieldEncryptingSerializer
import io.axoniq.gdpr.cryptoengine.{CryptoEngine, JdbcCryptoEngine, KeyType}
import org.axonframework.eventsourcing.GenericDomainEventMessage
import akka.actor.ActorSystem
import akka.stream.ActorMaterializer
import scala.concurrent.ExecutionContextExecutor
import scala.concurrent.Await
import scala.concurrent.duration.Duration
import akka.NotUsed
import akka.stream.FlowShape
import akka.stream.scaladsl.{Balance, Flow, GraphDSL, Merge}

val logger: Logger = LoggerFactory.getLogger("nl.dpes.core").asInstanceOf[Logger]
logger.setLevel(Level.INFO)

// Streams
implicit val system: ActorSystem = ActorSystem("LoadAndSaveEvents")
implicit val materializer: ActorMaterializer = ActorMaterializer()
implicit val ec: ExecutionContextExecutor = system.dispatcher

// Database
implicit val session: AutoSession = AutoSession
def setUpDatabase(environment: String, mysqlPassword: String): Unit = {
  val host = environment match {
    case "dev" => "localhost:8998"
    case _ => s"ndp-core-$environment.cxsobnhldvs2.eu-west-1.rds.amazonaws.com:3306"
  }

  val ssl = environment match {
    case "dev" => "&useSSL=false"
    case _ => ""
  }

  // Load MySQL driver
  GlobalSettings.loggingSQLAndTime = LoggingSQLAndTimeSettings(enabled = false)
  Class.forName("com.mysql.jdbc.Driver")
  ConnectionPool.singleton(s"*****************************************************************************", "platform_core", mysqlPassword)
}
lazy val connectionProvider: ConnectionProvider = new UnitOfWorkAwareConnectionProviderWrapper(
  new ConnectionProvider {
    override def getConnection: Connection = {
      val connection = ConnectionPool.borrow()
      connection.setReadOnly(false)
      connection
    }
  }
)

// Axon
scala.util.Properties.setProp("axoniq.gdpr.license", scala.util.Properties.envOrElse("AXONIQ_GDPR_LICENSE_FILE", "../axoniq.license"))
lazy val cryptoEngine: CryptoEngine = {
  val cryptoEngine = new JdbcCryptoEngine(ConnectionPool.dataSource(), "GDPRModuleKeys")
  cryptoEngine.setKeyType(KeyType.AES_256)

  if (!connectionProvider.getConnection.getMetaData.getTables(null, null, "GDPRModuleKeys", null).next()) {
    connectionProvider.getConnection.prepareStatement(cryptoEngine.getCreateTableStatement).execute()
  }

  cryptoEngine
}
lazy val serializer = new FieldEncryptingSerializer(cryptoEngine, new XStreamSerializer)
lazy val eventStore = new EncryptInPlaceEventStorageEngine(
  serializer,
  new EventUpcasterChain(
    new AangemaaktDoorRecruiterUpcaster(serializer),
    new OpgeslagenDoorRecruiterUpcaster(serializer)
  ),
  new JdbcSQLErrorCodesResolver,
  connectionProvider,
  NoTransactionManager.instance()
)

@main
def main(environment: String, mysqlPassword: String, aggregate: String): Unit = {
  logger.info(s"Loading and saving events on $environment for aggregate $aggregate")

  setUpDatabase(environment, mysqlPassword)

  val aggregates = aggregate match {
    case "all" =>
      allAggregateIds
    case aggregateId =>
      Source.single(aggregateId)
  }

  val future = aggregates.via(flow).runWith(Sink.foreach(aggregateId => logger.info(s"Processed aggregate $aggregateId")))
  Await.ready(future, Duration.Inf)
}

def flow: Flow[String, String, NotUsed] = Flow.fromGraph(GraphDSL.create() { implicit builder =>
  import GraphDSL.Implicits._

  val parallelism = 5
  val balancer = builder.add(Balance[String](parallelism))
  val merge = builder.add(Merge[String](parallelism))

  for (index <- 0 until parallelism) {
    balancer.out(index) ~> loadAndSaveAggregateFlow.async ~> merge.in(index)
  }

  FlowShape(balancer.in, merge.out)
})

def loadAndSaveAggregateFlow: Flow[String, String, NotUsed] = {
  Flow[String].map { aggregateId =>
    import scala.collection.JavaConverters._

    val events = eventStore.readEvents(aggregateId).asStream().iterator.asScala.toList

    logger.debug(s"Processing aggregate $aggregateId, ${events.length} events found")
    eventStore.overwriteEvents(events)
    aggregateId
  }
}

lazy val allAggregateIds: Source[String, _] = {
  Source.fromPublisher(DB.readOnlyStream {
    sql"""
        SELECT aggregateIdentifier, MIN(globalIndex) as firstIndex
        FROM DomainEventEntry
        WHERE type = 'Werkzoekende'
        OR type = 'Recruiter'
        GROUP BY aggregateIdentifier
        ORDER BY firstIndex ASC
     """.map(_.string("aggregateIdentifier")).iterator
  })
}

class EncryptInPlaceEventStorageEngine(serializer: Serializer,
                                       upcasterChain: EventUpcaster,
                                       persistenceExceptionResolver: PersistenceExceptionResolver,
                                       connectionProvider: ConnectionProvider,
                                       transactionManager: TransactionManager,
                                       dataType: Class[_] = classOf[Array[Byte]])
  extends JdbcEventStorageEngine(serializer, upcasterChain, persistenceExceptionResolver, null, connectionProvider, transactionManager,
                                 dataType, new EventSchema(), null, null) {

  def overwriteEvents(events: List[_ <: DomainEventMessage[_]]): Unit = {
    transactionManager.executeInTransaction(() => executeBatch(connectionProvider.getConnection, (connection: Connection) => {
      val sql = s"""UPDATE ${schema.domainEventTable()}
                   |SET ${schema.eventIdentifierColumn} = ?,
                   |    ${schema.aggregateIdentifierColumn} = ?,
                   |    ${schema.sequenceNumberColumn} = ?,
                   |    ${schema.typeColumn} = ?, ${schema.timestampColumn} = ?,
                   |    ${schema.payloadTypeColumn} = ?,
                   |    ${schema.payloadRevisionColumn} = ?,
                   |    ${schema.payloadColumn} = ?,
                   |    ${schema.metaDataColumn} = ?
                   |WHERE ${schema.eventIdentifierColumn()} = ?""".stripMargin
      val preparedStatement = connection.prepareStatement(sql)

      events.foreach(evt => {
        val event = new GenericDomainEventMessage(evt.getType, evt.getAggregateIdentifier, evt.getSequenceNumber, evt.getPayload, evt.getMetaData, evt.getIdentifier, evt.getTimestamp)
        val payload = serializePayload(event, serializer, dataType)
        val metaData = serializeMetaData(event, serializer, dataType)

        logger.debug(s"Serializer ${serializer.getClass}")
        logger.debug(s"Existing payload ${event.getPayload.toString}")
        logger.debug(s"Updating event with id ${event.getIdentifier}, serialized payload ${payload.getData.asInstanceOf[Array[Byte]].map(_.toChar).mkString}")

        preparedStatement.setString(1, event.getIdentifier)
        preparedStatement.setString(2, event.getAggregateIdentifier)
        preparedStatement.setLong(3, event.getSequenceNumber)
        preparedStatement.setString(4, event.getType)
        writeTimestamp(preparedStatement, 5, event.getTimestamp)
        preparedStatement.setString(6, payload.getType.getName)
        preparedStatement.setString(7, payload.getType.getRevision)
        preparedStatement.setObject(8, payload.getData)
        preparedStatement.setObject(9, metaData.getData)
        preparedStatement.setString(10, event.getIdentifier)
        preparedStatement.addBatch()
      })

      preparedStatement
      }, (e: SQLException) => handlePersistenceException(e, events.head))
    )
  }
}
