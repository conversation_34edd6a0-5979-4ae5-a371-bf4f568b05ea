import $ivy.`mysql:mysql-connector-java:5.1.46`
import $ivy.`org.scalikejdbc:scalikejdbc_2.12:3.2.3`
import $ivy.`org.axonframework:axon-core:3.2`
import $ivy.`org.quartz-scheduler:quartz:2.3.0`
import $cp.^.target.`scala-2.12`.`platform-core_2.12-1.0.0.jar`
import $ivy.`io.spray:spray-json_2.12:1.3.3`

@

import scalikejdbc._
import java.sql._
import java.io._
import scala.io._
import scala.util.{Try, Success, Failure}
import org.quartz.utils.ConnectionProvider
import org.quartz.JobDataMap
import org.axonframework.eventhandling.GenericEventMessage
import nl.dpes.core.domain.OpgeslagenZoekopdrachtSaga.FrequentietijdVerstreken
import nl.dpes.core.domain.werkzoekende.VerificatieSaga.VerificatiePeriodeVerstreken
import nl.dpes.core.domain.werkzoekende.VerificatieSaga.VerificatieHerinneringGewenst
import spray.json._

implicit val session: AutoSession = AutoSession

val EVENT_KEY = "org.axonframework.eventhandling.EventMessage"

class QuartzConnectionProvider extends ConnectionProvider {
  override def getConnection: Connection = {
    val connection = ConnectionPool.borrow()
    connection.setReadOnly(false)
    connection
  }

  override def shutdown(): Unit = ()

  override def initialize(): Unit = ()
}

val connections = new QuartzConnectionProvider

var succeeded: Int = 0
var invalid: Int = 0
var other: Int = 0

object JsonProtocol extends DefaultJsonProtocol {
  case class EventTrigger(job: String, payloadType: String, payload: String)

  implicit val eventFormat = jsonFormat3(EventTrigger)
  implicit val frequentietijdVerstrekenFormat = jsonFormat2(FrequentietijdVerstreken)
  implicit val verificatieperiodeVerstrekenFormat = jsonFormat1(VerificatiePeriodeVerstreken)
  implicit val verificatieHerinneringGewenstFormat = jsonFormat1(VerificatieHerinneringGewenst)
}

@main
def main(dbHost: String, password: String, file: String): Unit = {
  import JsonProtocol._

  GlobalSettings.loggingSQLAndTime = LoggingSQLAndTimeSettings(enabled = false)
  Class.forName("com.mysql.jdbc.Driver")
  ConnectionPool.singleton(s"****************************************************************************************", "platform_core", password)

  val fixed = Source.fromFile(file).getLines().flatMap { line =>
    val trigger = line.parseJson.convertTo[EventTrigger]

    val maybeEvent = trigger.payloadType match {
      case "nl.dpes.core.domain.OpgeslagenZoekopdrachtSaga$FrequentietijdVerstreken" =>
        Some(
          new GenericEventMessage[FrequentietijdVerstreken](
            trigger.payload.parseJson.convertTo[FrequentietijdVerstreken]
          )
        )
      case "nl.dpes.core.domain.werkzoekende.VerificatieSaga$VerificatiePeriodeVerstreken" =>
        Some(
          new GenericEventMessage[VerificatiePeriodeVerstreken](
            trigger.payload.parseJson.convertTo[VerificatiePeriodeVerstreken]
          )
        )
      case "nl.dpes.core.domain.werkzoekende.VerificatieSaga$VerificatieHerinneringGewenst" =>
        Some(
          new GenericEventMessage[VerificatieHerinneringGewenst](
            trigger.payload.parseJson.convertTo[VerificatieHerinneringGewenst]
          )
        )
      case _ =>
        sys.error(s"Unknown event type: ${trigger.payloadType}")
        None
    }

    maybeEvent.flatMap { event =>
      val jobData = new JobDataMap()
      jobData.put(EVENT_KEY, event)

      Try {
        val os = new ByteArrayOutputStream
        val oos = new ObjectOutputStream(os)
        oos.writeObject(jobData)
        val bytes = os.toByteArray
        os.close()

        bytes
      } match {
        case Success(data) =>
          if (process(trigger.job, data) > 0) {
            succeeded += 1
            Some(trigger)
          } else {
            other += 1
            None
          }
        case Failure(e) =>
          other += 1
          sys.error(e.getMessage)
          None
      }
    }
  }

  val recovered = recoverAll(fixed.toSeq)
  println(s"Fixed $succeeded, Recovered $recovered, Failed $other")
}

def process(name: String, data: scala.Array[Byte]): Int = {
  val in = new ByteArrayInputStream(data)
  val bytesBinder = ParameterBinder(
    value = in,
    binder = (stmt: PreparedStatement, idx: Int) => stmt.setBinaryStream(idx, in, data.length)
  )

  sql"UPDATE QRTZ_JOB_DETAILS SET QRTZ_JOB_DETAILS.JOB_DATA = (${bytesBinder}) WHERE QRTZ_JOB_DETAILS.JOB_NAME = ${name}".update().apply()
}

def recoverAll(triggers: Seq[JsonProtocol.EventTrigger]): Int = {
  val names = triggers.map(_.job)
  sql"UPDATE QRTZ_TRIGGERS SET QRTZ_TRIGGERS.TRIGGER_STATE = 'WAITING' WHERE QRTZ_TRIGGERS.JOB_NAME IN (${names})".update().apply()
}
