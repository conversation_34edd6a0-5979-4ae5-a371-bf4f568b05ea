import $ivy.`io.get-coursier::coursier:1.0.1`

@

import coursier.core.Authentication, coursier.MavenRepository

interp.repositories() ++= Seq(MavenRepository(
  "https://archiva.persgroep.digital/repository/snapshots",
  authentication = Some(Authentication("dpes-dev", sys.env("ARCHIVA_PASSWORD")))
))
interp.repositories() ++= Seq(MavenRepository(
  "https://archiva.persgroep.digital/repository/internal",
  authentication = Some(Authentication("dpes-dev", sys.env("ARCHIVA_PASSWORD")))
))

@

import $cp.^.target.`scala-2.12`.`platform-core_2.12-1.0.0.jar`
import $ivy.`com.lightbend.akka::akka-stream-alpakka-csv:0.18`
import $ivy.`com.typesafe.akka::akka-http-core:10.1.1`
import $ivy.`com.typesafe.akka::akka-http-spray-json:10.1.1`
import $ivy.`com.typesafe.akka::akka-actor:2.5.11`
import $ivy.`com.typesafe.akka::akka-stream:2.5.11`
import $ivy.`com.typesafe.akka::akka-slf4j:2.5.11`
import $ivy.`org.scalikejdbc::scalikejdbc:3.2.3`
import $ivy.`org.scalikejdbc::scalikejdbc-streams:3.2.3`
import $ivy.`ch.qos.logback:logback-classic:1.2.3`
import $ivy.`mysql:mysql-connector-java:5.1.46`
import $ivy.`com.microsoft.sqlserver:mssql-jdbc:6.1.0.jre8`
import $ivy.`nl.dpes::common:0.1.16-SNAPSHOT`
import $ivy.`com.amazonaws:aws-java-sdk-bundle:1.11.313`
import akka.actor.ActorSystem
import akka.stream.alpakka.csv.scaladsl.CsvFormatting
import akka.stream.{ActorMaterializer, IOResult}
import akka.stream.scaladsl.{FileIO, Flow, Keep, Sink, Source}
import ch.qos.logback.classic.{Level, Logger}
import java.nio.file.Paths
import com.amazonaws.auth.DefaultAWSCredentialsProviderChain
import com.amazonaws.services.dynamodbv2.{AmazonDynamoDB, AmazonDynamoDBClientBuilder}
import com.amazonaws.services.dynamodbv2.document.DynamoDB
import nl.dpes.core.domain.Werkzoekende.AccountOpgezegd
import nl.dpes.core.services.SimService
import nl.dpes.core.config.Environment
import nl.dpes.core.projections.core.CoreWerkzoekendeAccountProjections
import nl.dpes.core.services.sim.HttpSimClient
import org.slf4j.LoggerFactory
import scalikejdbc._
import scalikejdbc.streams._

import scala.concurrent.duration.Duration
import scala.concurrent.{Await, ExecutionContextExecutor, Future}

implicit val logger: Logger = LoggerFactory.getLogger("nl.dpes.core").asInstanceOf[Logger]
logger.setLevel(Level.INFO)

// Streams
implicit val system: ActorSystem = ActorSystem("CheckIfClosedAccountsAreRemovedFromSIM")
implicit val materializer: ActorMaterializer = ActorMaterializer()
implicit val ec: ExecutionContextExecutor = system.dispatcher

// Database
implicit val session: AutoSession = AutoSession
def setUpDatabase(mysqlHost: String, mysqlPassword: String): Unit = {
  // Load MySQL driver
  GlobalSettings.loggingSQLAndTime = LoggingSQLAndTimeSettings(enabled = false)
  Class.forName("com.mysql.jdbc.Driver")
  ConnectionPool.singleton(s"*****************************************************************************", "platform_core", mysqlPassword)
}

def setUpSimDatabase(mssqlHost: String, mssqlPassword: String): Unit = {
  Class.forName("com.microsoft.sqlserver.jdbc.SQLServerDriver")
  ConnectionPool.add('sim,s"****************************************************", "tskSimNdp", mssqlPassword)
}

lazy val dynamoDBClient: AmazonDynamoDB = {
  val client = AmazonDynamoDBClientBuilder.standard()
    .withCredentials(new DefaultAWSCredentialsProviderChain)

  client.build()
}

var counter = 0

@main
def main(mysqlHost: String, mysqlPassword: String, simSqlHost: String, simSqlPassword: String, simServiceHost: String): Unit = {
  logger.info("Looking for accounts with a Werkzoekende$AccountOpgezegd that were not deleted in SIM")

  setUpDatabase(mysqlHost, mysqlPassword)
  setUpSimDatabase(simSqlHost, simSqlPassword)

  Await.ready(opgezegdEvents
    .map(item => {
      counter = counter + 1
      item
    })
    .batch(1000, first => List(first)) { (item, list) =>
      item :+ list
    }
    .via(stillInSIM)
    .via(deleteInSim(simServiceHost))
    .map(event => {
      logger.error(s"Closed werkzoekende account still in SIM: ${event.werkzoekendeId}")
      event
    })
    .runWith(writeCsv("deleted_accounts_still_existing_in_sim.csv")), Duration.Inf)

  logger.info(s"Processed $counter items!")
}

lazy val opgezegdEvents: Source[AccountOpgezegd, _] = {
  Source.fromPublisher(DB.readOnlyStream {
    sql"""
      SELECT *
      FROM DomainEventEntry
      WHERE payloadType = 'nl.dpes.core.domain.Werkzoekende$$AccountOpgezegd'
       """
      .map(rs => {
        AccountOpgezegd(rs.string("aggregateIdentifier"), "")
      })
      .iterator
  })
}

lazy val stillInSIM: Flow[List[AccountOpgezegd], AccountOpgezegd, _] = {
  Flow[List[AccountOpgezegd]]
      .mapConcat { eventList =>
        val ids = eventList.map(_.werkzoekendeId)

        NamedDB('sim) readOnly { implicit session =>
          sql"""
            SELECT WERKZOEKENDE_ID, MAIL FROM SimVnu.dbo.NDP_IOL
            WHERE WERKZOEKENDE_ID IN ($ids) AND MAIL IS NOT NULL AND MAIL != ''
            UNION
            SELECT WERKZOEKENDE_ID, MAIL FROM SimVnu.dbo.NDP_NVB
            WHERE WERKZOEKENDE_ID IN ($ids) AND MAIL IS NOT NULL AND MAIL != ''
            UNION
            SELECT WERKZOEKENDE_ID, MAIL FROM SimVnu.dbo.NDP_ITB
            WHERE WERKZOEKENDE_ID IN ($ids) AND MAIL IS NOT NULL AND MAIL != ''
          """.map(rs => AccountOpgezegd(rs.string("WERKZOEKENDE_ID"), "")).list().apply()
      }
  }
}

def deleteInSim(simHost: String): Flow[AccountOpgezegd, AccountOpgezegd, _] = {
  // SIM Service
  val simClient = new HttpSimClient(s"http://$simHost:80/api/v1")
  val projections = new CoreWerkzoekendeAccountProjections(new DynamoDB(dynamoDBClient))(Environment.Acceptance)
  val simService: SimService = new SimService(simClient, projections)

  Flow[AccountOpgezegd]
    .map(event => {
      simService.onAccountOpgezegd(event)
      event
    })
}

def writeCsv(path: String): Sink[AccountOpgezegd, Future[IOResult]] = {
  Flow[AccountOpgezegd]
    .map(event => List(event.werkzoekendeId))
    .via(CsvFormatting.format())
    .toMat(FileIO.toPath(Paths.get(path)))(Keep.right)
}
