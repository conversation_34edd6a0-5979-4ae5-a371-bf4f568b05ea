import scala.sys.process._

lazy val startTestingContainers = taskKey[Unit]("Start testing containers")
lazy val stopTestingContainers = taskKey[Unit]("Stop testing containers")

startTestingContainers := {
  streams.value.log.info(
    "docker compose up -d aws_localstack mysql elasticsearch php" !!
  )
}

stopTestingContainers := {
  streams.value.log.info(
    "docker compose stop aws_localstack mysql elasticsearch" !!
  )
  streams.value.log.info(
    "docker compose rm -fv aws_localstack mysql elasticsearch php" !!
  )
}
