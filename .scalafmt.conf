version = 2.7.5
style = defaultWithAlign

align.openParenCallSite = false
align.openParenDefnSite = false
align.tokens = [{code = "->"}, {code = "<-"}, {code = "=>", owner = "Case"}, {code = "%", owner = "Term.ApplyInfix"}, {code = "%%", owner = "Term.ApplyInfix"}]
align=more
continuationIndent.callSite = 2
continuationIndent.defnSite = 2
danglingParentheses = true
indentOperator = spray
maxColumn = 140
newlines.alwaysBeforeTopLevelStatements = true
project.excludeFilters = [".*\\.sbt", ".*\\.scala\\.html"]
rewrite.redundantBraces.stringInterpolation = true
rewrite.rules = [RedundantParens, RedundantBraces, SortImports]
rewrite.sortModifiers.order = ["implicit", "final", "sealed", "abstract","override", "private", "protected", "lazy"]
spaces.inImportCurlyBraces = false
unindentTopLevelOperators = true

trailingCommas = never