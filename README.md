[Akka]: http://akka.io
[Akka-HTTP]: http://doc.akka.io/docs/akka-http/current/scala.html
[Axon]: http://www.axonframework.org/
[Docker]: https://docker.io
[Java]: http://openjdk.java.net/
[SBT]: https://www.scala-sbt.org
[Scala]: https://www.scala-lang.org
[Swagger]: https://swagger.io

Platform.Core
====

The Core component of the dPES platform.

Table of Contents
====

- [ 1. Getting started ](#GettingStarted)
- [ 1.1. Prerequisites ](#Prerequisites)
- [ 1.2. Installation ](#Installation)


- [ 2. Built with ](#BuiltWith)


- [ 3. Development ](#Development)
- [ 3.1. Using DynamoDb locally ](#UsingDynamoDbLocally)
- [ 3.2. Rebuilding projections ](#RebuildingProjections)


- [ 4. Running tests ](#RunningTests)
- [ 4.1. Automated tests ](#AutomatedTests)
- [ 4.2. Coverage report ](#CoverageReport)
- [ 4.3. Coding style ](#CodingStyle)


- [ 5. Infrastructure ](#Infrastructure)
- [ 5.1. Allocated Ports ](#AllocatedPorts)


- [ 6. Deployment ](#Deployment)


- [ 7. Troubleshooting ](#Troubleshooting)


<a name="GettingStarted"></a>
## Getting started
It is advised to use IntelliJ Ultimate edition with the Scala+SBT plugin installed.


<a name="Prerequisites"></a>
### Prerequisites

You'll need the following pre-installed for development:
- [Java] 8
- [SBT] 0.13+
- [Docker]


<a name="Installation"></a>
### Installation
Clone the project:
```
$ git clone ...
```

For **development**, it is convenient to run the app with the following command:
```
$ sbt ~reStart
```
It will trigger a re-run whenever a code change is detected. Stop the execution by sending the command `reStop`.
It can be difficult to run the project in Intelliji.
_**Note**: this project requires Archiva to be locally configured._
<a name="BuiltWith"></a>
## Built with
- [Scala] 2.12
- [Akka] 2.5 and [Akka-HTTP] 10.1 (API layer)
- [Axon] 3.0
- [Swagger] 2.0 (API docs)


<a name="Development"></a>
## Development
Besides the prerequisites mentioned at the top; setting up your development environment can be done by running:
```
$ docker-compose up -d
```

This will spin up required external services (e.g., MySQL, DynamoDb) and a Swagger UI to inspect the API docs. It is accessible on `http://localhost:5555`.

It might also be necessary to run:
```
$ sbt setupStreams
```
<a name="UsingDynamoDbLocally"></a>
### Using DynamoDb locally
For some tests DynamoDb is needed, this can be started with
```
$ sbt startDynamoDb
```
And stopped:
```
$ sbt stopDynamoDb
```

<a name="RebuildingProjections"></a>
### Rebuilding projections
To rebuild a projection cluster (which is effectively replaying events onto that projection cluster), can be done by running the SBT task `sbt "rebuildProjections {{environment}} {{imageBuildNumber}} {{projectionsVersion}}"`

Once this Kubernetes job has completed successfully you should update the K8s config map for that environment and do a release.

At the moment the job does not finish, the amount of handled events can be found in the index field of the tokenEntry for the indexes replayed.

When the index is up to date, the app can be cedeployed after the indexes have been switched in the configmap.

When replaying the core dynamodb tables, dont forget to temporarily set the capacity of the table AND the indexes
readCapacity: 1-40
writeCapacity: 1-80
Capacity for the indexes can be set by the option 'Use the same read capacity settings for all global secondary indexes'
After replay is done, capacity autoscaling can be turned off.

<a name="RunningTests"></a>
## Running tests


<a name="AutomatedTests"></a>
### Automated tests
```
$ sbt test
```

You can also specify `~test` to re-run the tests on every code change.
Some tests rely on external services. Make sure to start these containers prior to testing:
```
$ sbt startTestingContainers
# Stop them using stopTestingContainers
```

<a name="CoverageReport"></a>
### Coverage report
To generate an HTML report for codecoverage run
```
$ sbt coverageOn test coverageReport
```
The report can be found in `target/scala-2.13/scoverage-report/index.html`


<a name="CodingStyle"></a>
### Coding style
Format source code:
```
$ sbt scalafmtAll
```


<a name="Infrastructure"></a>
## Infrastructure


<a name="AllocatedPorts"></a>
## Allocated Ports

| Description | Listens on Port |
| --- | ---: |
| API  | 80 |
| GRPC  | 10600 |


<a name="Deployment"></a>
## Deployment
Deployment is done by the build server.


<a name="Troubleshooting"></a>
## Troubleshooting
Q: I'm pretty sure my CommandHandlers are configured correctly but I keep getting a `No handler was subscribed to command` exception from Axon.

A: This could be due to MySQL not being available. Make sure to run `docker-compose up -d`

Q: I get the error `ERROR nl.dpes.core  - com.amazonaws.SdkClientException: Unable to execute HTTP request: Connect to localhost:8997 [localhost/127.0.0.1] failed: Connection refused (Connection refused)`

A: Start DynamoDB with
```
$ sbt startDynamoDb
# OR
$ docker-compose up -d
```

Q: I get the error `ERROR nl.dpes.core  - org.axonframework.common.jdbc.JdbcException: Failed to obtain a database connection`

A: This might be because of an incorrect docker setup. Correct with
```
$ docker-compose down
$ docker-compose up -d
```
Q: My tests hang during a test run.

A: This is probably caused by a missing database or incorrect docker setup. Correct with
```
$ docker-compose down
$ docker-compose up -d
```
Q: My local DynamoDB doesn't show tables.

A: You need to specify the region as well before you get the right instance:
```
$ aws dynamodb list-tables --endpoint-url http://localhost:8997 --region us-west-2
```

Q: GDPR tests give this exception:
`io.axoniq.gdpr.api.DataException: GDPR-5001. A key turned out to be invalid. This is unexpected as keys are created within the system. The key database may be corrupted.`

A: You need to install JCE unlimited strength policy to allow for 256-bits encryption.
https://stackoverflow.com/questions/37741142/how-to-install-unlimited-strength-jce-for-java-8-in-os-x

Q: How to update RSA keys?

A: [Updating RSA keys](UPDATING_RSA_KEYS.md)

Q: Kannot listen to Kinesis streams:

A: Make sure streams are created:
```sbt setupStreams```
