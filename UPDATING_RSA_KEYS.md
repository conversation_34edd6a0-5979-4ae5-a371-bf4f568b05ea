### Updating RSA keys

RSA keys are used for signing JSON Web Token (JWT) and in JSON Web Key Set (JWKS) used by Istio.
In order to update the RSA keys (in order to sign users out or for any other reasons) the new RSA
keys need to be generated and placed into Kubernetes (K8s) secrets. This can be done in three steps (one of the ways):

1. Generate private key in PKCS#8 format, extract public key from private key and generate random key ID:
   ```bash
   openssl genrsa -out rsa.pem && openssl rsa -in rsa.pem -pubout -out public.pem && openssl pkcs8 -topk8 -nocrypt -inform PEM -in rsa.pem -outform PEM -out private.pem && rm rsa.pem && cat /dev/urandom | tr -dc 'a-zA-Z0-9' | fold -w 10 | head -n 1 > id.txt
   ```
   As a result of this command three files will be generated:
   * id.txt
   * private.pem
   * public.pem
2. Extract values of files into variables:
   ```bash
   RSA_KEY_ID=`cat id.txt` && RSA_PRIVATE_KEY=`cat private.pem` && RSA_PUBLIC_KEY=`cat public.pem`
   ```
3. Update K8s secrets (`kubectl` must be properly configured) with values from the variables:
   ```bash
   kubectl create secret generic platform-core-rsa-keys --from-literal=rsa-private-key=$RSA_PRIVATE_KEY --from-literal=rsa-public-key=$RSA_PUBLIC_KEY --from-literal=rsa-key-id=$RSA_KEY_ID --dry-run -o yaml | kubectl apply -f -
   ```
After this you can verify that RSA keys are updated and redeploy the application to apply the new secrets.
