include "../../main/resources/application"

kamon {
  enabled = false
  instrumentation {
    jdbc.enabled = false
  }
}

nl.dpes.core {
  env = "testing"

  notifier.events {
    username = "NDP [tst]"
  }

  projections {
      core {
        tokenName = "nl.dpes.core.projections.core"
        tokenName = ${?PROJECTIONS_CORE_TOKENNAME}
        version = 1
        version = ${?PROJECTIONS_CORE_VERSION}
      }

      index {
        tokenName = "nl.dpes.core.projections.index"
        tokenName = ${?PROJECTIONS_INDEX_TOKENNAME}
        version = 2
        version = ${?PROJECTIONS_INDEX_VERSION}
      }
    }
}
