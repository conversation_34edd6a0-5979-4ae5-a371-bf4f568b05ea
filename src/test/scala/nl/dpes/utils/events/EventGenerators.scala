package nl.dpes.utils.events

import cats.data.NonEmptySet
import cats.kernel.Order
import com.fortysevendeg.scalacheck.datetime.jdk8.GenJdk8
import nl.dpes.b2b.domain.Feature.Booster
import nl.dpes.b2b.domain.Feature.Booster.ValuedBooster
import nl.dpes.b2b.domain.PredefinedKey.EmploymentType
import nl.dpes.core.domain.Recruiter.Geregistreerd
import nl.dpes.core.domain.Werkzoekende.{IngeschrevenVoorEMail, WachtwoordGewijzigd, WachtwoordVergeten}
import nl.dpes.core.domain.{EMailadres, Event, _}
import org.scalacheck.{Arbitrary, Gen, ScalacheckShapeless}
import org.scalacheck.ScalacheckShapeless._

import java.time.{LocalDate, LocalDateTime}
import scala.concurrent.duration._
import scala.reflect.ClassTag

trait EventArbitraries extends CommonDomainGenerators {
  implicit def toArb[A](implicit gen: Gen[A]): Arbitrary[A] = Arbitrary(gen)

  lazy val eventGen: Arbitrary[Event] = Arbitrary(
    Gen.oneOf(
      implicitly[Arbitrary[EveryEvent]].arbitrary.map(_.event),
      implicitly[Arbitrary[WachtwoordVergeten]].arbitrary.map(_.copy(customTemplate = null))
    )
  )
}

trait BaseGenerators {

  implicit def email: Gen[EMailadres] =
    for {
      user      <- Gen.stringOfN(8, Gen.alphaChar)
      domain    <- Gen.stringOfN(8, Gen.alphaChar)
      topdomain <- Gen.stringOfN(2, Gen.alphaChar)
    } yield EMailadres(s"$user@$domain.$topdomain")

  def genLocalDate: Gen[LocalDate] = GenJdk8.genZonedDateTime.filter(_.toInstant.getEpochSecond > 0).map(_.toLocalDate)

  def genLocalDateTime: Gen[LocalDateTime] =
    GenJdk8.genZonedDateTime.filter(_.toInstant.getEpochSecond > 0).map(_.toLocalDateTime)
  def genBoolean: Gen[Boolean] = Gen.chooseNum(0, 1).map(_ == 1)

  def genNonEmptySetOf[T](gen: Gen[T])(implicit ord: Order[T], classtag: ClassTag[T]): Gen[NonEmptySet[T]] =
    for {
      list <- Gen.nonEmptyListOf(gen)
    } yield NonEmptySet.of(list.head, list.tail.toArray: _*)
}

trait CommonDomainGenerators extends BaseGenerators {
  import nl.dpes.b2b.common._
  import nl.dpes.b2b.domain._

  implicit def genCompanyType: Gen[CompanyType] =
    Gen.oneOf(CompanyType.DirectEmployer, CompanyType.HumanResourceManager, CompanyType.MediaBureau)
  def genWebshopProduct: Gen[WebshopProduct] = Gen.oneOf(genJobPosting, genJobUpgrade, genProfileView)

  implicit def genJobPosting: Gen[JobPosting] =
    for {
      name       <- Gen.alphaNumStr
      publishOn  <- Gen.listOf(genSite).map(_.toSet)
      available  <- Gen.posNum[Int].map(Duration(_, DAYS))
      features   <- Gen.listOf(genFeature).map(_.toSet)
      predefined <- Gen.mapOf(genPredefinedEntry)
    } yield JobPosting.unsafe(name, publishOn, available, features, predefined)

  implicit def genPredefinedEntry: Gen[(PredefinedKey, String)] =
    for {
      key   <- Gen.const(EmploymentType)
      value <- Gen.alphaNumStr
    } yield (key, value)

  implicit def genJobUpgrade: Gen[JobUpgrade] =
    for {
      name             <- Gen.alphaNumStr
      features         <- Gen.listOf(genFeature).map(_.toSet)
      sites            <- Gen.listOf(genSite).map(_.toSet)
      requiredFeatures <- Gen.listOf(genFeature).map(_.toSet)
      requiredSites    <- Gen.listOf(genSite).map(_.toSet)
    } yield JobUpgrade(name, features, sites, requiredFeatures, requiredSites, Requirements(Set.empty))

  implicit def genSite: Gen[Site] = Gen.oneOf(NVB, IOL, ITB)

  implicit def genFeature: Gen[Feature] = Gen.choose(Int.MinValue, Int.MaxValue).map(ValuedBooster)

  implicit def genProfileView: Gen[ProfileView] =
    for {
      name       <- Gen.alphaNumStr
      creditType <- Gen.oneOf(Consumable, Subscription)
    } yield ProfileView(name, creditType)
}

trait SalesforceGenerators extends CommonDomainGenerators {

  object Salesforce {
    import nl.dpes.b2b.salesforce.domain._

    implicit def genCredit: Gen[Credit] =
      for {
        id           <- Gen.alphaNumStr
        available    <- Gen.posNum[Int]
        product      <- genWebshopProduct
        accessPeriod <- genAccessPeriod
      } yield Credit(id, available, product, accessPeriod)

    implicit def genAccessPeriod: Gen[AccessPeriod] =
      for {
        start     <- genLocalDate
        randomAdd <- Gen.posNum[Int]
        end = start.plusDays(randomAdd)
      } yield AccessPeriod(start, end)

    implicit def genVerifiedRecruiter: Gen[VerifiedRecruiter] =
      for {
        consentedForMarketingContact <- genBoolean
        salesforceId                 <- genSalesforceId
        firstName                    <- Gen.option(Gen.alphaNumStr)
        lastName                     <- Gen.option(Gen.alphaNumStr)
        title                        <- Gen.option(Gen.alphaNumStr)
        phoneNumber                  <- Gen.option(Gen.alphaNumStr)
        emailAddress                 <- Gen.option(Gen.alphaNumStr)
        address                      <- Gen.option(genSalesforceAddress)
        company                      <- genSalesforceCompany
      } yield VerifiedRecruiter(
        salesforceId,
        firstName,
        lastName,
        title,
        phoneNumber,
        emailAddress,
        address,
        company,
        consentedForMarketingContact
      )

    implicit def genSalesforceCompany: Gen[Company] =
      for {
        salesforceId     <- Gen.alphaNumStr
        companyType      <- genCompanyType
        name             <- Gen.alphaNumStr
        alternativeNames <- Gen.listOf(Gen.alphaNumStr).map(_.toSet)
        shippingAddress  <- Gen.option(genSalesforceAddress)
        website          <- Gen.option(Gen.alphaNumStr)
        defaultLogo      <- Gen.option(genCompanyLogo)
      } yield Company(Some(salesforceId), companyType, name, alternativeNames, shippingAddress, website, defaultLogo)

    implicit def genCompanyLogo: Gen[CompanyLogo] =
      for {
        key <- Gen.alphaNumStr
      } yield CompanyLogo(key)

    implicit def genSalesforceAddress: Gen[Address] =
      for {
        streetNameAndHouseNumber <- Gen.alphaNumStr
        city                     <- Gen.alphaNumStr
        zipCode                  <- Gen.alphaNumStr
      } yield Address(streetNameAndHouseNumber, city, zipCode)

    implicit def genSalesforceId: Gen[SalesForceId] = Gen.oneOf(genCheckedSalesforceId, genUncheckedSalesforceId)

    implicit def genCheckedSalesforceId: Gen[SalesForceId] =
      for {
        id <- Gen.listOfN(SalesForceId.checkedLength, Gen.alphaChar).map(_.mkString) suchThat (SalesForceId(_).isRight)
      } yield SalesForceId(id).right.get

    implicit def genUncheckedSalesforceId: Gen[SalesForceId] =
      for {
        id <- Gen.listOfN(SalesForceId.uncheckedLength, Gen.alphaChar).map(_.mkString) suchThat (SalesForceId(_).isRight)
      } yield SalesForceId(id).right.get
  }
}

sealed trait EveryEvent {
  def event: Event
}

object EveryEvent {
  case class Wrapper_Geregistreerd(event: Geregistreerd)                                               extends EveryEvent
  case class Wrapper_Zoekopdracht_VerwijderdDoorRecruiter(event: Zoekopdracht.VerwijderdDoorRecruiter) extends EveryEvent
  case class Wrapper_Zoekopdracht_Gewijzigd(event: Zoekopdracht.Gewijzigd)                             extends EveryEvent

  case class Wrapper_OpgeslagenZoekopdrachtSaga_FrequentietijdVerstreken(event: OpgeslagenZoekopdrachtSaga.FrequentietijdVerstreken)
      extends EveryEvent
  case class Wrapper_Zoekopdracht_OpgeslagenDoorRecruiter(event: Zoekopdracht.OpgeslagenDoorRecruiter)         extends EveryEvent
  case class Wrapper_Recruiter_EMailadresGewijzigd(event: Recruiter.EMailadresGewijzigd)                       extends EveryEvent
  case class Wrapper_Recruiter_Verwijderd(event: Recruiter.Verwijderd)                                         extends EveryEvent
  case class Wrapper_Recruiter_FavorietGemaakt(event: Recruiter.FavorietGemaakt)                               extends EveryEvent
  case class Wrapper_Recruiter_Geimporteerd(event: Recruiter.Geimporteerd)                                     extends EveryEvent
  case class Wrapper_Recruiter_FavorietVerwijderd(event: Recruiter.FavorietVerwijderd)                         extends EveryEvent
  case class Wrapper_Werkzoekende_EMailadresWijzigingVerzocht(event: Werkzoekende.EMailadresWijzigingVerzocht) extends EveryEvent
  case class Wrapper_Werkzoekende_OpzeggingVerzocht(event: Werkzoekende.OpzeggingVerzocht)                     extends EveryEvent
  case class Wrapper_Werkzoekende_AccountOpgezegd(event: Werkzoekende.AccountOpgezegd)                         extends EveryEvent
  case class Wrapper_Werkzoekende_WachtwoordOpnieuwIngesteld(event: Werkzoekende.WachtwoordOpnieuwIngesteld)   extends EveryEvent
  case class Wrapper_Werkzoekende_Geregistreerd(event: Werkzoekende.Geregistreerd)                             extends EveryEvent
  case class Wrapper_Werkzoekende_Geverifieerd(event: Werkzoekende.Geverifieerd)                               extends EveryEvent
  case class Wrapper_Werkzoekende_EMailadresGewijzigd(event: Werkzoekende.EMailadresGewijzigd)                 extends EveryEvent
  case class Wrapper_Werkzoekende_WachtwoordVergeten(event: Werkzoekende.WachtwoordVergeten)                   extends EveryEvent
  case class Wrapper_Werkzoekende_AccountGeimporteerd(event: Werkzoekende.AccountGeimporteerd)                 extends EveryEvent
  case class Wrapper_Werkzoekende_WachtwoordIngesteld(event: Werkzoekende.WachtwoordIngesteld)                 extends EveryEvent
  case class Wrapper_WachtwoordGewijzigd(event: WachtwoordGewijzigd)                                           extends EveryEvent
  case class Wrapper_Zoekopdracht_AangemaaktDoorRecruiter(event: Zoekopdracht.AangemaaktDoorRecruiter)         extends EveryEvent
  case class Wrapper_IngeschrevenVoorEMail(event: IngeschrevenVoorEMail)                                       extends EveryEvent
}
