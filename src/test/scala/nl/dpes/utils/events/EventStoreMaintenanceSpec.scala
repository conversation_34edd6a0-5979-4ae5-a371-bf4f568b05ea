package nl.dpes.utils.events

import nl.dpes.core.repositories.TokenRepository
import nl.dpes.utils.events.EventStoreMaintenance.{Task, TaskResult}
import org.axonframework.common.jdbc.ConnectionProvider
import org.mockito.ArgumentMatchers.any
import org.mockito.Mockito._
import org.scalatest.BeforeAndAfter
import org.scalatest.flatspec.AnyFlatSpec
import org.scalatest.matchers.should.Matchers
import org.scalatestplus.mockito.MockitoSugar
import org.slf4j.Logger

class EventStoreMaintenanceSpec extends AnyFlatSpec with BeforeAndAfter with MockitoSugar with Matchers {
  private var service: EventStoreMaintenance = _
  private val connectionProvider             = mock[ConnectionProvider]
  private val tokenRepository                = mock[TokenRepository]
  private implicit val logger: Logger        = mock[Logger]

  before {
    reset(connectionProvider)
    reset(tokenRepository)

    service = new EventStoreMaintenance(connectionProvider, tokenRepository)

    when(tokenRepository.claim(any[TokenRepository.Token])) thenReturn true
  }

  it should "execute all given tasks in order" in {
    val tasks = Seq(
      new Task[Int] {
        override def execute(connectionProvider: ConnectionProvider)(implicit logger: Logger): Either[Throwable, TaskResult[Int]] =
          Right(TaskResult(1))
      },
      new Task[Int] {
        override def execute(connectionProvider: ConnectionProvider)(implicit logger: Logger): Either[Throwable, TaskResult[Int]] =
          Right(TaskResult(2))
      },
      new Task[Int] {
        override def execute(connectionProvider: ConnectionProvider)(implicit logger: Logger): Either[Throwable, TaskResult[Int]] =
          Right(TaskResult(3))
      }
    )

    val results = service.execute(tasks).right.get.results

    results.head shouldEqual TaskResult(1)
    results(1) shouldEqual TaskResult(2)
    results(2) shouldEqual TaskResult(3)
  }

  it should "execute all given tasks and return the first occurring throwable" in {
    val e = new RuntimeException("foo")

    val tasks = Seq(
      new Task[Unit] {
        override def execute(connectionProvider: ConnectionProvider)(implicit logger: Logger): Either[Throwable, TaskResult[Unit]] =
          Right(TaskResult())
      },
      new Task[Unit] {
        override def execute(connectionProvider: ConnectionProvider)(implicit logger: Logger): Either[Throwable, TaskResult[Unit]] =
          Right(TaskResult())
      },
      new Task[Unit] {
        override def execute(connectionProvider: ConnectionProvider)(implicit logger: Logger): Either[Throwable, TaskResult[Unit]] =
          Left(e)
      },
      new Task[Unit] {
        override def execute(connectionProvider: ConnectionProvider)(implicit logger: Logger): Either[Throwable, TaskResult[Unit]] =
          Right(TaskResult())
      }
    )

    service.execute(tasks) shouldEqual Left(e)
  }
}
