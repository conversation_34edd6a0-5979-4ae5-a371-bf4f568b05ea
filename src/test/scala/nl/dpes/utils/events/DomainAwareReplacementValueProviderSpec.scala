package nl.dpes.utils.events

import com.thoughtworks.xstream.XStream
import io.axoniq.gdpr.api.{dataSubjectId, personalData, FieldEncrypter}
import io.axoniq.gdpr.cryptoengine.InMemoryCryptoEngine
import nl.dpes.core.services.IdentifierService
import org.axonframework.serialization.xml.{CompactDriver, XStreamSerializer}
import org.mockito.Mockito.{reset, when}
import org.scalatest.BeforeAndAfterEach
import org.scalatest.flatspec.AnyFlatSpec
import org.scalatest.matchers.should.Matchers
import org.scalatestplus.mockito.MockitoSugar

class DomainAwareReplacementValueProviderSpec extends AnyFlatSpec with MockitoSugar with Matchers with BeforeAndAfterEach {
  private val identifierService        = mock[IdentifierService]
  private val replacementValueProvider = new DomainAwareReplacementValueProvider(identifierService)

  override def beforeEach(): Unit =
    reset(identifierService)

  behavior of "replacing a value for which the decryption key was deleted"

  it should "provide a random email address when asked for" in {
    case class Event(@dataSubjectId id: String, @personalData eMailadres: String)

    when(identifierService.generateUUID()).thenReturn("random-uuid")

    replacementValueProvider.replacementValue(
      classOf[Event],
      classOf[Event].getDeclaredField("eMailadres"),
      classOf[String],
      "",
      "*randomEmailAddress*",
      "".getBytes
    ) shouldBe "<EMAIL>"
  }

  it should "call the super class for other values of replacement" in {
    case class Event(@dataSubjectId id: String, @personalData name: String)

    when(identifierService.generateUUID()).thenReturn("random-uuid")

    replacementValueProvider.replacementValue(
      classOf[Event],
      classOf[Event].getDeclaredField("name"),
      classOf[String],
      "",
      "John Doe",
      "".getBytes
    ) shouldBe "John Doe"
  }

  it should "integrate well with the encrypter" in {
    case class Event(@dataSubjectId id: String, @personalData(replacement = "*randomEmailAddress*") eMail: String)

    val xstream = new XStream(new CompactDriver)
    xstream.allowTypesByWildcard(Array("nl.dpes.**", "scala.**", "cats.**", "ch.qos.logback.**"))
    xstream.ignoreUnknownElements()

    val serializer: XStreamSerializer = XStreamSerializer.builder().xStream(xstream).build()

    val cryptoEngine   = new InMemoryCryptoEngine
    val fieldEncrypter = new FieldEncrypter(cryptoEngine, serializer, replacementValueProvider)

    when(identifierService.generateUUID()).thenReturn("random-uuid")

    val event = Event("uuid", "<EMAIL>")
    fieldEncrypter.encrypt(event)
    cryptoEngine.deleteKey("uuid")
    fieldEncrypter.decrypt(event)

    event.eMail shouldBe "<EMAIL>"
  }
}
