package nl.dpes.utils.events.upcasting.werkzoekende

import com.thoughtworks.xstream.XStream

import java.time.Instant
import java.util.stream.StreamSupport
import nl.dpes.core.domain.Werkzoekende._
import org.axonframework.eventhandling.DomainEventData
import org.axonframework.messaging.MetaData
import org.axonframework.serialization.SimpleSerializedObject
import org.axonframework.serialization.upcasting.event.{InitialEventRepresentation, IntermediateEventRepresentation}
import org.axonframework.serialization.xml.{CompactDriver, XStreamSerializer}
import org.scalatest.flatspec.AnyFlatSpec
import org.scalatest.matchers.should.Matchers

import scala.xml.XML
import scala.collection.JavaConverters._

class WerkzoekendeEMailadresUpcasterSpec extends AnyFlatSpec with Matchers {
  val xstream = new XStream(new CompactDriver)
  xstream.allowTypesByWildcard(Array("nl.dpes.**", "scala.**", "cats.**", "ch.qos.logback.**"))
  xstream.ignoreUnknownElements()

  private val serializer: XStreamSerializer = XStreamSerializer.builder().xStream(xstream).build()
  private val upcaster                      = new WerkzoekendeEMailadresUpcaster

  private def eventData(payload: String, payloadType: Class[_]): DomainEventData[String] = new DomainEventData[String] {
    override def getSequenceNumber: Long        = 1L
    override def getType: String                = payloadType.getTypeName
    override def getAggregateIdentifier: String = "3d3c2368-3816-40cf-92d7-a14252ad4057"
    override def getEventIdentifier: String     = "42d4c536-0873-4444-b6d9-9ba5fb553a0f"

    override def getPayload = new SimpleSerializedObject[String](
      payload,
      classOf[String],
      payloadType.getTypeName,
      null
    )

    override def getTimestamp: Instant = Instant.now

    override def getMetaData = new SimpleSerializedObject[String](
      "<meta-data><entry><string>traceId</string><string>42d4c536-0873-4444-b6d9-9ba5fb553a0f</string></entry><entry><string>correlationId</string><string>42d4c536-0873-4444-b6d9-9ba5fb553a0f</string></entry></meta-data>",
      classOf[String],
      classOf[MetaData].getTypeName,
      null
    )
  }

  private def intermediateEvent(payload: String, payloadType: Class[_]) =
    new InitialEventRepresentation(eventData(payload, payloadType), serializer)

  it should "upcast a Werkzoekende.AccountGeimporteerd event from before the email address change from revision NULL to revision 1.1" in {
    val initialEventRepresentations = Stream[IntermediateEventRepresentation](
      intermediateEvent(
        "<nl.dpes.core.domain.Werkzoekende_-AccountGeimporteerd><werkzoekendeId>3c54d13a-79d4-11e6-bf4d-0242ac11000e</werkzoekendeId><sanDiegoId>7194050</sanDiegoId><eMailadres><value><EMAIL></value></eMailadres><site class=\"scala.Enumeration$Val\" in=\"nl.dpes.core.domain.Sites\" name=\"nationalevacaturebank.nl\"/><wachtwoord><hash>$2a$10$ntfcdMjvxaW13nHaWM80ReQfndSkOrhhpQEvPWiZB2fNACHeKlXt2</hash><salt>$2a$10$ntfcdMjvxaW13nHaWM80Re</salt></wachtwoord></nl.dpes.core.domain.Werkzoekende_-AccountGeimporteerd>",
        classOf[AccountGeimporteerd]
      )
    )

    val upcastedEventRepresentations = upcaster.upcast(
      StreamSupport.stream(initialEventRepresentations.asJava.spliterator(), false)
    )

    val upcastedEvent = upcastedEventRepresentations.findFirst.get
    upcastedEvent.getData.getType.getRevision shouldBe "1.1"
    upcastedEvent.getData.getType.getName shouldBe "nl.dpes.core.domain.Werkzoekende$AccountGeimporteerd"

    val xml          = XML.loadString(upcastedEvent.getData(classOf[String]).getData)
    val emailAddress = xml \\ "eMailadres"
    emailAddress.length shouldBe 1
    emailAddress.toString shouldBe "<eMailadres><EMAIL></eMailadres>"
  }

  it should "upcast a Werkzoekende.AccountGeimporteerd event from after the email address change from revision NULL to revision 1.1" in {
    val initialEventRepresentations = Stream[IntermediateEventRepresentation](
      intermediateEvent(
        "<nl.dpes.core.domain.Werkzoekende_-AccountGeimporteerd><werkzoekendeId>3c54d13a-79d4-11e6-bf4d-0242ac11000e</werkzoekendeId><sanDiegoId>7194050</sanDiegoId><eMailadres><EMAIL></eMailadres><site class=\"scala.Enumeration$Val\" in=\"nl.dpes.core.domain.Sites\" name=\"nationalevacaturebank.nl\"/><wachtwoord><hash>$2a$10$ntfcdMjvxaW13nHaWM80ReQfndSkOrhhpQEvPWiZB2fNACHeKlXt2</hash><salt>$2a$10$ntfcdMjvxaW13nHaWM80Re</salt></wachtwoord></nl.dpes.core.domain.Werkzoekende_-AccountGeimporteerd>",
        classOf[AccountGeimporteerd]
      )
    )

    val upcastedEventRepresentations = upcaster.upcast(
      StreamSupport.stream(initialEventRepresentations.asJava.spliterator(), false)
    )

    val upcastedEvent = upcastedEventRepresentations.findFirst.get
    upcastedEvent.getData.getType.getRevision shouldBe "1.1"
    upcastedEvent.getData.getType.getName shouldBe "nl.dpes.core.domain.Werkzoekende$AccountGeimporteerd"

    val xml          = XML.loadString(upcastedEvent.getData(classOf[String]).getData)
    val emailAddress = xml \\ "eMailadres"
    emailAddress.length shouldBe 1
    emailAddress.toString shouldBe "<eMailadres><EMAIL></eMailadres>"
  }

  it should "upcast a Werkzoekende.Geregistreerd event from revision NULL to revision 1.1" in {
    val initialEventRepresentations = Stream[IntermediateEventRepresentation](
      intermediateEvent(
        "<nl.dpes.core.domain.Werkzoekende_-Geregistreerd><werkzoekendeId>f0a9082e-982e-4fb3-a5a9-d1757cf00608</werkzoekendeId><eMailadres><value><EMAIL></value></eMailadres><site class=\"scala.Enumeration$Val\" in=\"nl.dpes.core.domain.Sites\" name=\"nationalevacaturebank.nl\"/><verificatieUrl><value class=\"akka.http.scaladsl.model.Uri$$anon$1\"><scheme>http</scheme><authority><host class=\"akka.http.scaladsl.model.Uri$NamedHost\"><address>www.nationalevacaturebank.nl</address></host><port>0</port><userinfo></userinfo></authority><path class=\"akka.http.scaladsl.model.Uri$Path$Empty$\"/><rawQueryString class=\"scala.None$\"/><fragment class=\"scala.None$\" reference=\"../rawQueryString\"/></value></verificatieUrl><verificatieToken>eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.********************************************************************************************************************************************************************************************************.-VPTq3Y4aniEYxx7i615XOhHikBc4_Adhz-7l9RTDgk</verificatieToken></nl.dpes.core.domain.Werkzoekende_-Geregistreerd>",
        classOf[Geregistreerd]
      )
    )

    val upcastedEventRepresentations = upcaster.upcast(
      StreamSupport.stream(initialEventRepresentations.asJava.spliterator(), false)
    )

    val upcastedEvent = upcastedEventRepresentations.findFirst.get
    upcastedEvent.getData.getType.getRevision shouldBe "1.1"
    upcastedEvent.getData.getType.getName shouldBe "nl.dpes.core.domain.Werkzoekende$Geregistreerd"

    val xml          = XML.loadString(upcastedEvent.getData(classOf[String]).getData)
    val emailAddress = xml \\ "eMailadres"
    emailAddress.length shouldBe 1
    emailAddress.toString shouldBe "<eMailadres><EMAIL></eMailadres>"
  }

  it should "upcast a Werkzoekende.WachtwoordVergeten event from revision NULL to revision 1.1" in {
    val initialEventRepresentations = Stream[IntermediateEventRepresentation](
      intermediateEvent(
        "<nl.dpes.core.domain.Werkzoekende_-WachtwoordVergeten><werkzoekendeId>b216a494-3932-11e6-836c-0242ac11000c</werkzoekendeId><eMailadres><value><EMAIL></value></eMailadres><site class=\"scala.Enumeration$Val\" in=\"nl.dpes.core.domain.Sites\" name=\"intermediair.nl\"/><herstelUrl><value class=\"akka.http.scaladsl.model.Uri$$anon$1\"><scheme>https</scheme><authority><host class=\"akka.http.scaladsl.model.Uri$NamedHost\"><address>www.intermediair.nl</address></host><port>0</port><userinfo></userinfo></authority><path class=\"akka.http.scaladsl.model.Uri$Path$Slash\"><tail class=\"akka.http.scaladsl.model.Uri$Path$Segment\"><head>account</head><tail class=\"akka.http.scaladsl.model.Uri$Path$Slash\"><tail class=\"akka.http.scaladsl.model.Uri$Path$Segment\"><head>nieuw-wachtwoord</head><tail class=\"akka.http.scaladsl.model.Uri$Path$Empty$\"/></tail></tail></tail></path><rawQueryString class=\"scala.Some\"><value class=\"string\">emailAddress=hugo.drost%40gmail.com</value></rawQueryString><fragment class=\"scala.None$\"/></value></herstelUrl><herstelToken>eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.**************************************************************************************************************************************************************************************************************************************************************************************.zTJ94Z9aU9Mh4K99Wo5mFPDdDf4s8ec1fWQs_cfBfh8</herstelToken><herstelTokenId>c0c1f1e0-faa6-4053-89e5-8667e19b341b</herstelTokenId></nl.dpes.core.domain.Werkzoekende_-WachtwoordVergeten>",
        classOf[WachtwoordVergeten]
      )
    )

    val upcastedEventRepresentations = upcaster.upcast(
      StreamSupport.stream(initialEventRepresentations.asJava.spliterator(), false)
    )

    val upcastedEvent = upcastedEventRepresentations.findFirst.get
    upcastedEvent.getData.getType.getRevision shouldBe "1.1"
    upcastedEvent.getData.getType.getName shouldBe "nl.dpes.core.domain.Werkzoekende$WachtwoordVergeten"

    val xml          = XML.loadString(upcastedEvent.getData(classOf[String]).getData)
    val emailAddress = xml \\ "eMailadres"
    emailAddress.length shouldBe 1
    emailAddress.toString shouldBe "<eMailadres><EMAIL></eMailadres>"
  }

  it should "upcast a Werkzoekende.EMailadresWijzigingVerzocht event from revision NULL to revision 1.1" in {
    val initialEventRepresentations = Stream[IntermediateEventRepresentation](
      intermediateEvent(
        "<nl.dpes.core.domain.Werkzoekende_-EMailadresWijzigingVerzocht><werkzoekendeId>b216a494-3932-11e6-836c-0242ac11000c</werkzoekendeId><site class=\"scala.Enumeration$Val\" in=\"nl.dpes.core.domain.Sites\" name=\"intermediair.nl\"/><nieuwEMailadres><value><EMAIL></value></nieuwEMailadres><verificatieUrl>https://www.intermediair.nl/jouw-pagina/account/verifieer-email-wijziging</verificatieUrl><verificatieToken>eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************************************************************.4hDrMIuZ1urzTxo9A3IpsoCOlTxt3BzSOERVj1XCIF8</verificatieToken><verificatieTokenId>89c3a585-dad1-43ba-98f7-41d0591e92a3</verificatieTokenId></nl.dpes.core.domain.Werkzoekende_-EMailadresWijzigingVerzocht>",
        classOf[EMailadresWijzigingVerzocht]
      )
    )

    val upcastedEventRepresentations = upcaster.upcast(
      StreamSupport.stream(initialEventRepresentations.asJava.spliterator(), false)
    )

    val upcastedEvent = upcastedEventRepresentations.findFirst.get
    upcastedEvent.getData.getType.getRevision shouldBe "1.1"
    upcastedEvent.getData.getType.getName shouldBe "nl.dpes.core.domain.Werkzoekende$EMailadresWijzigingVerzocht"

    val xml          = XML.loadString(upcastedEvent.getData(classOf[String]).getData)
    val emailAddress = xml \\ "nieuwEMailadres"
    emailAddress.length shouldBe 1
    emailAddress.toString shouldBe "<nieuwEMailadres><EMAIL></nieuwEMailadres>"
  }

  it should "upcast a Werkzoekende.EMailadresGewijzigd event from revision NULL to revision 1.1" in {
    val initialEventRepresentations = Stream[IntermediateEventRepresentation](
      intermediateEvent(
        "<nl.dpes.core.domain.Werkzoekende_-EMailadresGewijzigd><werkzoekendeId>dec61dcc-bec0-11e5-a17a-0242ac11081f</werkzoekendeId><nieuwEMailadres><value><EMAIL></value></nieuwEMailadres></nl.dpes.core.domain.Werkzoekende_-EMailadresGewijzigd>",
        classOf[EMailadresGewijzigd]
      )
    )

    val upcastedEventRepresentations = upcaster.upcast(
      StreamSupport.stream(initialEventRepresentations.asJava.spliterator(), false)
    )

    val upcastedEvent = upcastedEventRepresentations.findFirst.get
    upcastedEvent.getData.getType.getRevision shouldBe "1.1"
    upcastedEvent.getData.getType.getName shouldBe "nl.dpes.core.domain.Werkzoekende$EMailadresGewijzigd"

    val xml          = XML.loadString(upcastedEvent.getData(classOf[String]).getData)
    val emailAddress = xml \\ "nieuwEMailadres"
    emailAddress.length shouldBe 1
    emailAddress.toString shouldBe "<nieuwEMailadres><EMAIL></nieuwEMailadres>"
  }
}
