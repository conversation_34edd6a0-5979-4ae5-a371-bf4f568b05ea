package nl.dpes.utils.events.upcasting.zoekopdracht

import com.thoughtworks.xstream.XStream
import nl.dpes.axon4s.serialization.xml.EnumConverter
import nl.dpes.core.domain.{Fre<PERSON><PERSON>, Zoekparameters, Zoektermen}
import nl.dpes.core.domain.Zoekopdracht.OpgeslagenDoorRecruiter
import nl.dpes.utils.events.upcasting.converter.ListConverter
import nl.dpes.utils.events.upcasting.zoekopdracht.SerializationHelper.ListSerializeEnd
import org.axonframework.eventhandling.DomainEventData
import org.axonframework.messaging.MetaData
import org.axonframework.serialization.upcasting.event.{InitialEventRepresentation, IntermediateEventRepresentation}
import org.axonframework.serialization.xml.{CompactDriver, XStreamSerializer}
import org.axonframework.serialization.{SerializedObject, SimpleSerializedObject}
import org.scalatest.flatspec.AnyFlatSpec
import org.scalatest.matchers.should.Matchers

import java.time.Instant
import java.util.stream.StreamSupport
import scala.collection.JavaConverters._

class OpgeslagenDoorRecruiterUpcasterSpec extends AnyFlatSpec with Matchers {
  val xstream: XStream = new XStream(new CompactDriver)
  xstream.alias("scala.collection.immutable.List$SerializationProxy", classOf[SerializationHelper[_]])
  xstream.alias("scala.collection.immutable.ListSerializeEnd$", ListSerializeEnd.getClass)
  xstream.allowTypesByWildcard(Array("nl.dpes.**", "scala.**", "cats.**", "ch.qos.logback.**"))
  xstream.registerConverter(new EnumConverter)
  xstream.registerConverter(new ListConverter(xstream.getMapper))
  xstream.ignoreUnknownElements()

  private val serializer: XStreamSerializer = XStreamSerializer.builder().xStream(xstream).build()
  private val upcaster                      = new OpgeslagenDoorRecruiterUpcaster(serializer)

  private val opgeslagenDoorRecruiterData: DomainEventData[String] = new DomainEventData[String] {
    override def getSequenceNumber: Long = 1L

    override def getType: String = classOf[OpgeslagenDoorRecruiter].getTypeName

    override def getAggregateIdentifier: String = "3d3c2368-3816-40cf-92d7-a14252ad4057"

    override def getEventIdentifier: String = "opgeslagen-door-recruiter-id"

    override def getPayload = new SimpleSerializedObject[String](
      "<nl.dpes.core.domain.Zoekopdracht_-OpgeslagenDoorRecruiter><zoekopdrachtId>1650cd65-5104-410b-bf4d-8f980124b5e0</zoekopdrachtId><recruiterId>3d7c3aa5-c8fc-4b68-aad8-cc3d17004d4e</recruiterId><naam>My saved search</naam><frequentie class=\"scala.Enumeration$Val\" in=\"nl.dpes.core.domain.Frequenties\" name=\"Nooit\"/><zoekparameters><zoektermen class=\"scala.Some\"><value class=\"nl.dpes.core.domain.Zoektermen\"><alles class=\"scala.Some\"><value class=\"list\"><string></string></value></alles><opleidingNaam class=\"scala.Some\"><value class=\"scala.collection.immutable.Nil$\" resolves-to=\"scala.collection.immutable.List$SerializationProxy\" serialization=\"custom\"><scala.collection.immutable.List_-SerializationProxy><default/><scala.collection.immutable.ListSerializeEnd_-/></scala.collection.immutable.List_-SerializationProxy></value></opleidingNaam><opleidingBeschrijving class=\"scala.Some\"><value class=\"scala.collection.immutable.Nil$\" reference=\"../../opleidingNaam/value\"/></opleidingBeschrijving><gewensteBaan class=\"scala.Some\"><value class=\"scala.collection.immutable.Nil$\" reference=\"../../opleidingNaam/value\"/></gewensteBaan><functieTitel class=\"scala.Some\"><value class=\"scala.collection.immutable.Nil$\" reference=\"../../opleidingNaam/value\"/></functieTitel><functieBeschrijving class=\"scala.Some\"><value class=\"scala.collection.immutable.Nil$\" reference=\"../../opleidingNaam/value\"/></functieBeschrijving><cursussen class=\"scala.Some\"><value class=\"scala.collection.immutable.Nil$\" reference=\"../../opleidingNaam/value\"/></cursussen></value></zoektermen><locatie class=\"scala.Some\"><value class=\"string\">Amsterdam</value></locatie><wijzigingsdatum class=\"scala.Some\"><value class=\"string\">Afgelopen 6 maanden</value></wijzigingsdatum><opleidingsniveaus class=\"scala.Some\"><value class=\"list\"><string>HBO</string></value></opleidingsniveaus><aantallenUren class=\"scala.Some\"><value class=\"scala.collection.immutable.Nil$\" reference=\"../../zoektermen/value/opleidingNaam/value\"/></aantallenUren><soortenWerk class=\"scala.Some\"><value class=\"scala.collection.immutable.Nil$\" reference=\"../../zoektermen/value/opleidingNaam/value\"/></soortenWerk><beschikbaarheden class=\"scala.Some\"><value class=\"scala.collection.immutable.Nil$\" reference=\"../../zoektermen/value/opleidingNaam/value\"/></beschikbaarheden><rijbewijzen class=\"scala.Some\"><value class=\"scala.collection.immutable.Nil$\" reference=\"../../zoektermen/value/opleidingNaam/value\"/></rijbewijzen><talen class=\"scala.Some\"><value class=\"scala.collection.immutable.Nil$\" reference=\"../../zoektermen/value/opleidingNaam/value\"/></talen><afstandTotWerklocatie class=\"scala.None$\"/><carriereniveau class=\"scala.Some\"><value class=\"scala.collection.immutable.Nil$\" reference=\"../../zoektermen/value/opleidingNaam/value\"/></carriereniveau><functiegroep class=\"scala.Some\"><value class=\"scala.collection.immutable.Nil$\" reference=\"../../zoektermen/value/opleidingNaam/value\"/></functiegroep></zoekparameters></nl.dpes.core.domain.Zoekopdracht_-OpgeslagenDoorRecruiter>",
      classOf[String],
      classOf[OpgeslagenDoorRecruiter].getTypeName,
      null
    )

    override def getTimestamp: Instant = Instant.now

    override def getMetaData = new SimpleSerializedObject[String](
      "<meta-data><entry><string>traceId</string><string>42d4c536-0873-4444-b6d9-9ba5fb553a0f</string></entry><entry><string>correlationId</string><string>42d4c536-0873-4444-b6d9-9ba5fb553a0f</string></entry></meta-data>",
      classOf[String],
      classOf[MetaData].getTypeName,
      null
    )
  }

  private val intermediateOpgeslagenDoorRecruiter = new InitialEventRepresentation(opgeslagenDoorRecruiterData, serializer)

  it should "upcast a Zoekopdracht.OpgeslagenDoorRecruiter event from revision NULL to revision 1.1" in {
    val initialEventRepresentations = Stream[IntermediateEventRepresentation](
      intermediateOpgeslagenDoorRecruiter
    )

    val upcastedEventRepresentations = upcaster.upcast(
      StreamSupport.stream(initialEventRepresentations.toIterable.asJava.spliterator(), false)
    )

    val event = upcastedEventRepresentations.findFirst.get.getData
    event.getType.getRevision shouldBe "1.1"
    serializer.deserialize[String, OpgeslagenDoorRecruiter](event.asInstanceOf[SerializedObject[String]]) shouldBe
    OpgeslagenDoorRecruiter(
      "1650cd65-5104-410b-bf4d-8f980124b5e0",
      "3d7c3aa5-c8fc-4b68-aad8-cc3d17004d4e",
      "My saved search",
      Frequenties.Nooit,
      Zoekparameters(
        Some(
          Zoektermen(
            None,
            Some(Nil),
            Some(Nil),
            Some(Nil),
            Some(Nil),
            Some(Nil),
            Some(Nil)
          )
        ),
        Some("Amsterdam"),
        Some("Afgelopen 6 maanden"),
        Some(List("HBO")),
        Some(Nil),
        Some(Nil),
        Some(Nil),
        Some(Nil),
        Some(Nil),
        None,
        Some(Nil),
        Some(Nil)
      )
    )
  }
}
