package nl.dpes.utils.events.upcasting.werkzoekende

import com.thoughtworks.xstream.XStream

import java.time.Instant
import java.util.stream.StreamSupport
import nl.dpes.core.domain.Werkzoekende.{Geregistreerd, WachtwoordVergeten}
import org.axonframework.eventhandling.DomainEventData
import org.axonframework.messaging.MetaData
import org.axonframework.serialization.SimpleSerializedObject
import org.axonframework.serialization.upcasting.event.{InitialEventRepresentation, IntermediateEventRepresentation}
import org.axonframework.serialization.xml.{CompactDriver, XStreamSerializer}
import org.scalatest.flatspec.AnyFlatSpec
import org.scalatest.matchers.should.Matchers

import scala.collection.JavaConverters._
import scala.xml.XML

class UrlUpcasterSpec extends AnyFlatSpec with Matchers {
  val xstream = new XStream(new CompactDriver)
  xstream.allowTypesByWildcard(Array("nl.dpes.**", "scala.**", "cats.**", "ch.qos.logback.**"))
  xstream.ignoreUnknownElements()

  private val serializer: XStreamSerializer = XStreamSerializer.builder().xStream(xstream).build()

  private val upcaster = new UrlUpcaster

  private def eventData(payload: String, payloadType: Class[_]): DomainEventData[String] = new DomainEventData[String] {
    override def getSequenceNumber: Long        = 1L
    override def getType: String                = payloadType.getTypeName
    override def getAggregateIdentifier: String = "3d3c2368-3816-40cf-92d7-a14252ad4057"
    override def getEventIdentifier: String     = "42d4c536-0873-4444-b6d9-9ba5fb553a0f"

    override def getPayload = new SimpleSerializedObject[String](
      payload,
      classOf[String],
      payloadType.getTypeName,
      "1.1"
    )

    override def getTimestamp: Instant = Instant.now

    override def getMetaData = new SimpleSerializedObject[String](
      "<meta-data><entry><string>traceId</string><string>42d4c536-0873-4444-b6d9-9ba5fb553a0f</string></entry><entry><string>correlationId</string><string>42d4c536-0873-4444-b6d9-9ba5fb553a0f</string></entry></meta-data>",
      classOf[String],
      classOf[MetaData].getTypeName,
      null
    )
  }

  private def intermediateEvent(payload: String, payloadType: Class[_]) =
    new InitialEventRepresentation(eventData(payload, payloadType), serializer)

  it should "upcast a Werkzoekende.Geregistreerd event from revision 1.1 to revision 1.2" in {
    val initialEventRepresentations = Stream[IntermediateEventRepresentation](
      intermediateEvent(
        "<nl.dpes.core.domain.Werkzoekende_-Geregistreerd><werkzoekendeId>0c901dc0-ef0c-4546-8701-4aea06a98edd</werkzoekendeId><eMailadres>CAEV+ay/6hog//H47gO3zw/NJBICqB3JxUmG1TyMlKOmdF1NwUu/hoYhnB3qyj161tIpxgw7jGqUkcA=</eMailadres><site class=\"scala.Enumeration$Val\" in=\"nl.dpes.core.domain.Sites\" name=\"intermediair.nl\"/><verificatieUrl><value class=\"akka.http.scaladsl.model.Uri$$anon$1\"><scheme>https</scheme><authority><host class=\"akka.http.scaladsl.model.Uri$NamedHost\"><address>www.intermediair.nl</address></host><port>0</port><userinfo></userinfo></authority><path class=\"akka.http.scaladsl.model.Uri$Path$Slash\"><tail class=\"akka.http.scaladsl.model.Uri$Path$Segment\"><head>account</head><tail class=\"akka.http.scaladsl.model.Uri$Path$Slash\"><tail class=\"akka.http.scaladsl.model.Uri$Path$Segment\"><head>verificatie</head><tail class=\"akka.http.scaladsl.model.Uri$Path$Empty$\"/></tail></tail></tail></path><rawQueryString class=\"scala.None$\"/><fragment class=\"scala.None$\" reference=\"../rawQueryString\"/></value></verificatieUrl><verificatieToken>eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.********************************************************************************************************************************************************************************************************.T4nDcNN2Is_HCBJA0VgZEX2mO1WaUH24ILXsV7GGkAU</verificatieToken></nl.dpes.core.domain.Werkzoekende_-Geregistreerd>",
        classOf[Geregistreerd]
      )
    )

    val upcastedEventRepresentations = upcaster.upcast(
      StreamSupport.stream(initialEventRepresentations.asJava.spliterator(), false)
    )

    val upcastedEvent = upcastedEventRepresentations.findFirst.get
    upcastedEvent.getData.getType.getRevision shouldBe "1.2"
    upcastedEvent.getData.getType.getName shouldBe "nl.dpes.core.domain.Werkzoekende$Geregistreerd"

    val xml            = XML.loadString(upcastedEvent.getData(classOf[String]).getData)
    val verificatieUrl = xml \\ "verificatieUrl"
    verificatieUrl.length shouldBe 1
    verificatieUrl.toString shouldBe "<verificatieUrl>https://www.intermediair.nl/account/verificatie</verificatieUrl>"
  }

  it should "upcast a Werkzoekende.WachtwoordVergeten event from revision 1.1 to revision 1.2" in {
    val initialEventRepresentations = Stream[IntermediateEventRepresentation](
      intermediateEvent(
        "<?xml version=\"1.0\" encoding=\"UTF-8\"?><nl.dpes.core.domain.Werkzoekende_-WachtwoordVergeten><werkzoekendeId>4f44b62a-bdff-11e5-bd1d-0242ac11081f</werkzoekendeId><eMailadres><value>CAEVcIHVrhog4qGJQr4RbnloTlacTUMHFSh6TuPXIjq0q2l7CrNK12shqQORDI/3o9EpNS2v2lMERjo=</value></eMailadres><site class=\"scala.Enumeration$Val\" in=\"nl.dpes.core.domain.Sites\" name=\"nationalevacaturebank.nl\"/><herstelUrl><value class=\"akka.http.scaladsl.model.Uri$$anon$1\"><scheme>https</scheme><authority><host class=\"akka.http.scaladsl.model.Uri$NamedHost\"><address>www.nationalevacaturebank.nl</address></host><port>0</port><userinfo></userinfo></authority><path class=\"akka.http.scaladsl.model.Uri$Path$Slash\"><tail class=\"akka.http.scaladsl.model.Uri$Path$Segment\"><head>account</head><tail class=\"akka.http.scaladsl.model.Uri$Path$Slash\"><tail class=\"akka.http.scaladsl.model.Uri$Path$Segment\"><head>nieuw-wachtwoord</head><tail class=\"akka.http.scaladsl.model.Uri$Path$Empty$\"/></tail></tail></tail></path><rawQueryString class=\"scala.Some\"><value class=\"string\">emailAddress=test%40gmail.com</value></rawQueryString><fragment class=\"scala.None$\"/></value></herstelUrl><herstelToken>eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.**************************************************************************************************************************************************************************************************************************************************************************************.Dq7vUFbvtcNOmfZWZ2_vrhVCnqANzcvMfnv7OGN_79o</herstelToken><herstelTokenId>94eda8f1-32ac-4bb4-ad38-72a6c7db0c8f</herstelTokenId><customTemplate class=\"scala.None$\" reference=\"../herstelUrl/value/fragment\"/></nl.dpes.core.domain.Werkzoekende_-WachtwoordVergeten>",
        classOf[WachtwoordVergeten]
      )
    )

    val upcastedEventRepresentations = upcaster.upcast(
      StreamSupport.stream(initialEventRepresentations.asJava.spliterator(), false)
    )

    val upcastedEvent = upcastedEventRepresentations.findFirst.get
    upcastedEvent.getData.getType.getRevision shouldBe "1.2"
    upcastedEvent.getData.getType.getName shouldBe "nl.dpes.core.domain.Werkzoekende$WachtwoordVergeten"

    val xml        = XML.loadString(upcastedEvent.getData(classOf[String]).getData)
    val herstelUrl = xml \\ "herstelUrl"
    herstelUrl.length shouldBe 1
    herstelUrl.toString shouldBe "<herstelUrl>https://www.nationalevacaturebank.nl/account/nieuw-wachtwoord?emailAddress=test%40gmail.com</herstelUrl>"
  }
}
