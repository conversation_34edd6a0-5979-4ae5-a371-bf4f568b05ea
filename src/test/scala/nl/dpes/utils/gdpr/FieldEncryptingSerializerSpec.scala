package nl.dpes.utils.gdpr

import com.thoughtworks.xstream.XStream
import io.axoniq.gdpr.api._
import io.axoniq.gdpr.cryptoengine.{CryptoEngine, InMemoryCryptoEngine, KeyType}
import nl.dpes.core.domain.EMailadres
import nl.dpes.core.services.IdentifierService
import nl.dpes.utils.events.DomainAwareReplacementValueProvider
import org.axonframework.serialization.xml.{CompactDriver, XStreamSerializer}
import org.mockito.Mockito.{reset, when}
import org.scalatest.BeforeAndAfter
import org.scalatest.flatspec.AnyFlatSpec
import org.scalatest.matchers.should.Matchers
import org.scalatestplus.mockito.MockitoSugar

object FieldEncryptingSerializerSpec {
  case class Event(@dataSubjectId id: String, @personalData name: String)

  case class EventWithEMailadres(@dataSubjectId id: String, @deepPersonalData eMailadres: EMailadres)
}

class FieldEncryptingSerializerSpec extends AnyFlatSpec with BeforeAndAfter with Matchers with MockitoSugar {
  import FieldEncryptingSerializerSpec._

  val xstream = new XStream(new CompactDriver)
  xstream.allowTypesByWildcard(Array("nl.dpes.**", "scala.**", "cats.**", "ch.qos.logback.**"))
  xstream.ignoreUnknownElements()
  val xstreamSerializer: XStreamSerializer = XStreamSerializer.builder().xStream(xstream).build()

  var identifierService: IdentifierService = mock[IdentifierService]
  var cryptoEngine: CryptoEngine           = new InMemoryCryptoEngine
  cryptoEngine.setKeyType(KeyType.AES_256)
  val fieldEncrypter = new FieldEncrypter(cryptoEngine, xstreamSerializer, new DomainAwareReplacementValueProvider(identifierService))

  val serializer = new FieldEncryptingSerializer(fieldEncrypter, xstreamSerializer)

  before {
    reset(identifierService)
  }

  behavior of "FieldEncrypterSerializer"

  it should "encrypt annotated fields when serializing" in {
    val event = Event("uuid", "John Doe")

    serializer.serialize[String](event, classOf[String]).getData should not include "John Doe"
  }

  it should "decrypt encrypted fields when deserializing" in {
    val event            = Event("uuid", "John Doe")
    val serializedObject = serializer.serialize[String](event, classOf[String])

    serializer.deserialize[String, Event](serializedObject) shouldBe event
  }

  it should "show a default value when deserializing if the decryption key has been deleted" in {
    val event            = Event("uuid", "John Doe")
    val serializedObject = serializer.serialize[String](event, classOf[String])

    cryptoEngine.deleteKey(event.id)

    serializer.deserialize[String, Event](serializedObject) shouldBe Event("uuid", "")
  }

  it should "be able to encrypt the EMailadres type" in {
    val event = EventWithEMailadres("uuid", "<EMAIL>")

    serializer.serialize[String](event, classOf[String]).getData should not include "<EMAIL>"
  }

  it should "create a valid EMailadres when deserializing if the decryption key has been deleted" in {
    val event            = EventWithEMailadres("uuid", "<EMAIL>")
    val serializedObject = serializer.serialize[String](event, classOf[String])

    when(identifierService.generateUUID()).thenReturn("random-uuid")

    cryptoEngine.deleteKey("uuid")

    serializer.deserialize[String, Event](serializedObject) shouldBe EventWithEMailadres(
      "uuid",
      EMailadres("<EMAIL>")
    )
  }
}
