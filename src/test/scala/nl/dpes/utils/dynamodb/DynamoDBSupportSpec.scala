package nl.dpes.utils.dynamodb

import com.amazonaws.services.dynamodbv2.document.{DynamoDB, Table}
import com.amazonaws.services.dynamodbv2.model.{AmazonDynamoDBException, CreateTableRequest, ResourceNotFoundException, TableDescription}
import nl.dpes.core.config.Configuration
import nl.dpes.testutils.{DynamoDBSupport => DynamoDBUtils}
import nl.dpes.utils.dynamodb.DynamoDBSupport.Identity
import org.mockito.ArgumentMatchers._
import org.mockito.Mockito._
import org.scalatest.flatspec.AnyFlatSpec
import org.scalatest.matchers.should.Matchers
import org.scalatest.{BeforeAndAfterAll, BeforeAndAfterEach}
import org.scalatestplus.mockito.MockitoSugar
import spray.json.{DefaultJsonProtocol, RootJsonFormat}

class DynamoDBSupportSpec
    extends AnyFlatSpec
    with MockitoSugar
    with Matchers
    with BeforeAndAfterAll
    with BeforeAndAfterEach
    with Configuration
    with DynamoDBUtils
    with DefaultJsonProtocol {
  private var db: DynamoDB = _

  implicit val supportedItemFormat: RootJsonFormat[SupportedItem] = jsonFormat3(SupportedItem)

  override def beforeEach(): Unit = super.afterEach(Seq("my-table"))
  override def afterAll(): Unit   = super.afterAll(Seq("my-table"))

  it should "create a table if it doesn't exist" in {
    val table       = mock[Table]
    val db          = mock[DynamoDB]
    val dBSupported = new DynamoDBSupported(db)

    when(table.describe()).thenThrow(new ResourceNotFoundException(""))
    when(db.getTable("my-table")).thenReturn(table)
    when(db.createTable(any[CreateTableRequest])).thenReturn(table)

    dBSupported.table

    verify(db, times(1)).createTable(any[CreateTableRequest])
  }

  it should "not create table if it already exists" in {
    val table       = mock[Table]
    val db          = mock[DynamoDB]
    val dBSupported = new DynamoDBSupported(db)

    when(table.describe()).thenReturn(mock[TableDescription])
    when(db.getTable("my-table")).thenReturn(table)

    dBSupported.table

    verify(db, never).createTable(any[CreateTableRequest])
  }

  it should "fail if table creation failed" in {
    val table       = mock[Table]
    val db          = mock[DynamoDB]
    val dBSupported = new DynamoDBSupported(db)

    when(db.getTable(any[String])).thenReturn(table)
    when(table.describe()).thenThrow(new ResourceNotFoundException(""))
    when(db.createTable(any[CreateTableRequest])).thenThrow(new AmazonDynamoDBException(""))

    assertThrows[AmazonDynamoDBException](dBSupported.table)
  }

  it should "create table with proper primary index" in {
    db = new DynamoDB(dynamoDbClient)
    val dBSupported = new DynamoDBSupported(db)

    val item = SupportedItem(123, "bar value", "baz value")

    dBSupported.insert(Identity("foo", item.foo), item, Identity("bar", item.bar), Identity("baz", item.baz))

    dBSupported.findOnPrimaryIndex(Identity("foo", item.foo)) should be(Some(item))
  }

  it should "create table with proper secondary index" in {
    db = new DynamoDB(dynamoDbClient)
    val dBSupported = new DynamoDBSupported(db)

    val item = SupportedItem(123, "some string", "baz index")

    val description = dBSupported.table.describe()
    description.getGlobalSecondaryIndexes.size() should be(1)
    description.getGlobalSecondaryIndexes.get(0).getKeySchema.get(0).getAttributeName should be("bar")

    dBSupported.insert(Identity("foo", item.foo), item, Identity("bar", item.bar), Identity("baz", item.baz))

    dBSupported.findOnPrimaryIndex(Identity("foo", item.foo)) should be(Some(item))
    dBSupported.findOnSecondaryIndex("bar-index", Identity("bar", item.bar), Some(Identity("baz", item.baz))) should be(List(item))
  }

  it should "delete an item by its primary index" in {
    db = new DynamoDB(dynamoDbClient)
    val dBSupported = new DynamoDBSupported(db)

    val item = SupportedItem(123, "bar value", "baz value")

    dBSupported.insert(Identity("foo", item.foo), item)
    dBSupported.delete(Identity("foo", item.foo))

    dBSupported.findOnPrimaryIndex(Identity("foo", item.foo)) should be(None)
  }
}

sealed case class SupportedItem(foo: Int, bar: String, baz: String)

sealed class DynamoDBSupported(override protected val db: DynamoDB) extends DynamoDBSupport with DefaultJsonProtocol {
  import DynamoDBSupport._
  implicit val supportedItemFormat: RootJsonFormat[SupportedItem] = jsonFormat3(SupportedItem)

  override protected val primaryIndex = PrimaryIndex(HashField("foo", DataTypes.Number))

  override protected val secondaryIndexes = List(
    SecondaryIndex("bar-index", HashField("bar", DataTypes.String), Some(SortField("baz", DataTypes.String)))
  )

  implicit lazy val table: Table = super.table("my-table")

  def findOnPrimaryIndex(id: Identity): Option[SupportedItem] = super.findOnPrimaryIndex[SupportedItem](id)

  def findOnSecondaryIndex(indexName: String, partitionKeyId: Identity, sortKeyId: Option[Identity]): List[SupportedItem] =
    super.findOnSecondaryIndex[SupportedItem](indexName, partitionKeyId, sortKeyId)

  def insert(id: Identity, document: SupportedItem, secondaryId: Identity*): Unit =
    super.insert[SupportedItem](id, document, secondaryId: _*)
  def delete(id: Identity): Unit = super.delete(id)
}
