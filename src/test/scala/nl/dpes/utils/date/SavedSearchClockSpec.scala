package nl.dpes.utils.date

import nl.dpes.core.domain.Frequenties
import org.mockito.ArgumentMatchers.anyInt
import org.mockito.Mockito.{reset, times, verify, when}
import org.scalatest.BeforeAndAfter
import org.scalatest.flatspec.AnyFlatSpec
import org.scalatestplus.mockito.MockitoSugar

import java.time.{Clock, ZoneId, ZonedDateTime}
import scala.util.Random

class SavedSearchClockSpec extends AnyFlatSpec with BeforeAndAfter with MockitoSugar {

  private val currentTime = ZonedDateTime
    .of(2018, 1, 1, 15, 0, 0, 0, ZoneId.of("Europe/Amsterdam")) // Monday
  private var clock: Clock = Clock.fixed(currentTime.toInstant, ZoneId.of("Europe/Amsterdam"))
  private val random       = mock[Random]
  val savedSearchClock     = new SavedSearchClock(clock, random)

  before {
    reset(random)

    when(random.nextInt(anyInt())).thenReturn(74) // 74 + 1 = 75 minutes
  }

  private val recruiterId = "recruiter-id"

  behavior of "getFirstScheduleTimeForRecruiter"

  it should "calculate the first scheduled time as day after 4:15 am when frequentie is <PERSON><PERSON>i<PERSON><PERSON>" in {
    val frequentie = Frequenties.Dagelijks
    val expectedTime = ZonedDateTime
      .of(2018, 1, 2, 5, 15, 0, 0, ZoneId.of("Europe/Amsterdam"))
      .toInstant

    assert(savedSearchClock.getNextScheduleTimeForRecruiter(recruiterId, frequentie).contains(expectedTime))
  }

  it should "calculate the first scheduled time as the next day of the week that matches the creation day when frequentie is Wekelijks" in {
    val frequentie = Frequenties.Wekelijks
    val creationDate = ZonedDateTime
      .of(2017, 1, 5, 5, 15, 0, 0, ZoneId.of("Europe/Amsterdam")) // Thursday
      .toInstant
    val expectedTime = ZonedDateTime
      .of(2018, 1, 4, 5, 15, 0, 0, ZoneId.of("Europe/Amsterdam"))
      .toInstant

    assert(savedSearchClock.getFirstScheduleTimeForRecruiter(recruiterId, frequentie, creationDate).contains(expectedTime))
  }

  behavior of "getNextScheduleTimeForRecruiter"

  it should "set the random seed to recruiterId" in {
    savedSearchClock.getNextScheduleTimeForRecruiter(recruiterId, Frequenties.Dagelijks)

    verify(random, times(1)).setSeed(recruiterId.hashCode.toLong)
  }

  it should "calculate the scheduled time as day after 4:15 am when frequentie is Dagelijks" in {

    val frequentie = Frequenties.Dagelijks
    val expectedTime = ZonedDateTime
      .of(2018, 1, 2, 5, 15, 0, 0, ZoneId.of("Europe/Amsterdam"))
      .toInstant

    assert(savedSearchClock.getNextScheduleTimeForRecruiter(recruiterId, frequentie).contains(expectedTime))
  }

  it should "calculate the scheduled time as 7 days later 4:15 am when frequentie is Weekly" in {

    val frequentie = Frequenties.Wekelijks
    val expectedTime = ZonedDateTime
      .of(2018, 1, 8, 5, 15, 0, 0, ZoneId.of("Europe/Amsterdam"))
      .toInstant

    assert(savedSearchClock.getNextScheduleTimeForRecruiter(recruiterId, frequentie).contains(expectedTime))
  }

  it should "return None when the frequentie is nooit" in {
    val frequentie = Frequenties.Nooit

    assert(savedSearchClock.getNextScheduleTimeForRecruiter(recruiterId, frequentie).isEmpty)
  }
}
