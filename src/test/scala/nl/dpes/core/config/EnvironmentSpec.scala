package nl.dpes.core.config

import nl.dpes.core.config.Environment._
import org.scalatest.flatspec.AnyFlatSpec
import org.scalatest.matchers.should.Matchers
import org.scalatest.prop.TableDrivenPropertyChecks

class EnvironmentSpec extends AnyFlatSpec with Matchers with TableDrivenPropertyChecks {

  behavior of "Environment"

  it should "be created by a valid name" in {
    val validEnvironmentNamesForEnvironments = Table(
      ("name", "environment"),
      ("development", Development),
      ("testing", Testing),
      ("acceptance", Acceptance),
      ("production", Production)
    )

    forAll(validEnvironmentNamesForEnvironments) { (name: String, environment: Environment) =>
      Environment(name) should be(environment)
    }
  }

  it should "throw an exception when created with an invalid name" in {
    an[Environment.Unknown] should be thrownBy Environment("unknown environment name")
  }
}
