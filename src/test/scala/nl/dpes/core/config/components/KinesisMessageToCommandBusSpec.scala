package nl.dpes.core.config.components

import akka.actor.ActorSystem
import akka.stream.ActorMaterializer
import akka.stream.scaladsl.Keep
import akka.stream.testkit.scaladsl.{TestSink, TestSource}
import akka.stream.testkit.{TestPublisher, TestSubscriber}
import akka.testkit.TestKit
import nl.dpes.core.domain.Recruiter.{<PERSON><PERSON><PERSON>, WijzigEMailadres}
import nl.dpes.core.domain.Sites
import nl.dpes.core.projections.core.CoreRecruiterAccountProjections
import nl.dpes.core.projections.core.CoreRecruiterAccountProjections.CoreRecruiterAccount
import nl.dpes.core.services.eventSubscriber.KinesisMessageToCommandBus
import nl.dpes.protocol.v1.Envelope
import nl.dpes.protocol.v1.Envelope.Payload
import nl.dpes.salesforce.protocol.v1.{ContactCreated => PbContactCreated, ContactEmailAddressChanged => PbContactEmailAddressChanged}
import org.axonframework.commandhandling.gateway.CommandGateway
import org.mockito.ArgumentMatchers._
import org.mockito.Mockito._
import org.scalatest.BeforeAndAfter
import org.scalatest.flatspec.AnyFlatSpecLike
import org.scalatest.matchers.should.Matchers
import org.scalatestplus.mockito.MockitoSugar

import scala.concurrent.ExecutionContext

class KinesisMessageToCommandBusSpec
    extends TestKit(ActorSystem("KinesisMessageToCommandBusSpec"))
    with AnyFlatSpecLike
    with Matchers
    with MockitoSugar
    with BeforeAndAfter {

  val commandGateway: CommandGateway                                   = mock[CommandGateway]
  val coreRecruiterAccountProjections: CoreRecruiterAccountProjections = mock[CoreRecruiterAccountProjections]

  private val graph = KinesisMessageToCommandBus(commandGateway, coreRecruiterAccountProjections)

  implicit val materializer: ActorMaterializer    = ActorMaterializer()
  implicit val executionContext: ExecutionContext = system.dispatcher

  private def createStream: (TestPublisher.Probe[Envelope], TestSubscriber.Probe[Unit]) = TestSource
    .probe[Envelope]
    .via(graph)
    .toMat(TestSink.probe[Unit])(Keep.both)
    .run()

  before {
    reset(commandGateway, coreRecruiterAccountProjections)
  }

  it should "send a Registreer command when receiving a message of type ContactCreated" in {
    when(coreRecruiterAccountProjections.findByRecruiterId(any[String])).thenReturn(None)
    val (pub, sub) = createStream

    val contactCreated        = PbContactCreated("id", "<EMAIL>")
    val contactCreatedPayload = Payload.ContactCreated(contactCreated)
    val record                = Envelope("", None, "core", "", None, contactCreatedPayload)

    sub.request(1)
    pub.sendNext(record)
    sub.expectNext(())

    verify(commandGateway).sendAndWait(Registreer("id", "<EMAIL>", Sites.Ndp))
  }

  it should "not send a Registreer command recruiter already registered" in {
    when(coreRecruiterAccountProjections.findByRecruiterId(any[String])).thenReturn(Some(mock[CoreRecruiterAccount]))
    val (pub, sub) = createStream

    val contactCreated        = PbContactCreated("id", "<EMAIL>")
    val contactCreatedPayload = Payload.ContactCreated(contactCreated)
    val record                = Envelope("", None, "core", "", None, contactCreatedPayload)

    sub.request(1)
    pub.sendNext(record)
    sub.expectNext(())

    verify(commandGateway, never()).sendAndWait(Registreer("id", "<EMAIL>", Sites.Ndp))
  }

  it should "send a WijzigEMailadres command when receiving a message of type ContactEmailAddressChanged" in {
    when(coreRecruiterAccountProjections.findByRecruiterId(any[String])).thenReturn(Some(mock[CoreRecruiterAccount]))
    val (pub, sub) = createStream

    val emailAddressChanged               = PbContactEmailAddressChanged("id", "<EMAIL>")
    val contactEmailAddressChangedPayload = Payload.ContactEmailAddressChanged(emailAddressChanged)
    val record                            = Envelope("", None, "core", "", None, contactEmailAddressChangedPayload)

    sub.request(1)
    pub.sendNext(record)
    sub.expectNext(())

    verify(commandGateway).sendAndWait(WijzigEMailadres("id", "<EMAIL>"))
  }
  it should "not send a WijzigEMailadres command when recruiter not yet registered" in {
    when(coreRecruiterAccountProjections.findByRecruiterId(any[String])).thenReturn(None)
    val (pub, sub) = createStream

    val emailAddressChanged               = PbContactEmailAddressChanged("id", "<EMAIL>")
    val contactEmailAddressChangedPayload = Payload.ContactEmailAddressChanged(emailAddressChanged)
    val record                            = Envelope("", None, "core", "", None, contactEmailAddressChangedPayload)

    sub.request(1)
    pub.sendNext(record)
    sub.expectNext(())

    verify(commandGateway, never()).sendAndWait(WijzigEMailadres("id", "<EMAIL>"))
  }
}
