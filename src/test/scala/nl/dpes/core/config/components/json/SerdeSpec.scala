package nl.dpes.core.config.components.json

import com.softwaremill.diffx.scalatest.DiffMatcher
import com.thoughtworks.xstream.XStream
import nl.dpes.core.domain.Event
import nl.dpes.utils.events.EventArbitraries
import org.axonframework.serialization.xml.{CompactDriver, XStreamSerializer}
import org.axonframework.serialization.{SerializedObject, Serializer}
import org.scalacheck.Prop.forAll
import org.scalacheck.Test.Parameters
import org.scalacheck.util.Pretty
import org.scalacheck.{Arbitrary, Prop, Shrink}
import org.scalatest.flatspec.AnyFlatSpec
import org.scalatest.BeforeAndAfter
import org.scalatest.matchers.should.Matchers
import org.scalatestplus.mockito.MockitoSugar
import org.scalatestplus.scalacheck.Checkers

class SerdeSpec extends AnyFlatSpec with BeforeAndAfter with Matchers with MockitoSugar with Checkers with DiffMatcher {
  val TEST_SIZE        = 500                                      // this is a balanced size, the time grows not linearly as you increase the test size
  val PARALLEL_WORKERS = Runtime.getRuntime.availableProcessors() // should be as close as possible to the number of available cores
  trait Serializers extends EventArbitraries {
    val xstream = new XStream(new CompactDriver)
    xstream.allowTypesByWildcard(Array("nl.dpes.**", "scala.**", "cats.**", "ch.qos.logback.**"))
    xstream.ignoreUnknownElements()
    val xmlSerializer  = XStreamSerializer.builder().xStream(xstream).build()
    val jsonSerializer = new SpraySerializer()

    def checkSerde[A <: Event, P](serializer: Serializer)(a1: Arbitrary[A])(implicit s1: Shrink[A]): Prop =
      forAll { event: A =>
        var serialized: Option[SerializedObject[_]] = None
        serialized = Option(serializer.serialize(event, classOf[String]))
        val deserialized: A = serializer.deserialize(serialized.get)
        val eq              = event.hashCode() == deserialized.hashCode()
        Prop(eq)
      }(identity, a1, s1, customPretty)

    def customPretty[A](a: A): Pretty = a match {
      case t: Throwable => Pretty.prettyThrowable(t)
      case any          => Pretty.prettyAny(any)
    }
  }
  behavior of "Json Serde"
  it should "yield the same object on the roundtrip" in new Serializers {
    def partial[A <: Event]: Arbitrary[A] => Prop = checkSerde(jsonSerializer)
    check(
      partial(eventGen),
      Parameters.default
        .withMinSize(TEST_SIZE)
        .withMaxSize(TEST_SIZE)
        .withWorkers(PARALLEL_WORKERS)
    )
  }
  behavior of "XML Serde"
  it should "yield the same object on the roundtrip" in new Serializers {
    def partial[A <: Event]: Arbitrary[A] => Prop = checkSerde(xmlSerializer)
    check(
      partial(eventGen),
      Parameters.default
        .withMinSize(TEST_SIZE)
        .withMaxSize(TEST_SIZE)
        .withWorkers(PARALLEL_WORKERS)
    )
  }
}
