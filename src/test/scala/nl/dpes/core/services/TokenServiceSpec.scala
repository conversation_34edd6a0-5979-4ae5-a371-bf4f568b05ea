package nl.dpes.core.services

import com.auth0.jwt.exceptions.TokenExpiredException
import nl.dpes.core.services.security.tokens._
import nl.dpes.core.services.security.{JWTProvider, RsaKeys}
import org.mockito.Mockito._
import org.scalatest.BeforeAndAfter
import org.scalatest.flatspec.AnyFlatSpec
import org.scalatest.matchers.should.Matchers
import org.scalatestplus.mockito.MockitoSugar

import scala.concurrent.duration._

class TokenServiceSpec extends AnyFlatSpec with Matchers with MockitoSugar with BeforeAndAfter {
  import TokenService._

  private val rsaKeys = RsaKeys
    .fromStrings(
      "MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQDaYxeZR0jlmUbJ\nBLqhduKr3lDe2jTMaGNFCbHonw2K1s02iNOarA0+43xqYgdlJOEOy4eaF2E19sBy\nxZjaElKpPbwHRaz6qmC76kBo1ohk8M35ml6gRsF1lRvjak2PK1PIkivIp7eyu+hP\nu8+GMxwLa5WNhI+Ij3/islVn+LIQy2rpt6dQc/83wGURxT3q/ZQ2kxaRqYPhQe6J\ndm+lrDgSPhbO5aKtO1/ZhzkT/77uojtuDvfR4OBnl3uN0sJ5u77MzBm1twzkwDwC\nwTlzz8PASMbHITFHtYF/zHk7qBet0VhB8U4nxodl1J23fOo6/A6HrZTmpSR6Zivn\nyWf8hZd5AgMBAAECggEAWnieuCMt8eAT0Qcige2MbgMFoazoHp5IMbuYdnQd2hyu\nmx8H+pW4TwtS6Yd+5MnoaoaqwI1a+k4k3+c4MW9Ry0cF9EVLKfUXagHbeRMXIioY\nSoVHGkL1tk51ZTsOsncSmI7lkJTapzLtRMrbaA8YWSrSo/P8umgoZLPYaeA/dHDs\nmgKgJtR3tQRoLKG39wyBh+QTwo/f8LJuJ3BBldk7a1EvX+nSuO7aU2XVBcOVu8zB\nCejE3EnLcNFQU+HcGPWMDJPk1D/vTp08zUeLjRkouyJPTzWjLGIh7FbOR3fzVCbx\nzHYXyercurrA5rhMqVDfcLKwAWSiFfJTIfQis816YQKBgQD83lNGg1GNcBiAn/5A\nPV91F9VoKG5d4SwzXA44VVHEqn8BS4GWMtgmeia2mLvWZwHfNOHcYNq77xJavGrQ\nqoyFwFcojyHrYbJBK7osyxNnmZipXveMPlZXmExPeGiYXRHfrnvkbg31JtbxC+rJ\n4V+D5tKtQlggVPLOrVGUSdkI3QKBgQDdF3Mkf3h9RPerju7e0aa3Jq8WnYwQcKVd\nstMwX4HyX0+k+d2VdPXfV1eHe5SDUttIwWQIqumX/YUPkgDFhcXaRCVFjwMUD5Sr\nqxqA1qFDKUfTKq0QVuxVT7kXFyoy637mqCp8rYhOmwYWtvzQIYNDsBdgXwdXD3Bz\nKGiopTBRTQKBgE/+FOHtkNLpbxbhhhDUWWo1S/FTOPbB0LW+nVD8zI0mUl8af7gA\nBZjGc5C36FP+6fyuQfTxPsDM9EV37WJtUV7OZcQS42wOz9EGADNDWrQfRsftnuni\nnnW/XASX1p68cncMP20X05qF2uEOIAwVG54iWussXax8swLmmTQL2HiRAoGAKzqA\niuxZlLS9cE/cKLj5TepEZbmcP0KnMrDaViT2dXtpSSqJxsU9pdOgC5+AJEU5Fvqp\nIoC4d71cHPVBLV3Al7KnTvhtc6iwOviTkAlnMoiJwKIhgIZeYzfZSsh+ucIN5++N\njx8m/aeWAy74M4G59qwhhfhOqRkdtYZ7IC5cSikCgYEAiwxx/IOMJFpyZQakK2Nh\nICsZL1l43FB5ZCba6vPA/cVBkLo+pvkb5w/3yyDhKYSD3jf1T8SibT073ONGCPb1\n2EDjy/Vzf7oGQTprWeJBeNt7Ufg2A1YTNdxe+NSY9WZdeebzylcbc1Tc+rDpxBX5\nWWloe6U3Pd5rBpwTZAlLlrM=",
      "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA2mMXmUdI5ZlGyQS6oXbi\nq95Q3to0zGhjRQmx6J8NitbNNojTmqwNPuN8amIHZSThDsuHmhdhNfbAcsWY2hJS\nqT28B0Ws+qpgu+pAaNaIZPDN+ZpeoEbBdZUb42pNjytTyJIryKe3srvoT7vPhjMc\nC2uVjYSPiI9/4rJVZ/iyEMtq6benUHP/N8BlEcU96v2UNpMWkamD4UHuiXZvpaw4\nEj4WzuWirTtf2Yc5E/++7qI7bg730eDgZ5d7jdLCebu+zMwZtbcM5MA8AsE5c8/D\nwEjGxyExR7WBf8x5O6gXrdFYQfFOJ8aHZdSdt3zqOvwOh62U5qUkemYr58ln/IWX\neQIDAQAB"
    )
    .right
    .get
  private val rsaKeyId = "123456"
  private val jwt      = new JWTProvider("issuer", rsaKeys, rsaKeyId)
  private val service  = new TokenService(jwt)

  behavior of "Token extraction"

  it should "be able to extract an AccessToken definition from a token" in {
    val userId     = "user-id"
    val role       = "role"
    val sanDiegoId = Some(2)

    val accessToken = jwt.createAndSignJWTToken(
      userId,
      List(role),
      AccessTokenValidity,
      Map("type" -> TokenTypes.AccessToken, "sanDiegoId" -> sanDiegoId.get.toString)
    )
    service.extract[AccessToken](accessToken, TokenTypes.AccessToken) shouldEqual Right(AccessToken(userId, sanDiegoId, role))
  }

  it should "be able to extract a VerificationToken definition from a token" in {
    val userId = "user-id"
    val role   = "role"

    val verificationToken =
      jwt.createAndSignJWTToken(userId, List(role), VerificationTokenValidity, Map("type" -> TokenTypes.VerificationToken))
    service.extract[VerificationToken](verificationToken, TokenTypes.VerificationToken) shouldEqual Right(VerificationToken(userId, role))
  }

  it should "be able to extract a ResetPasswordToken definition from a token" in {
    val userId  = "user-id"
    val role    = "role"
    val tokenId = "token-id"

    val resetPasswordToken = jwt.createAndSignJWTToken(
      userId,
      List(role),
      PasswordTokenValidity,
      Map("type" -> TokenTypes.ResetPasswordToken, "passwordTokenId" -> tokenId)
    )
    service.extract[ResetPasswordToken](resetPasswordToken, TokenTypes.ResetPasswordToken) shouldEqual Right(
      ResetPasswordToken(userId, role, tokenId)
    )
  }

  it should "be able to extract a ChangeEmailToken definition from a token" in {
    val userId  = "user-id"
    val role    = "role"
    val tokenId = "token-id"

    val changeEmailToken = jwt.createAndSignJWTToken(
      userId,
      List(role),
      ChangeEmailTokenValidity,
      Map("type" -> TokenTypes.ChangeEmailToken, "emailTokenId" -> tokenId)
    )
    service.extract[ResetPasswordToken](changeEmailToken, TokenTypes.ChangeEmailToken) shouldEqual Right(
      ChangeEmailToken(userId, role, tokenId)
    )
  }

  it should "be able to extract a LoginAsToken definition from a token" in {
    val userId  = "user-id"
    val role    = "role"
    val tokenId = "token-id"

    val loginAsToken = jwt.createAndSignJWTToken(
      userId,
      List(role),
      LoginAsTokenValidity,
      Map("type" -> TokenTypes.LoginAsToken)
    )
    service.extract[LoginAsToken](loginAsToken, TokenTypes.LoginAsToken) shouldEqual Right(LoginAsToken(userId, role))
  }

  it should "be able to extract a more generic token that complies to expectations" in {
    val userId     = "user-id"
    val role       = "role"
    val sanDiegoId = None

    val someToken = jwt.createAndSignJWTToken(userId, List(role), AccessTokenValidity, Map("type" -> TokenTypes.AccessToken))
    service.extract[Token](someToken, TokenTypes.AccessToken, TokenTypes.VerificationToken, TokenTypes.ResetPasswordToken) shouldEqual
    Right(AccessToken(userId, sanDiegoId, role))
  }

  it should "be able to extract a token without giving expectations" in {
    val userId     = "user-id"
    val role       = "role"
    val sanDiegoId = None

    val someToken = jwt.createAndSignJWTToken(userId, List(role), AccessTokenValidity, Map("type" -> TokenTypes.AccessToken))
    service.extract[Token](someToken) shouldEqual Right(AccessToken(userId, sanDiegoId, role))
  }

  it should "fail to extract a token that does not conform to expectation" in {
    val userId = "user-id"
    val role   = "role"

    val someToken = jwt.createAndSignJWTToken(userId, List(role), VerificationTokenValidity, Map("type" -> TokenTypes.VerificationToken))
    service.extract[AccessToken](someToken, TokenTypes.AccessToken).left.get shouldBe a[InvalidTokenType]
  }

  it should "fail to extract a token that is of an unsupported type" in {
    val userId = "user-id"
    val role   = "role"

    val someToken = jwt.createAndSignJWTToken(userId, List(role), VerificationTokenValidity, Map("type" -> "unsupported"))
    service.extract[Token](someToken).left.get shouldBe a[InvalidTokenType]
  }

  it should "fail to extract a token that is expired" in {
    val userId = "user-id"
    val role   = "role"

    val someToken = jwt.createAndSignJWTToken(userId, List(role), -2.days, Map("type" -> TokenTypes.AccessToken))
    service.extract[Token](someToken).left.get shouldBe a[TokenExpiredException]
  }

  behavior of "Token generation"

  it should "be able to generate a token with an AccessToken definition without optional fields" in {
    val jwt     = mock[JWTProvider]
    val service = new TokenService(jwt)

    val userId         = "user-id"
    val role           = "role"
    val generatedToken = "generated-token"

    val claims = Map[String, String]("type" -> TokenTypes.AccessToken)

    when(jwt.createAndSignJWTToken(userId, List(role), AccessTokenValidity, claims)) thenReturn generatedToken

    service.generate(AccessToken(userId, None, role)) should be(generatedToken)

    verify(jwt, times(1)).createAndSignJWTToken(userId, List(role), AccessTokenValidity, claims)
  }

  it should "be able to generate a token with an AccessToken definition with optional fields" in {
    val jwt     = mock[JWTProvider]
    val service = new TokenService(jwt)

    val userId         = "user-id"
    val role           = "role"
    val generatedToken = "generated-token"
    val sanDiegoId     = Some(2)
    val emailAddress   = Some("<EMAIL>")
    val site           = Some("intermediair.nl")

    val claims = Map[String, String](
      "type"         -> TokenTypes.AccessToken,
      "sanDiegoId"   -> sanDiegoId.get.toString,
      "emailAddress" -> emailAddress.get,
      "site"         -> site.get
    )

    when(jwt.createAndSignJWTToken(userId, List(role), AccessTokenValidity, claims)) thenReturn generatedToken

    service.generate(AccessToken(userId, sanDiegoId, role, emailAddress, site)) should be(generatedToken)

    verify(jwt, times(1)).createAndSignJWTToken(userId, List(role), AccessTokenValidity, claims)
  }

  it should "be able to generate a token with a VerificationToken definition" in {
    val jwt     = mock[JWTProvider]
    val service = new TokenService(jwt)

    val userId         = "user-id"
    val role           = "role"
    val generatedToken = "generated-token"

    when(
      jwt.createAndSignJWTToken(userId, List(role), VerificationTokenValidity, Map("type" -> TokenTypes.VerificationToken))
    ) thenReturn generatedToken

    service.generate(VerificationToken(userId, role)) should be(generatedToken)
    verify(jwt, times(1)).createAndSignJWTToken(userId, List(role), VerificationTokenValidity, Map("type" -> TokenTypes.VerificationToken))
  }

  it should "be able to generate a token with a ResetPasswordToken definition" in {
    val jwt     = mock[JWTProvider]
    val service = new TokenService(jwt)

    val userId         = "user-id"
    val role           = "role"
    val tokenId        = "token-id"
    val generatedToken = "generated-token"

    when(
      jwt.createAndSignJWTToken(
        userId,
        List(role),
        PasswordTokenValidity,
        Map("type" -> TokenTypes.ResetPasswordToken, "passwordTokenId" -> tokenId)
      )
    ) thenReturn generatedToken

    service.generate(ResetPasswordToken(userId, role, tokenId)) should be(generatedToken)
    verify(jwt, times(1)).createAndSignJWTToken(
      userId,
      List(role),
      PasswordTokenValidity,
      Map("type" -> TokenTypes.ResetPasswordToken, "passwordTokenId" -> tokenId)
    )
  }
}
