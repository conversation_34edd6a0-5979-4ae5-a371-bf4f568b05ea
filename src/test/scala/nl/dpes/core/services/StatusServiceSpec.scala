package nl.dpes.core.services

import nl.dpes.core.config.Configuration
import org.joda.time.DateTime
import org.scalatest.Outcome
import org.scalatest.flatspec.FixtureAnyFlatSpec
import org.scalatest.matchers.should.Matchers
import org.scalatestplus.mockito.MockitoSugar
import org.slf4j.Logger
import scalikejdbc._

import scala.concurrent.duration._

class StatusServiceSpec extends FixtureAnyFlatSpec with Matchers with Configuration with MockitoSugar {
  import StatusService._

  ConnectionPool.singleton(
    MySQL.connectionString,
    MySQL.user,
    MySQL.password
  )

  private implicit val logger: Logger = mock[Logger]

  private final val licenses: Set[License] = Set(
    License("GDPR module", DateTime.now().plus(100.days.toMillis))
  )

  it should "default to Starting" in { implicit session =>
    val service = new StatusService(licenses)
    service.getApplicationState shouldBe ApplicationStatus.Starting
  }

  it should "return Starting when application is starting" in { implicit session =>
    val service = new StatusService(licenses)
    service.onApplicationStarting()

    service.getApplicationState shouldBe ApplicationStatus.Starting
  }

  it should "return Started when application is started" in { implicit session =>
    val service = new StatusService(licenses)
    service.onApplicationStarted()

    service.getApplicationState shouldBe ApplicationStatus.Started
  }

  it should "return Stopping when application is shutting down" in { implicit session =>
    val service = new StatusService(licenses)
    service.onApplicationStopping()

    service.getApplicationState shouldBe ApplicationStatus.Stopping
  }

  it should "return Stopped when application is shut down" in { implicit session =>
    val service = new StatusService(licenses)
    service.onApplicationStopped()

    service.getApplicationState shouldBe ApplicationStatus.Stopped
  }

  it should "return Error when application is in error state" in { implicit session =>
    session.close()

    val service = new StatusService(licenses)
    service.onApplicationStarted()

    service.getApplicationState shouldBe ApplicationStatus.Error
  }

  type FixtureParam = DBSession

  protected[this] def settingsProvider: SettingsProvider =
    SettingsProvider.default

  /** Creates a [[scalikejdbc.DB]] instance.
    *
    * @return DB instance
    */
  def db(): DB =
    DB(conn = ConnectionPool.borrow(), settingsProvider = settingsProvider)

  /** Prepares database for the test.
    *
    * @param session db session implicitly
    */
  def fixture(implicit session: DBSession): Unit = {}

  /** Provides transactional block
    *
    * @param test one arg test
    */
  override def withFixture(test: OneArgTest): Outcome =
    using(db()) { db =>
      try {
        db.begin()
        db.withinTx { implicit session =>
          fixture(session)
        }
        withFixture(test.toNoArgTest(db.withinTxSession()))
      } finally db.rollbackIfActive()
    }
}
