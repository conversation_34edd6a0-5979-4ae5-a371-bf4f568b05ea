package nl.dpes.core.services

import nl.dpes.core.services.MailService._
import nl.dpes.core.services.mail.MailServiceClient
import nl.dpes.testutils.EventBusSupport
import nl.dpes.testutils.fixtures.domain.WerkzoekendeFixtures._
import nl.dpes.testutils.fixtures.services.MailServiceFixtures._
import org.mockito.ArgumentMatchers
import org.mockito.ArgumentMatchers.any
import org.mockito.Mockito._
import org.scalatest.flatspec.AnyFlatSpec
import org.scalatest.{BeforeAndAfter, BeforeAndAfterAll}
import org.scalatestplus.mockito.MockitoSugar
import org.slf4j.Logger

import scala.concurrent.ExecutionContext.Implicits.global
import scala.concurrent.Future

class MailServiceSpec extends AnyFlatSpec with MockitoSugar with BeforeAndAfterAll with EventBusSupport with BeforeAndAfter {
  private implicit val logger: Logger = mock[Logger]

  private val client             = mock[MailServiceClient]
  override protected val service = new MailService(client)

  before {
    reset(client)
  }

  it should "send an email on Geregistreerd" in {
    when(client.send(any[Mail])) thenReturn Future.unit

    publish(geregistreerd)

    verify(client, times(1)).send(
      ArgumentMatchers.eq(
        RegistratieMail(geregistreerd.eMailadres, geregistreerd.site, geregistreerd.verificatieUrl, geregistreerd.verificatieToken)
      )
    )
  }

  it should "send an email on WachtwoordVergeten" in {
    when(client.send(any[Mail])) thenReturn Future.unit

    publish(wachtwoordVergeten)

    verify(client, times(1)).send(
      ArgumentMatchers.eq(
        WachtwoordVergetenMail(
          wachtwoordVergeten.eMailadres,
          wachtwoordVergeten.site,
          wachtwoordVergeten.herstelUrl,
          wachtwoordVergeten.herstelToken,
          wachtwoordVergeten.customTemplate.get
        )
      )
    )
  }

  it should "send an email on WijzigEMailadres" in {
    when(client.send(any[Mail])) thenReturn Future.unit

    publish(eMailadresWijzigingVerzocht)

    verify(client, times(1)).send(
      ArgumentMatchers.eq(
        WijzigEMailadresMail(
          eMailadresWijzigingVerzocht.nieuwEMailadres,
          eMailadresWijzigingVerzocht.site,
          eMailadresWijzigingVerzocht.verificatieUrl,
          eMailadresWijzigingVerzocht.verificatieToken
        )
      )
    )
  }

  it should "send a OpgeslagenZoekopdracht mail" in {
    when(client.send(any[OpgeslagenZoekopdrachtMail])) thenReturn Future.unit

    service.send(
      OpgeslagenZoekopdrachtMail(
        geregistreerd.eMailadres,
        geregistreerd.site,
        recruiter,
        jobSeekers,
        opgeslagenZoekopdracht
      )
    )

    verify(client, times(1)).send(any[OpgeslagenZoekopdrachtMail])
  }
}
