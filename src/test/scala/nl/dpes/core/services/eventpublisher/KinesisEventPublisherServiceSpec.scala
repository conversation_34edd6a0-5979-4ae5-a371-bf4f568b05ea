package nl.dpes.core.services.eventpublisher

import java.nio.ByteBuffer
import java.time.Instant
import nl.dpes.core.domain.Werkzoekende.Geregistreerd
import nl.dpes.core.services.IdentifierService
import nl.dpes.core.services.protobuf.WerkzoekendeMappers._
import nl.dpes.core.services.protobuf.Envelope
import com.amazonaws.services.kinesis.model.{DescribeStreamResult, ResourceNotFoundException}
import com.amazonaws.services.kinesis.{model, AmazonKinesis}
import org.mockito.ArgumentMatchers._
import org.mockito.Mockito._
import org.scalatest.BeforeAndAfter
import org.scalatest.flatspec.AnyFlatSpec
import org.scalatest.matchers.should.Matchers
import org.scalatestplus.mockito.MockitoSugar
import org.slf4j.Logger

class KinesisEventPublisherServiceSpec extends AnyFlatSpec with Matchers with MockitoSugar with BeforeAndAfter {
  val client: AmazonKinesis                         = mock[AmazonKinesis]
  implicit val identifierService: IdentifierService = mock[IdentifierService]
  implicit val logger: Logger                       = mock[Logger]

  val streamDescriptionResult: DescribeStreamResult = new DescribeStreamResult()
    .withStreamDescription(
      new model.StreamDescription()
        .withStreamStatus("ACTIVE")
    )
  val event: Geregistreerd = Geregistreerd("uuid", "<EMAIL>", "intermediair.nl", "http://test.com", "verificationtoken")
  val now                  = Instant.now()

  before(reset(client, logger))

  behavior of "creating the service"
  it should "not be created when the stream is not active" in {
    val streamDescriptionResult: DescribeStreamResult = new DescribeStreamResult()
      .withStreamDescription(
        new model.StreamDescription()
          .withStreamStatus("foo")
      )
    when(client.describeStream(any[String])).thenReturn(streamDescriptionResult)

    KinesisEventPublisherService(client, "test-domain") shouldBe None
  }

  it should "not be created when the stream is not found" in {
    val streamDescriptionResult: DescribeStreamResult = new DescribeStreamResult()
      .withStreamDescription(
        new model.StreamDescription()
          .withStreamStatus("foo")
      )
    when(client.describeStream(any[String])).thenThrow(new ResourceNotFoundException("error"))

    KinesisEventPublisherService(client, "test-domain") shouldBe None
  }

  it should "not be created when an error occurred" in {
    val streamDescriptionResult: DescribeStreamResult = new DescribeStreamResult()
      .withStreamDescription(
        new model.StreamDescription()
          .withStreamStatus("foo")
      )
    when(client.describeStream(any[String])).thenThrow(new RuntimeException("error"))

    KinesisEventPublisherService(client, "test-domain") shouldBe None
  }

  behavior of "sending an event"

  it should "send a message to kinesis when an event is handled" in {
    when(client.describeStream(any[String])).thenReturn(streamDescriptionResult)
    when(identifierService.generateUUID()).thenReturn("some-uuid")
    val service: KinesisEventPublisherService = KinesisEventPublisherService(client, "test-domain").get

    service.onWerkzoekendeEvent(event, now)

    verify(client).putRecord(
      "test-domain",
      ByteBuffer.wrap(GeregistreerdProtobufMapper.toProto(Envelope("some-uuid", now, "core", event)).toByteArray),
      event.werkzoekendeId
    )
  }

  it should "not send an event when an error occurred" in {
    when(client.describeStream(any[String])).thenReturn(streamDescriptionResult)
    val service: KinesisEventPublisherService = KinesisEventPublisherService(client, "test-domain").get

    when(client.putRecord(any[String], any[ByteBuffer], any[String])).thenThrow(new RuntimeException("error"))
    service.onWerkzoekendeEvent(event, now)

    verify(logger).error("Error sending record to Amazon Kinesis: java.lang.RuntimeException error")
  }
}
