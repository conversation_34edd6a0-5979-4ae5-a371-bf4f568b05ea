package nl.dpes.core.services

import nl.dpes.core.domain.{<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>kpar<PERSON><PERSON>, Zoektermen}
import nl.dpes.core.services.ndsm.JobSeekerServiceClient
import nl.dpes.core.domain.Sites._
import nl.dpes.core.services.ndsm.JobSeekerServiceClient.{JobSeeker, SearchParameters, SearchResult, SearchTerms}
import nl.dpes.testutils.EventBusSupport
import nl.dpes.testutils.fixtures.services.JobSeekerServiceFixtures._
import org.scalatest.{BeforeAndAfter, BeforeAndAfterAll}
import org.mockito.Mockito.{reset, times, verify, when}
import org.mockito.ArgumentMatchers._
import org.scalatest.flatspec.AnyFlatSpec
import org.scalatest.matchers.should.Matchers
import org.scalatestplus.mockito.MockitoSugar
import org.slf4j.Logger

import scala.concurrent.{Await, Future}
import scala.concurrent.duration._
import scala.concurrent.ExecutionContext.Implicits.global

class JobSeekerServiceSpec
    extends AnyFlatSpec
    with Matchers
    with MockitoSugar
    with BeforeAndAfter
    with BeforeAndAfterAll
    with EventBusSupport {
  private val patience           = 10 seconds
  private val client             = mock[JobSeekerServiceClient]
  private implicit val logger    = mock[Logger]
  override protected val service = new JobSeekerService(client)

  private val ndsmClientJobSeeker = JobSeekerServiceClient.JobSeeker(
    NdsmJobSeekerId,
    Some(LegacyJobSeekerId),
    NdsmJobSeekerSite,
    Some(NdsmJobSeekerFirstName),
    Some(NdsmJobSeekerLastName),
    NdsmJobSeekerEmailAddress
  )

  before {
    reset(client)
  }

  it should "find a JobSeeker by UUID" in {
    when(client.findByUuid(NdsmJobSeekerId)).thenReturn(Future(Some(ndsmClientJobSeeker)))

    Await.result(service.findByUuid(NdsmJobSeekerId), patience) should be(Some(ndsmJobSeeker))

    verify(client, times(1)).findByUuid(NdsmJobSeekerId)
  }

  it should "handle missing fields in JobSeekerServiceClient JobSeeker" in {
    val clientJobSeekerWithEmptyOptionalValues = JobSeeker(
      firstName = None,
      lastName = None,
      legacyJobSeekerId = ndsmClientJobSeeker.legacyJobSeekerId,
      id = ndsmClientJobSeeker.id,
      emailAddress = ndsmClientJobSeeker.emailAddress,
      site = ndsmClientJobSeeker.site
    )

    val serviceJobSeekerWithEmptyValues = JobSeekerService.JobSeeker(
      firstName = "",
      lastName = "",
      legacyJobSeekerId = ndsmClientJobSeeker.legacyJobSeekerId,
      id = ndsmClientJobSeeker.id,
      emailAddress = ndsmClientJobSeeker.emailAddress,
      site = ndsmClientJobSeeker.site
    )

    JobSeekerService.JobSeeker.fromClientJobSeeker(clientJobSeekerWithEmptyOptionalValues) should be(serviceJobSeekerWithEmptyValues)
  }

  behavior of "search"

  it should "find jobseekers from all sites" in {

    val zoekterms = Zoektermen(
      Some(Seq("all")),
      None,
      None
    )

    val zoekparameters = Zoekparameters(
      Some(zoekterms),
      Some("Amsterdam"),
      Some("Afgelopen week")
    )

    val mockSearchResults = SearchResult(List(ndsmClientJobSeeker), 1, locationNotFound = false, invalidQuery = false)

    val expectedSearchParameters = SearchParameters(
      term = Some(SearchTerms(Some("all"))),
      location = Some("Amsterdam"),
      updatedDate = Some(Seq("Afgelopen week")),
      pageSize = Some(25)
    )

    when(client.search(any[SearchParameters])).thenReturn(Future.successful(mockSearchResults))

    Await.result(service.search(zoekparameters), patience)

    verify(client, times(1)).search(expectedSearchParameters)
  }

  it should "fix radius and find jobseekers from all sites" in {

    val zoekterms = Zoektermen(
      Some(Seq("all")),
      None,
      None
    )

    val zoekparameters = Zoekparameters(
      zoektermen = Some(zoekterms),
      locatie = Some("Amsterdam"),
      wijzigingsdatum = Some("Afgelopen week"),
      afstandTotWerklocatie = Some("*-20.0")
    )

    val mockSearchResults = SearchResult(List(ndsmClientJobSeeker), 1, locationNotFound = false, invalidQuery = false)

    val expectedSearchParameters = SearchParameters(
      term = Some(SearchTerms(Some("all"))),
      location = Some("Amsterdam"),
      radius = Some(Seq("20")),
      updatedDate = Some(Seq("Afgelopen week")),
      pageSize = Some(25)
    )

    when(client.search(any[SearchParameters])).thenReturn(Future.successful(mockSearchResults))

    Await.result(service.search(zoekparameters), patience)

    verify(client, times(1)).search(expectedSearchParameters)
  }

  it should "ignore type of job filter" in {
    val savedSearchParameters = Zoekparameters(
      zoektermen = Some(Zoektermen(Some(Seq("foo")))),
      soortenWerk = Some(Seq("bar"))
    )

    val searchResults = SearchResult(List(ndsmClientJobSeeker), 1, locationNotFound = false, invalidQuery = false)

    val jobSeekerServiceSearchParameters = SearchParameters(
      term = Some(SearchTerms(Some("foo"))),
      pageSize = Some(nl.dpes.core.services.JobSeekerService.SearchDefaultPageSize)
    )

    when(client.search(any[SearchParameters])).thenReturn(Future.successful(searchResults))

    Await.result(service.search(savedSearchParameters), patience)

    verify(client, times(1)).search(jobSeekerServiceSearchParameters)
  }
}
