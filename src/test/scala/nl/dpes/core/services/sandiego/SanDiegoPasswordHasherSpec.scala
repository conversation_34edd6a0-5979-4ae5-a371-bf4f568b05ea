package nl.dpes.core.services.sandiego

import nl.dpes.core.config.Configuration
import nl.dpes.core.services.security.PasswordHasher.HashedPassword
import org.scalatest.flatspec.AnyFlatSpec
import org.scalatest.matchers.should.Matchers
import org.scalatestplus.mockito.MockitoSugar

class SanDiegoPasswordHasherSpec extends AnyFlatSpec with Matchers with MockitoSugar with Configuration {
  private val hasher = new SanDiegoPasswordHasher(PHP.executable)

  it should "hash a password" in {
    val password = "somepassword"

    val hashed = hasher.hashPassword(password)
    hashed shouldBe a[HashedPassword]

    hashed.hash shouldNot be(empty)
    hashed.salt shouldNot be(empty)
  }

  it should "hash a password with salt" in {
    val password = "somepassword"
    val salt     = "<EMAIL>"

    val hashed = hasher.hashPassword(password, salt)
    hashed shouldBe a[HashedPassword]

    hashed.hash shouldNot be(empty)
    hashed.salt shouldEqual salt
  }

  it should "verify a password is correct" in {
    val password = "somepassword"
    val salt     = "<EMAIL>"

    val hashed = hasher.hashPassword(password, salt)
    hasher.verifyHashedPassword(hashed, password) shouldBe true
  }

  it should "verify a password is incorrect" in {
    val password = "somepassword"
    val salt     = "<EMAIL>"

    val hashed = hasher.hashPassword(password, salt)
    hasher.verifyHashedPassword(hashed, "wrongpassword") shouldBe false
  }
}
