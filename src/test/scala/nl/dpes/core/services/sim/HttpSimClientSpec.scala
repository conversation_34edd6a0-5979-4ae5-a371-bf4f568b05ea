package nl.dpes.core.services.sim

import akka.actor.ActorSystem
import nl.dpes.common.marshalling.JsonSupport
import nl.dpes.core.domain.Sites
import nl.dpes.core.domain.werkzoekende.EMailinschrijvingen
import nl.dpes.core.services.SimService.Statuses
import nl.dpes.core.services.sim.SimClient.UserProfile
import org.scalatest.BeforeAndAfterAll
import org.scalatest.concurrent.ScalaFutures
import org.scalatest.flatspec.AnyFlatSpec
import org.scalatest.matchers.should.Matchers
import spray.json.RootJsonFormat
import sttp.client3.sprayJson._
import sttp.client3.testing.SttpBackendStub
import sttp.client3.{basicRequest, SttpBackend, UriContext}
import sttp.model.{Header, MediaType}

import scala.concurrent.Future
import scala.concurrent.duration._

class HttpSimClientSpec extends AnyFlatSpec with Matchers with ScalaFutures with BeforeAndAfterAll with JsonSupport {
  private implicit val system = ActorSystem("HttpSimClientSpec")

  private implicit val patience: PatienceConfig = PatienceConfig(3 seconds)

  final private val bindHost = "localhost"
  final private val bindPort = 12345

  private val userId = "werkzoekende-id-1"

  private val profile = UserProfile(
    userId,
    "<EMAIL>",
    "intermediair.nl",
    "origin ndp",
    None,
    None,
    Statuses.Geregistreerd,
    None,
    None
  )
  private val subscription = EMailinschrijvingen.Persoonlijk
  private val site         = Sites.Nvb

  implicit val userProfileFormat: RootJsonFormat[UserProfile] = jsonFormat9(UserProfile)
  private val baseUrl                                         = s"https://$bindHost:$bindPort/api/v1"

  behavior of "SIM user profiles"

  it should "create a user profile in SIM successfully" in {
    implicit val sttpBackend: SttpBackend[Future, _] =
      SttpBackendStub.asynchronousFuture
        .whenRequestMatches(
          _ == basicRequest
            .put(uri"$baseUrl/user-profiles/b2c")
            .header(Header.contentType(MediaType.ApplicationJson))
            .body(profile)
        )
        .thenRespondWithCode(sttp.model.StatusCode.Accepted)

    val client = new HttpSimClient(baseUrl)

    whenReady(client.createOrUpdateUserProfile(profile)) { result =>
      result shouldBe ()
    }
  }

  it should "fail when creating a user profile in SIM failed" in {
    implicit val sttpBackend: SttpBackend[Future, _] =
      SttpBackendStub.asynchronousFuture.whenAnyRequest
        .thenRespondWithCode(sttp.model.StatusCode.InternalServerError)

    val client = new HttpSimClient(baseUrl)
    whenReady(client.createOrUpdateUserProfile(profile.copy(werkzoekendeId = "failing-id")).failed) { e =>
      e shouldBe a[Exception]
    }
  }

  behavior of "SIM user subscriptions"

  it should "subscribe a user in SIM successfully" in {
    implicit val sttpBackend: SttpBackend[Future, _] =
      SttpBackendStub.asynchronousFuture
        .whenRequestMatches(
          _ == basicRequest
            .put(uri"$baseUrl/user-profiles/b2c/$userId/subscriptions/$site/$subscription")
            .header(Header.contentType(MediaType.ApplicationJson))
        )
        .thenRespondWithCode(sttp.model.StatusCode.Accepted)

    val client = new HttpSimClient(baseUrl)

    whenReady(client.subscribe(userId, subscription, site)) { result =>
      result shouldBe ()
    }
  }

  it should "fail when subscribing a user in SIM failed" in {
    implicit val sttpBackend: SttpBackend[Future, _] =
      SttpBackendStub.asynchronousFuture.whenAnyRequest
        .thenRespondWithCode(sttp.model.StatusCode.InternalServerError)

    val client = new HttpSimClient(baseUrl)

    whenReady(client.subscribe("failing-id", subscription, site).failed) { e =>
      e shouldBe a[Exception]
    }
  }

  behavior of "SIM user unsubscriptions"

  it should "unsubscribe a user in SIM successfully" in {
    implicit val sttpBackend: SttpBackend[Future, _] =
      SttpBackendStub.asynchronousFuture
        .whenRequestMatches(
          _ == basicRequest
            .put(uri"$baseUrl/user-profiles/b2c/$userId/unsubscriptions/$site/$subscription")
            .header(Header.contentType(MediaType.ApplicationJson))
        )
        .thenRespondWithCode(sttp.model.StatusCode.Accepted)

    val client = new HttpSimClient(baseUrl)

    whenReady(client.unsubscribe(userId, subscription, site)) { result =>
      result shouldBe ()
    }
  }

  it should "fail when unsubscribing a user in SIM failed" in {
    implicit val sttpBackend: SttpBackend[Future, _] =
      SttpBackendStub.asynchronousFuture.whenAnyRequest
        .thenRespondWithCode(sttp.model.StatusCode.InternalServerError)

    val client = new HttpSimClient(baseUrl)

    whenReady(client.unsubscribe("failing-id", subscription, site).failed) { e =>
      e shouldBe a[Exception]
    }
  }

  behavior of "deleting a werkzoekende in SIM"

  it should "delete a user in SIM successfully" in {
    implicit val sttpBackend: SttpBackend[Future, _] =
      SttpBackendStub.asynchronousFuture
        .whenRequestMatches(_ == basicRequest.delete(uri"$baseUrl/user-profiles/b2c/$userId"))
        .thenRespondWithCode(sttp.model.StatusCode.Accepted)

    val client = new HttpSimClient(baseUrl)

    whenReady(client.deleteProfile(userId)) { result =>
      result shouldBe ()
    }
  }

  it should "fail when deleting a user in SIM failed" in {
    implicit val sttpBackend: SttpBackend[Future, _] =
      SttpBackendStub.asynchronousFuture.whenAnyRequest
        .thenRespondWithCode(sttp.model.StatusCode.InternalServerError)

    val client = new HttpSimClient(baseUrl)

    whenReady(client.deleteProfile("failing-id").failed) { e =>
      e shouldBe a[Exception]
    }
  }
}
