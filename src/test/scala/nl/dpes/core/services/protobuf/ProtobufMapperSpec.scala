package nl.dpes.core.services.protobuf

import java.time.Instant
import nl.dpes.core.domain.Sites
import nl.dpes.core.domain.Sites._
import nl.dpes.core.domain.Werkzoekende.Geregistreerd
import nl.dpes.core.services.protobuf
import nl.dpes.core.services.protobuf.WerkzoekendeMappers._
import org.scalatest.flatspec.AnyFlatSpec
import org.scalatest.matchers.should.Matchers
import org.scalatestplus.mockito.MockitoSugar

class ProtobufMapperSpec extends AnyFlatSpec with Matchers with MockitoSugar {
  val werkzoekendeId: String   = "werkzoekende"
  val eMailadres: String       = "<EMAIL>"
  val site: Sites.Site         = Iol
  val verificatieUrl: String   = "http://example.com"
  val verificatieToken: String = "token"

  val EVENT: Geregistreerd = Geregistreerd(
    werkzoekendeId,
    eMailadres,
    site,
    verificatieUrl,
    verificatieToken
  )

  behavior of "toProto"

  it should "return correct value" in {
    val timestamp = Instant.ofEpochMilli(0)

    val actual = GeregistreerdProtobufMapper.toProto(protobuf.Envelope("test", timestamp, "core", EVENT))
    actual.toByteArray.slice(0, 10) shouldBe Array[Byte](10, 4, 116, 101, 115, 116, 18, 0, 26, 4)
  }
}
