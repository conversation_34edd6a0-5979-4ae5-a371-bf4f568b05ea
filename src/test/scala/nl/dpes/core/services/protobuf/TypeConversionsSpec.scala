package nl.dpes.core.services.protobuf

import nl.dpes.core.domain.Sites.{Iol, Itb, Nvb}
import nl.dpes.core.domain.Frequenties.{<PERSON><PERSON>i<PERSON><PERSON>, Nooit, Wekelijks}
import nl.dpes.protocol.v1.Frequence.{DAILY, NEVER, WEEKLY}
import nl.dpes.protocol.v1.Site.{IOL, ITB, NVB}
import org.scalatest.BeforeAndAfter
import org.scalatest.flatspec.AnyFlatSpec
import org.scalatest.matchers.should.Matchers
import org.scalatestplus.mockito.MockitoSugar

class TypeConversionsSpec extends AnyFlatSpec with BeforeAndAfter with Matchers with MockitoSugar {

  behavior of "TypeConversions"

  behavior of "toSite"

  import TypeConversions._

  it should "return IOL, given site === 'Iol'" in {
    toSite(Iol) should equal(IOL)
  }

  it should "return ITB, given site === 'Itb'" in {
    toSite(Itb) should equal(ITB)
  }

  it should "return NVB, given site === 'Nvb'" in {
    toSite(Nvb) should equal(NVB)
  }

  behavior of "toFrequence"

  it should "return NEVER, given site === 'Nooit'" in {
    toFrequence(Nooit) should equal(NEVER)
  }

  it should "return DAILY, given site === 'Dagelijks'" in {
    toFrequence(Dagelijks) should equal(DAILY)
  }

  it should "return WEEKLY, given site === 'Wekelijks'" in {
    toFrequence(Wekelijks) should equal(WEEKLY)
  }

}
