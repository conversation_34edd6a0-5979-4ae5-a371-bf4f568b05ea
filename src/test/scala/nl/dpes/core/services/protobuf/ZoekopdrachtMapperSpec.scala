package nl.dpes.core.services.protobuf

import nl.dpes.core.domain.{<PERSON><PERSON><PERSON>ame<PERSON>, Zoektermen}
import nl.dpes.core.protocol.v1.SearchRequest.SearchParameterValues
import org.scalatest.flatspec.AnyFlatSpec
import org.scalatest.BeforeAndAfter
import org.scalatest.matchers.should.Matchers
import org.scalatestplus.mockito.MockitoSugar

class ZoekopdrachtMapperSpec extends AnyFlatSpec with BeforeAndAfter with Matchers with MockitoSugar {

  behavior of "ZoekopdrachtMapper.toSearchParameters"

  it should "return empty map, given ZoekParameters has no values" in {
    val zoekParameters: Zoekparameters = Zoekparameters()

    val actual = ZoekopdrachtMapper.toSearchParameters(zoekParameters)

    actual shouldBe Map()
  }

  it should "return correct map, given ZoekParameters has one value" in {
    val zoekParameters: Zoekparameters = Zoekparameters(locatie = Some("locatie"))

    val actual = ZoekopdrachtMapper.toSearchParameters(zoekParameters)

    actual shouldBe Map(
      "locatie" -> SearchParameterValues(List("locatie"))
    )
  }

  it should "return correct map, given ZoekParameters has some Zoektermen" in {
    val zoekParameters: Zoekparameters = Zoekparameters(zoektermen =
      Some(
        Zoektermen(
          alles = Some(Seq("alles")),
          opleidingNaam = Some(Seq("opleidingNaam")),
          opleidingBeschrijving = Some(Seq("opleidingBeschrijving")),
          gewensteBaan = Some(Seq("gewensteBaan")),
          functieTitel = Some(Seq("functieTitel")),
          functieBeschrijving = Some(Seq("functieBeschrijving")),
          cursussen = Some(Seq("cursussen"))
        )
      )
    )

    val actual = ZoekopdrachtMapper.toSearchParameters(zoekParameters)

    actual shouldBe Map(
      "term.alles"                 -> SearchParameterValues(List("alles")),
      "term.opleidingNaam"         -> SearchParameterValues(List("opleidingNaam")),
      "term.opleidingBeschrijving" -> SearchParameterValues(List("opleidingBeschrijving")),
      "term.gewensteBaan"          -> SearchParameterValues(List("gewensteBaan")),
      "term.functieTitel"          -> SearchParameterValues(List("functieTitel")),
      "term.functieBeschrijving"   -> SearchParameterValues(List("functieBeschrijving")),
      "term.cursussen"             -> SearchParameterValues(List("cursussen"))
    )
  }

  it should "return correct map, given ZoekParameters has multiple values" in {
    val zoekParameters: Zoekparameters = Zoekparameters(
      zoektermen = None,
      locatie = Some("locatie"),
      wijzigingsdatum = Some("wijzigingsdatum"),
      opleidingsniveaus = Some(Seq("opleidingsniveaus")),
      aantallenUren = Some(Seq("aantallenUren")),
      soortenWerk = Some(Seq("soortenWerk")),
      beschikbaarheden = Some(Seq("beschikbaarheden")),
      rijbewijzen = Some(Seq("rijbewijzen")),
      talen = Some(Seq("talen")),
      afstandTotWerklocatie = Some("afstandTotWerklocatie"),
      carriereniveau = Some(Seq("carriereniveau")),
      functiegroep = Some(Seq("functiegroep")),
      gewenstSalaris = Some(Seq("gewenstSalaris")),
      provincies = Some(Seq("provincies"))
    )

    val actual = ZoekopdrachtMapper.toSearchParameters(zoekParameters)

    actual shouldBe Map(
      "locatie"               -> SearchParameterValues(List("locatie")),
      "wijzigingsdatum"       -> SearchParameterValues(List("wijzigingsdatum")),
      "opleidingsniveaus"     -> SearchParameterValues(List("opleidingsniveaus")),
      "aantallenUren"         -> SearchParameterValues(List("aantallenUren")),
      "soortenWerk"           -> SearchParameterValues(List("soortenWerk")),
      "beschikbaarheden"      -> SearchParameterValues(List("beschikbaarheden")),
      "rijbewijzen"           -> SearchParameterValues(List("rijbewijzen")),
      "talen"                 -> SearchParameterValues(List("talen")),
      "afstandTotWerklocatie" -> SearchParameterValues(List("afstandTotWerklocatie")),
      "carriereniveau"        -> SearchParameterValues(List("carriereniveau")),
      "functiegroep"          -> SearchParameterValues(List("functiegroep")),
      "gewenstSalaris"        -> SearchParameterValues(List("gewenstSalaris")),
      "provincies"            -> SearchParameterValues(List("provincies"))
    )
  }
}
