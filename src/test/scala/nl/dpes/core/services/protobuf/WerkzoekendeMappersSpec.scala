package nl.dpes.core.services.protobuf

import com.google.protobuf.timestamp.Timestamp
import nl.dpes.core.domain.Sites
import nl.dpes.core.domain.Sites._
import nl.dpes.core.domain.Werkzoekende.EMailadresGewijzigd
import nl.dpes.core.protocol.v1.Jobseeker.ModifiedEmailAddress
import nl.dpes.core.services.protobuf
import nl.dpes.core.services.protobuf.WerkzoekendeMappers._
import nl.dpes.protocol.v1
import nl.dpes.protocol.v1.Envelope.Payload.JobseekerModifiedEmailAddress
import org.scalatest.flatspec.AnyFlatSpec
import org.scalatest.matchers.should.Matchers
import org.scalatestplus.mockito.MockitoSugar

import java.time.{Instant, LocalDateTime, ZoneOffset}
import java.util.UUID

class WerkzoekendeMappersSpec extends AnyFlatSpec with Matchers with MockitoSugar {
  val werkzoekendeId: String   = "werkzoekende"
  val eMailadres: String       = "<EMAIL>"
  val site: Sites.Site         = Iol
  val verificatieUrl: String   = "http://example.com"
  val verificatieToken: String = "token"

  val EVENT: EMailadresGewijzigd = EMailadresGewijzigd(
    werkzoekendeId,
    eMailadres
  )

  behavior of "WerkzoekendeMappers"

  behavior of "EMailadresGewijzigdProtobufMapper"

  it should "be able to perform a roundtrip: toProto -> fromProto" in {

    val timestamp: Instant = LocalDateTime.parse("2018-09-26T13:16:00").toInstant(ZoneOffset.UTC)

    val correlationId = UUID.randomUUID().toString

    val actualToProto: v1.Envelope = EMailadresGewijzigdProtobufMapper.toProto(protobuf.Envelope(correlationId, timestamp, "core", EVENT))

    actualToProto.correlationId shouldBe correlationId
    actualToProto.source shouldBe "core"
    actualToProto.timestamp shouldBe Some(Timestamp(1537967760, 0))
    actualToProto.payload shouldBe JobseekerModifiedEmailAddress(ModifiedEmailAddress(werkzoekendeId, eMailadres))

    val actualFromProto: Envelope[EMailadresGewijzigd] = EMailadresGewijzigdProtobufMapper.fromProto(actualToProto)

    actualFromProto.correlationId shouldBe correlationId
    actualFromProto.timestamp shouldBe timestamp
    actualFromProto.source shouldBe "core"
    actualFromProto.payload shouldBe EVENT
  }
}
