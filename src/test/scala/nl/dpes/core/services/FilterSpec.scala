package nl.dpes.core.services

import org.scalatest.flatspec.AnyFlatSpec
import org.scalatest.matchers.should.Matchers

class FilterSpec extends AnyFlatSpec with Matchers {
  "Filter" should "convert a list of optional filtervalues to a query string" in {
    Filter("emailAddress" -> Some("email@address")).asMap should be(Map("emailAddress" -> "email@address"))
  }

  it should "ignore empty options" in {
    Filter(
      "lastName"   -> Some("last name"),
      "middleName" -> None
    ).asMap should be(Map("lastName" -> "last name"))
  }

  it should "combine multiple filtervalues" in {
    Filter(
      "firstName" -> Some("first name"),
      "lastName"  -> Some("last name")
    ).asMap should be(Map("firstName" -> "first name", "lastName" -> "last name"))
  }

  it should "overwrite a value with the same key" in {
    Filter(
      "key" -> Some("old value"),
      "key" -> Some("new value")
    ).asMap should be(Map("key" -> "new value"))
  }

  it should "convert to none when there are no filterValues" in {
    Filter().asMap should be(Map.empty[String, String])
  }

  it should "convert to none when there are only empty filterValues" in {
    Filter(
      "firstName" -> None,
      "lastName"  -> None
    ).asMap should be(Map.empty[String, String])
  }
}
