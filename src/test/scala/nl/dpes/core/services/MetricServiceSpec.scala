package nl.dpes.core.services

import com.amazonaws.services.cloudwatch.AmazonCloudWatch
import com.amazonaws.services.cloudwatch.model.PutMetricDataRequest
import nl.dpes.core.config.Environment
import org.mockito.ArgumentMatchers._
import org.mockito.Mockito._
import org.scalatest.BeforeAndAfter
import org.scalatest.flatspec.AnyFlatSpec
import org.scalatestplus.mockito.MockitoSugar
import org.slf4j.Logger

class MetricServiceSpec extends AnyFlatSpec with MockitoSugar with BeforeAndAfter {
  private val logger  = mock[Logger]
  private val client  = mock[AmazonCloudWatch]
  private val service = new MetricService(client)(logger, Environment.Testing)

  before {
    reset(client)
  }

  it should "update a metric when an event from San Diego was handled" in {
    service.measureSanDiegoEventHandle()

    verify(client, times(1)).putMetricData(any[PutMetricDataRequest])
  }
}
