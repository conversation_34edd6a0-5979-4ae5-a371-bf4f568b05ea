package nl.dpes.core.services

import io.axoniq.gdpr.cryptoengine.CryptoEngine
import nl.dpes.core.domain.Werkzoekende
import nl.dpes.testutils.fixtures.domain.RecruiterFixtures
import org.mockito.Mockito.{reset, times, verify}
import org.scalatest.BeforeAndAfter
import org.scalatest.flatspec.AnyFlatSpec
import org.scalatestplus.mockito.MockitoSugar

class GDPRServiceSpec extends AnyFlatSpec with MockitoSugar with BeforeAndAfter {
  private val cryptoEngine = mock[CryptoEngine]
  private val service      = new GDPRService(cryptoEngine)

  before {
    reset(cryptoEngine)
  }

  behavior of "GDPR Service"

  it should "delete the decryption token of a werkzoekende when the werkzoekende account was closed" in {
    service.onAccountOpgezegd(Werkzoekende.AccountOpgezegd("uuid", "zomaar"))

    verify(cryptoEngine, times(1)).deleteKey("uuid")
  }

  it should "delete the decryption token of a recruiter on Recruiter.Verwijderd" in {
    service.onRecruiterVerwijderd(RecruiterFixtures.verwijderd)

    verify(cryptoEngine, times(1)).deleteKey(RecruiterFixtures.verwijderd.recruiterId)
  }
}
