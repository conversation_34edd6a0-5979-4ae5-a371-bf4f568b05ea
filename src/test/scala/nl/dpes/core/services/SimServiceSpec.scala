package nl.dpes.core.services

import nl.dpes.core.domain.Sites
import nl.dpes.core.domain.Sites.Site
import nl.dpes.core.domain.werkzoekende.EMailinschrijvingen.EMailinschrijving
import nl.dpes.core.projections.core.CoreWerkzoekendeAccountProjections
import nl.dpes.core.projections.core.CoreWerkzoekendeAccountProjections.CoreWerkzoekendeAccount
import nl.dpes.core.services.sim.SimClient
import nl.dpes.core.services.sim.SimClient.UserProfile
import nl.dpes.testutils.EventBusSupport
import nl.dpes.testutils.fixtures.domain.WerkzoekendeFixtures._
import org.mockito.ArgumentMatchers.any
import org.mockito.Mockito.{reset, times, verify, when}
import org.scalatest.flatspec.AnyFlatSpec
import org.scalatest.matchers.should.Matchers
import org.scalatest.{BeforeAndAfter, BeforeAndAfterAll}
import org.scalatestplus.mockito.MockitoSugar
import org.slf4j.Logger

import scala.concurrent.Future

class SimServiceSpec extends AnyFlatSpec with Matchers with BeforeAndAfterAll with BeforeAndAfter with EventBusSupport with MockitoSugar {
  private implicit val logger: Logger = mock[Logger]

  private val client                         = mock[SimClient]
  private val werkzoekendeAccountProjections = mock[CoreWerkzoekendeAccountProjections]
  override protected val service             = new SimService(client, werkzoekendeAccountProjections)

  before {
    reset(client)
  }

  behavior of "B2C"

  it should "create a user profile on Werkzoekende.Geregistreerd" in {
    when(client.createOrUpdateUserProfile(any[UserProfile])) thenReturn Future.unit

    publish(geregistreerd)

    verify(client, times(1)).createOrUpdateUserProfile(any[UserProfile])
  }

  it should "update user profile status on Werkzoekende.Geverifieerd" in {
    when(werkzoekendeAccountProjections.findByWerkzoekendeId(any[String])) thenReturn Some(
      CoreWerkzoekendeAccount(WerkzoekendeId, None, WerkzoekendeEmailAddress, Sites.Nvb, None, None)
    )
    when(client.createOrUpdateUserProfile(any[UserProfile])) thenReturn Future.unit

    publish(geverifieerd)

    verify(client, times(1)).createOrUpdateUserProfile(any[UserProfile])
  }

  it should "subscribe a user to a mail subscription on Werkzoekende.IngeschrevenVoorEMail" in {
    when(client.subscribe(any[String], any[EMailinschrijving], any[Site])) thenReturn Future.unit

    publish(ingeschrevenVoorPersoonlijkeEMails)

    verify(client, times(1)).subscribe(
      ingeschrevenVoorPersoonlijkeEMails.werkzoekendeId,
      ingeschrevenVoorPersoonlijkeEMails.eMailinschrijving,
      ingeschrevenVoorPersoonlijkeEMails.site
    )
  }

  it should "unsubscribe a user from a mail subscription on Werkzoekende.UitgeschrevenVoorEMail" in {
    when(client.unsubscribe(any[String], any[EMailinschrijving], any[Site])) thenReturn Future.unit

    publish(uitgeschrevenVoorEMail)

    verify(client, times(1)).unsubscribe(
      uitgeschrevenVoorEMail.werkzoekendeId,
      uitgeschrevenVoorEMail.eMailinschrijving,
      uitgeschrevenVoorEMail.site
    )
  }

  it should "change the email address on the SIM profile when the email address has changed" in {
    when(werkzoekendeAccountProjections.findByWerkzoekendeId(any[String])) thenReturn Some(
      CoreWerkzoekendeAccount(WerkzoekendeId, None, WerkzoekendeEmailAddress, Sites.Nvb, None, None)
    )
    when(client.createOrUpdateUserProfile(any[UserProfile])) thenAnswer { invocation =>
      invocation.getArgument[UserProfile](0).emailAddress shouldEqual eMailadresGewijzigd.nieuwEMailadres

      Future.unit
    }

    publish(eMailadresGewijzigd)

    verify(client, times(1)).createOrUpdateUserProfile(any[UserProfile])
  }
}
