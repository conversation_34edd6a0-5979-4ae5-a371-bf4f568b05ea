package nl.dpes.core.services.mail

import nl.dpes.core.domain.EMailadres
import nl.dpes.core.services.MailService.Mail
import org.joda.time.DateTime
import org.scalatest.flatspec.AnyFlatSpec
import org.scalatest.matchers.should.Matchers
import spray.json._

class JsonMailSupportSpec extends AnyFlatSpec with Matchers with JsonMailSupport {
  val mailFormat: JsonFormat[Mail] = jsonMail()

  private val mailWithoutOptionalValues: Mail = new Mail {
    override val `type`: String        = "mail type"
    override val recipient: EMailadres = "<EMAIL>"

    override val parameters: Map[String, String] = Map(
      "parameter1" -> "value1",
      "parameter2" -> "value2",
      "parameter3" -> "value3"
    )
  }

  private val mailWithOptionalValues: Mail = new Mail {
    override val `type`: String        = "mail type"
    override val recipient: EMailadres = "<EMAIL>"

    override val parameters: Map[String, String] = Map(
      "foo" -> "baz"
    )
    override val subAccountId = Some("sub-account-id")

    override val metaData = Map(
      "foo" -> "bar"
    )
    override val sendAt = Some(DateTime.parse("2017-07-20T11:48:01.575+02:00"))
  }

  "mailFormat" should "throw an exception when trying to read from JSON" in {
    a[DeserializationException] should be thrownBy mailFormat.read(JsString("I Cannot be converted to a Mail"))
  }

  it should "convert a Mail to a RootJsonFormat" in {
    mailFormat.write(mailWithoutOptionalValues) should be(
      JsObject(
        "type"      -> JsString(mailWithoutOptionalValues.`type`),
        "recipient" -> JsString(mailWithoutOptionalValues.recipient),
        "parameters" -> JsObject(
          "parameter1" -> AnyJsonFormat.write(mailWithoutOptionalValues.parameters("parameter1")),
          "parameter2" -> AnyJsonFormat.write(mailWithoutOptionalValues.parameters("parameter2")),
          "parameter3" -> AnyJsonFormat.write(mailWithoutOptionalValues.parameters("parameter3"))
        ),
        "subAccountId" -> JsNull,
        "metaData"     -> JsObject(),
        "sendAt"       -> JsNull
      )
    )
  }

  it should "convert a Mail with used optional values to RootJsonFormat" in {
    mailFormat.write(mailWithOptionalValues) should be(
      JsObject(
        "type"      -> JsString(mailWithOptionalValues.`type`),
        "recipient" -> JsString(mailWithOptionalValues.recipient),
        "parameters" -> JsObject(
          "foo" -> JsString("baz")
        ),
        "subAccountId" -> JsString("sub-account-id"),
        "metaData" -> JsObject(
          "foo" -> JsString("bar")
        ),
        "sendAt" -> JsString("2017-07-20T11:48:01.575+02:00")
      )
    )
  }
}
