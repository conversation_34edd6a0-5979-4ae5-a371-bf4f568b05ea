package nl.dpes.core.services.mail

import nl.dpes.core.domain.{EMailadres, Sites, Url}
import nl.dpes.core.services.MailService.RegistratieMail
import org.scalatest.BeforeAndAfterAll
import org.scalatest.flatspec.AnyFlatSpec
import org.scalatest.matchers.should.Matchers
import sttp.client3.testing.SttpBackendStub
import sttp.client3.{SttpBackend, UriContext}
import sttp.model.StatusCode

import scala.concurrent.duration._
import scala.concurrent.{Await, Future}

class HttpMailServiceClientSpec extends AnyFlatSpec with Matchers with BeforeAndAfterAll {
  final val bindHost = "localhost"
  final val bindPort = 9999

  final private val patience = 10 second

  private val ndsmClientMail: RegistratieMail = RegistratieMail(
    EMailadres("<EMAIL>"),
    Sites.Iol,
    Url("http://verficatie.url"),
    "verificatie-token"
  )

  private implicit val sttpBackend: SttpBackend[Future, _] = SttpBackendStub.asynchronousFuture
    .whenRequestMatches(_.uri == uri"https://$bindHost:$bindPort/ok/mail")
    .thenRespondWithCode(StatusCode.Ok)

  it should "be successful on success" in {
    val client = new HttpMailServiceClient(s"https://$bindHost:$bindPort/ok")

    Await.result(client.send(ndsmClientMail), patience) shouldBe ()
  }

  it should "throw an exception on failure" in {
    val client = new HttpMailServiceClient(s"https://$bindHost:$bindPort/unprocessable")

    an[Exception] should be thrownBy Await.result(client.send(ndsmClientMail), patience)
  }
}
