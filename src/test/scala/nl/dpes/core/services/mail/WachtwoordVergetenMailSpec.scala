package nl.dpes.core.services.mail

import nl.dpes.core.services.MailService.WachtwoordVergetenMail
import org.scalatest.flatspec.AnyFlatSpec
import org.scalatest.matchers.should.Matchers

class WachtwoordVergetenMailSpec extends AnyFlatSpec with Matchers {
  val wachtwoordVergetenMail = WachtwoordVergetenMail("<EMAIL>", "itbanen.nl", "http://itbanen.nl/herstel", "some-token")

  "wachtwoordVergetenMail" should "add the token as query parameter to the herstelUrl" in {
    wachtwoordVergetenMail.parameters("herstelurl") should be("http://itbanen.nl/herstel?token=some-token")
  }
}
