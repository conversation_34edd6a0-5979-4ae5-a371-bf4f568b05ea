package nl.dpes.core.services.mail

import nl.dpes.core.domain.Sites
import nl.dpes.core.services.MailService.RegistratieMail
import org.scalatest.flatspec.AnyFlatSpec
import org.scalatest.matchers.should.Matchers

class RegistratieMailSpec extends AnyFlatSpec with Matchers with JsonMailSupport {

  private val registratieMail: RegistratieMail = RegistratieMail(
    "<EMAIL>",
    Sites.Iol,
    "http://verificatie.url",
    "verificatie-token"
  )

  it should "have a type composed of 'saved search verification' and Site" in {
    registratieMail.`type` should be("registration mail iol")
  }

  it should "have a 'verificationurl' parameter containing the verification url and the verification token" in {
    registratieMail.parameters("verificationurl") should be("http://verificatie.url?token=verificatie-token")
  }

  it should "have an 'emailaddress' parameter containing the email address" in {
    registratieMail.parameters("emailaddress") should be("<EMAIL>")
  }
}
