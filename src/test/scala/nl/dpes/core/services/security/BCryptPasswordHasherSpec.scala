package nl.dpes.core.services.security

import nl.dpes.core.services.security.PasswordHasher.HashedPassword
import org.mockito.ArgumentMatchers.any
import org.mockito.Mockito.{atLeastOnce, verify}
import org.scalatest.flatspec.AnyFlatSpec
import org.scalatest.matchers.should.Matchers
import org.scalatestplus.mockito.MockitoSugar.mock
import org.slf4j.Logger

class BCryptPasswordHasherSpec extends AnyFlatSpec with Matchers {
  private val logger = mock[Logger]
  private val hasher = new BCryptPasswordHasher(logger)

  it should "hash a password" in {
    val password = "foo"

    val hashed = hasher.hashPassword(password)
    hashed shouldBe a[HashedPassword]

    hashed.hash should startWith("$2a$")
    hashed.salt shouldNot be(empty)
  }

  it should "verify a password is correct" in {
    val password = "foo"

    val hashed = hasher.hashPassword(password)
    hasher.verifyHashedPassword(hashed, password) shouldBe true
  }

  it should "verify a password is incorrect" in {
    val password = "foo"

    val hashed = hasher.hashPassword(password)
    hasher.verifyHashedPassword(hashed, "bar") shouldBe false
  }

  it should "verify a password is incorrect when the hash is corrupted" in {
    val hashed = HashedPassword("corrupted", "corrupted")
    hasher.verifyHashedPassword(hashed, "bar") shouldBe false
    verify(logger, atLeastOnce()).error(any)
  }
}
