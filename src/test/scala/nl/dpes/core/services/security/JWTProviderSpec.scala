package nl.dpes.core.services.security

import com.auth0.jwt.JWT
import com.auth0.jwt.exceptions.TokenExpiredException
import org.scalatest.flatspec.AnyFlatSpec
import org.scalatest.BeforeAndAfter
import org.scalatest.matchers.should.Matchers

import scala.concurrent.duration._

class JWTProviderSpec extends AnyFlatSpec with Matchers with BeforeAndAfter {
  private var jwt: JWTProvider = _

  private val rsaKeys = RsaKeys
    .fromStrings(
      "MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQDaYxeZR0jlmUbJ\nBLqhduKr3lDe2jTMaGNFCbHonw2K1s02iNOarA0+43xqYgdlJOEOy4eaF2E19sBy\nxZjaElKpPbwHRaz6qmC76kBo1ohk8M35ml6gRsF1lRvjak2PK1PIkivIp7eyu+hP\nu8+GMxwLa5WNhI+Ij3/islVn+LIQy2rpt6dQc/83wGURxT3q/ZQ2kxaRqYPhQe6J\ndm+lrDgSPhbO5aKtO1/ZhzkT/77uojtuDvfR4OBnl3uN0sJ5u77MzBm1twzkwDwC\nwTlzz8PASMbHITFHtYF/zHk7qBet0VhB8U4nxodl1J23fOo6/A6HrZTmpSR6Zivn\nyWf8hZd5AgMBAAECggEAWnieuCMt8eAT0Qcige2MbgMFoazoHp5IMbuYdnQd2hyu\nmx8H+pW4TwtS6Yd+5MnoaoaqwI1a+k4k3+c4MW9Ry0cF9EVLKfUXagHbeRMXIioY\nSoVHGkL1tk51ZTsOsncSmI7lkJTapzLtRMrbaA8YWSrSo/P8umgoZLPYaeA/dHDs\nmgKgJtR3tQRoLKG39wyBh+QTwo/f8LJuJ3BBldk7a1EvX+nSuO7aU2XVBcOVu8zB\nCejE3EnLcNFQU+HcGPWMDJPk1D/vTp08zUeLjRkouyJPTzWjLGIh7FbOR3fzVCbx\nzHYXyercurrA5rhMqVDfcLKwAWSiFfJTIfQis816YQKBgQD83lNGg1GNcBiAn/5A\nPV91F9VoKG5d4SwzXA44VVHEqn8BS4GWMtgmeia2mLvWZwHfNOHcYNq77xJavGrQ\nqoyFwFcojyHrYbJBK7osyxNnmZipXveMPlZXmExPeGiYXRHfrnvkbg31JtbxC+rJ\n4V+D5tKtQlggVPLOrVGUSdkI3QKBgQDdF3Mkf3h9RPerju7e0aa3Jq8WnYwQcKVd\nstMwX4HyX0+k+d2VdPXfV1eHe5SDUttIwWQIqumX/YUPkgDFhcXaRCVFjwMUD5Sr\nqxqA1qFDKUfTKq0QVuxVT7kXFyoy637mqCp8rYhOmwYWtvzQIYNDsBdgXwdXD3Bz\nKGiopTBRTQKBgE/+FOHtkNLpbxbhhhDUWWo1S/FTOPbB0LW+nVD8zI0mUl8af7gA\nBZjGc5C36FP+6fyuQfTxPsDM9EV37WJtUV7OZcQS42wOz9EGADNDWrQfRsftnuni\nnnW/XASX1p68cncMP20X05qF2uEOIAwVG54iWussXax8swLmmTQL2HiRAoGAKzqA\niuxZlLS9cE/cKLj5TepEZbmcP0KnMrDaViT2dXtpSSqJxsU9pdOgC5+AJEU5Fvqp\nIoC4d71cHPVBLV3Al7KnTvhtc6iwOviTkAlnMoiJwKIhgIZeYzfZSsh+ucIN5++N\njx8m/aeWAy74M4G59qwhhfhOqRkdtYZ7IC5cSikCgYEAiwxx/IOMJFpyZQakK2Nh\nICsZL1l43FB5ZCba6vPA/cVBkLo+pvkb5w/3yyDhKYSD3jf1T8SibT073ONGCPb1\n2EDjy/Vzf7oGQTprWeJBeNt7Ufg2A1YTNdxe+NSY9WZdeebzylcbc1Tc+rDpxBX5\nWWloe6U3Pd5rBpwTZAlLlrM=",
      "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA2mMXmUdI5ZlGyQS6oXbi\nq95Q3to0zGhjRQmx6J8NitbNNojTmqwNPuN8amIHZSThDsuHmhdhNfbAcsWY2hJS\nqT28B0Ws+qpgu+pAaNaIZPDN+ZpeoEbBdZUb42pNjytTyJIryKe3srvoT7vPhjMc\nC2uVjYSPiI9/4rJVZ/iyEMtq6benUHP/N8BlEcU96v2UNpMWkamD4UHuiXZvpaw4\nEj4WzuWirTtf2Yc5E/++7qI7bg730eDgZ5d7jdLCebu+zMwZtbcM5MA8AsE5c8/D\nwEjGxyExR7WBf8x5O6gXrdFYQfFOJ8aHZdSdt3zqOvwOh62U5qUkemYr58ln/IWX\neQIDAQAB"
    )
    .right
    .get
  private val rsaKeyId = "123456"
  private val issuer   = "issuer"

  before {
    jwt = new JWTProvider(issuer, rsaKeys, rsaKeyId)
  }

  it should "use RS256 encryption algorithm while signing a JWT" in {
    val token = jwt.createAndSignJWTToken("123456", List("role"), 15 minutes)

    JWT.decode(token).getAlgorithm shouldBe "RS256"
  }

  it should "add RSA key ID to the header while creating a JWT" in {
    val token = jwt.createAndSignJWTToken("123456", List("role"), 15 minutes)

    JWT.decode(token).getKeyId shouldBe rsaKeyId
  }

  it should "add issuer claim while creating a JWT" in {
    val token = jwt.createAndSignJWTToken("123456", List("role"), 15 minutes)

    JWT.decode(token).getIssuer shouldBe issuer
  }

  it should "add user ID claim while creating a JWT" in {
    val userId = "123456"

    val token = jwt.createAndSignJWTToken(userId, List("role"), 15 minutes)

    JWT.decode(token).getClaim("uid").asString shouldBe userId
  }

  it should "add role claim while creating a JWT" in {
    val role = "role"

    val token = jwt.createAndSignJWTToken("123456", List(role), 15 minutes)

    JWT.decode(token).getClaim("rol").asString shouldBe role
  }

  it should "check expiration dates of a token" in {
    val role = "admin"

    val token = jwt.createAndSignJWTToken("123456", List(role), -1 seconds)

    jwt.checkHasRole(role)(token).left.get shouldBe a[TokenExpiredException]
  }

  it should "retrieve claims from token" in {
    val userId = "123456"
    val role   = "admin"

    val token = jwt.createAndSignJWTToken(userId, List(role), 15 minutes)

    jwt.getClaimFromToken(token, "uid") shouldEqual Right(userId)
    jwt.getClaimFromToken(token, "rol") shouldEqual Right(role)
  }

  it should "be able to check whether a token has a role" in {
    val admin  = "admin"
    val viewer = "viewer"
    val roles  = s"$admin,$viewer"

    val token = jwt.createAndSignJWTToken("123456", List(roles), 15 minutes)

    jwt.checkHasRole(admin)(token) shouldEqual Right(true)
    jwt.checkHasRole(viewer)(token) shouldEqual Right(true)
  }
}
