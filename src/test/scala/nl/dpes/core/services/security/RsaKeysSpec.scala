package nl.dpes.core.services.security

import org.scalatest.EitherValues
import org.scalatest.flatspec.AnyFlatSpec
import org.scalatest.matchers.should.Matchers

import java.security.interfaces.{RSAPrivateKey, RSAPublicKey}

class RsaKeysSpec extends AnyFlatSpec with Matchers with EitherValues {

  val privateKeyString =
    "MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQDaYxeZR0jlmUbJ\nBLqhduKr3lDe2jTMaGNFCbHonw2K1s02iNOarA0+43xqYgdlJOEOy4eaF2E19sBy\nxZjaElKpPbwHRaz6qmC76kBo1ohk8M35ml6gRsF1lRvjak2PK1PIkivIp7eyu+hP\nu8+GMxwLa5WNhI+Ij3/islVn+LIQy2rpt6dQc/83wGURxT3q/ZQ2kxaRqYPhQe6J\ndm+lrDgSPhbO5aKtO1/ZhzkT/77uojtuDvfR4OBnl3uN0sJ5u77MzBm1twzkwDwC\nwTlzz8PASMbHITFHtYF/zHk7qBet0VhB8U4nxodl1J23fOo6/A6HrZTmpSR6Zivn\nyWf8hZd5AgMBAAECggEAWnieuCMt8eAT0Qcige2MbgMFoazoHp5IMbuYdnQd2hyu\nmx8H+pW4TwtS6Yd+5MnoaoaqwI1a+k4k3+c4MW9Ry0cF9EVLKfUXagHbeRMXIioY\nSoVHGkL1tk51ZTsOsncSmI7lkJTapzLtRMrbaA8YWSrSo/P8umgoZLPYaeA/dHDs\nmgKgJtR3tQRoLKG39wyBh+QTwo/f8LJuJ3BBldk7a1EvX+nSuO7aU2XVBcOVu8zB\nCejE3EnLcNFQU+HcGPWMDJPk1D/vTp08zUeLjRkouyJPTzWjLGIh7FbOR3fzVCbx\nzHYXyercurrA5rhMqVDfcLKwAWSiFfJTIfQis816YQKBgQD83lNGg1GNcBiAn/5A\nPV91F9VoKG5d4SwzXA44VVHEqn8BS4GWMtgmeia2mLvWZwHfNOHcYNq77xJavGrQ\nqoyFwFcojyHrYbJBK7osyxNnmZipXveMPlZXmExPeGiYXRHfrnvkbg31JtbxC+rJ\n4V+D5tKtQlggVPLOrVGUSdkI3QKBgQDdF3Mkf3h9RPerju7e0aa3Jq8WnYwQcKVd\nstMwX4HyX0+k+d2VdPXfV1eHe5SDUttIwWQIqumX/YUPkgDFhcXaRCVFjwMUD5Sr\nqxqA1qFDKUfTKq0QVuxVT7kXFyoy637mqCp8rYhOmwYWtvzQIYNDsBdgXwdXD3Bz\nKGiopTBRTQKBgE/+FOHtkNLpbxbhhhDUWWo1S/FTOPbB0LW+nVD8zI0mUl8af7gA\nBZjGc5C36FP+6fyuQfTxPsDM9EV37WJtUV7OZcQS42wOz9EGADNDWrQfRsftnuni\nnnW/XASX1p68cncMP20X05qF2uEOIAwVG54iWussXax8swLmmTQL2HiRAoGAKzqA\niuxZlLS9cE/cKLj5TepEZbmcP0KnMrDaViT2dXtpSSqJxsU9pdOgC5+AJEU5Fvqp\nIoC4d71cHPVBLV3Al7KnTvhtc6iwOviTkAlnMoiJwKIhgIZeYzfZSsh+ucIN5++N\njx8m/aeWAy74M4G59qwhhfhOqRkdtYZ7IC5cSikCgYEAiwxx/IOMJFpyZQakK2Nh\nICsZL1l43FB5ZCba6vPA/cVBkLo+pvkb5w/3yyDhKYSD3jf1T8SibT073ONGCPb1\n2EDjy/Vzf7oGQTprWeJBeNt7Ufg2A1YTNdxe+NSY9WZdeebzylcbc1Tc+rDpxBX5\nWWloe6U3Pd5rBpwTZAlLlrM="

  val publicKeyString =
    "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA2mMXmUdI5ZlGyQS6oXbi\nq95Q3to0zGhjRQmx6J8NitbNNojTmqwNPuN8amIHZSThDsuHmhdhNfbAcsWY2hJS\nqT28B0Ws+qpgu+pAaNaIZPDN+ZpeoEbBdZUb42pNjytTyJIryKe3srvoT7vPhjMc\nC2uVjYSPiI9/4rJVZ/iyEMtq6benUHP/N8BlEcU96v2UNpMWkamD4UHuiXZvpaw4\nEj4WzuWirTtf2Yc5E/++7qI7bg730eDgZ5d7jdLCebu+zMwZtbcM5MA8AsE5c8/D\nwEjGxyExR7WBf8x5O6gXrdFYQfFOJ8aHZdSdt3zqOvwOh62U5qUkemYr58ln/IWX\neQIDAQAB"

  it should "create RSA keys from strings" in {
    val maybeRsaKeys = RsaKeys.fromStrings(privateKeyString, publicKeyString)

    maybeRsaKeys.right.value shouldBe a[RsaKeys]
  }

  it should "create RSA private key" in {
    val maybeRsaKeys = RsaKeys.fromStrings(privateKeyString, publicKeyString)

    maybeRsaKeys.right.value.privateKey shouldBe a[RSAPrivateKey]
    maybeRsaKeys.right.value.privateKey.getPrivateExponent.toString shouldBe "11420927384419151242114704356258831259724110123866112653571079396238427416093804783132979082909922344572464819347007810142453115879025091470813826230135989091522345342748853584824277062865786115721362541822953787708064318826420105417029356001833874546677211782775391180266972259952733212261432736464350174682677817962360439252532200628851773967331491166504846539212255811098999937764104895853125678679702884842735657474430522188051752951752507941683155475868579583863295882094750961060745890763794954975185337515938488383078454935459174155586390757386882065353918815601055332577547566555915077297813100252617377675873"
    maybeRsaKeys.right.value.privateKey.getAlgorithm shouldBe "RSA"
    maybeRsaKeys.right.value.privateKey.getFormat shouldBe "PKCS#8"
    maybeRsaKeys.right.value.privateKey.getModulus.toString shouldBe "27568814659030494105137067381073113232727035181871581030463640161741356079872548216286779011295306839640722904808281799385117858392768597411518443080825868032821360837117186644148384746557459472045338375817713531676737063091237364593585742331203927667240678770082902828035232449374669485524033784518015373782235446911635674576528675495571023709532768848793102626362153521041457552119557765497797910586852926676660316862132668644625533376606494585096879514779070689391201654849260958686342373631004583695279348671772412597924093973915320455395559609096062598705642823053331078126104548610667426682479987846239668770681"
  }

  it should "be able to create RSA private key from string that contains header and footer" in {
    val maybeRsaKeys = RsaKeys.fromStrings(
      s"-----BEGIN PRIVATE KEY-----$privateKeyString-----END PRIVATE KEY-----",
      publicKeyString
    )

    maybeRsaKeys.right.value.privateKey shouldBe a[RSAPrivateKey]
  }

  it should "create RSA public key" in {
    val maybeRsaKeys = RsaKeys.fromStrings(privateKeyString, publicKeyString)

    maybeRsaKeys.right.value.publicKey shouldBe a[RSAPublicKey]
    maybeRsaKeys.right.value.publicKey.getPublicExponent.toString shouldBe "65537"
    maybeRsaKeys.right.value.publicKey.getAlgorithm shouldBe "RSA"
    maybeRsaKeys.right.value.publicKey.getFormat shouldBe "X.509"
    maybeRsaKeys.right.value.publicKey.getModulus.toString shouldBe "27568814659030494105137067381073113232727035181871581030463640161741356079872548216286779011295306839640722904808281799385117858392768597411518443080825868032821360837117186644148384746557459472045338375817713531676737063091237364593585742331203927667240678770082902828035232449374669485524033784518015373782235446911635674576528675495571023709532768848793102626362153521041457552119557765497797910586852926676660316862132668644625533376606494585096879514779070689391201654849260958686342373631004583695279348671772412597924093973915320455395559609096062598705642823053331078126104548610667426682479987846239668770681"
  }

  it should "be able to create RSA public key from string that contains header and footer" in {
    val maybeRsaKeys = RsaKeys.fromStrings(
      privateKeyString,
      s"-----BEGIN PUBLIC KEY-----$publicKeyString-----END PUBLIC KEY-----"
    )

    maybeRsaKeys.right.value.publicKey shouldBe a[RSAPublicKey]
  }

  it should "provide an error that happened while creating a private key" in {
    val maybeRsaKeys = RsaKeys.fromStrings("Oops", publicKeyString)

    maybeRsaKeys.left.value shouldBe a[Throwable]
  }

  it should "provide an error that happened while creating a public key" in {
    val maybeRsaKeys = RsaKeys.fromStrings(privateKeyString, "Oops")

    maybeRsaKeys.left.value shouldBe a[Throwable]
  }
}
