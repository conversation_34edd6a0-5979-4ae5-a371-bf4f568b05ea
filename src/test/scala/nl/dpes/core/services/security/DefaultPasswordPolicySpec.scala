package nl.dpes.core.services.security

import nl.dpes.core.services.security.PasswordPolicy.TooShort
import org.scalatest.flatspec.AnyFlatSpec
import org.scalatest.matchers.should.Matchers

class DefaultPasswordPolicySpec extends AnyFlatSpec with Matchers {
  it should "pass validation if password adheres to policy" in {
    val password = "fine"
    val length   = password.length

    val policy = new DefaultPasswordPolicy(length)
    policy.validate(password) shouldBe Right(())
  }

  it should "fail validation with TooShort error if password is too short according to policy" in {
    val password = "tooshort"
    val length   = password.length + 1

    val policy = new DefaultPasswordPolicy(length)
    policy.validate(password) shouldBe Left(TooShort(length))
  }
}
