package nl.dpes.core.services.ndsm

import nl.dpes.core.domain.Sites.Nvb
import nl.dpes.core.services.Filter
import nl.dpes.core.services.ndsm.JobSeekerServiceClient._
import nl.dpes.testutils.fixtures.services.JobSeekerServiceFixtures._
import org.scalatest.BeforeAndAfterAll
import org.scalatest.flatspec.AnyFlatSpec
import org.scalatest.matchers.should.Matchers
import org.slf4j.{Logger, LoggerFactory}
import sttp.client3.testing.SttpBackendStub
import sttp.client3.{basicRequest, SttpBackend, UriContext}
import sttp.model.Header

import scala.concurrent.duration._
import scala.concurrent.{Await, Future}

class HttpJobSeekerServiceClientSpec extends AnyFlatSpec with Matchers with BeforeAndAfterAll {
  implicit val logger: Logger = LoggerFactory.getLogger("test-jss")

  final val bindHost = "localhost"
  final val bindPort = 9999

  final private val patience = 10 seconds

  private val ndsmClientJobSeeker = JobSeeker(
    NdsmJobSeekerId,
    Some(LegacyJobSeekerId),
    NdsmJobSeekerSite,
    Some(NdsmJobSeekerFirstName),
    Some(NdsmJobSeekerLastName),
    NdsmJobSeekerEmailAddress
  )

  private val searchParameters = SearchParameters(
    Some(SearchTerms(Some("PHP"))),
    Some("Amsterdam"),
    None,
    Some(Seq("Afgelopen week"))
  )

  val apiKey = "apikey"

  it should "return some JobSeeker when a JobSeeker was found" in {
    val baseUrl = "https://$bindHost:$bindPort/found"
    val request = basicRequest
      .get(uri"$baseUrl/job-seekers/${ndsmClientJobSeeker.id}")
      .headers(getHeaders(Nvb, apiKey), replaceExisting = false)

    implicit val sttpBackend: SttpBackend[Future, _] = SttpBackendStub.asynchronousFuture
      .whenRequestMatches(r => r.uri == request.uri)
      .thenRespond(Right(Some(ndsmClientJobSeeker)))
    val client = new HttpJobSeekerServiceClient(baseUrl, apiKey)

    Await.result(client.findByUuid(ndsmClientJobSeeker.id), patience) should be(Some(ndsmClientJobSeeker))
  }

  it should "return None when a JobSeeker does not exist" in {
    implicit val sttpBackend: SttpBackend[Future, _] = SttpBackendStub.asynchronousFuture.whenAnyRequest
      .thenRespond(Right(None))
    val client = new HttpJobSeekerServiceClient(s"http://$bindHost:$bindPort/not-found", apiKey)

    Await.result(client.findByUuid(ndsmClientJobSeeker.id), patience) should be(None)
  }

  it should "return None when a JobSeeker is gone" in {
    implicit val sttpBackend: SttpBackend[Future, _] = SttpBackendStub.asynchronousFuture.whenAnyRequest
      .thenRespond(Right(None))
    val client = new HttpJobSeekerServiceClient(s"http://$bindHost:$bindPort/gone", apiKey)

    Await.result(client.findByUuid(ndsmClientJobSeeker.id), patience) should be(None)
  }

  it should "fail when an unhandled status is returned" in {
    val uri = s"https://$bindHost:$bindPort/unhandled"
    implicit val sttpBackend: SttpBackend[Future, _] = SttpBackendStub.asynchronousFuture
      .whenRequestMatches(r => r.uri == uri"$uri" && r.headers == getHeaders(Nvb, apiKey))
      .thenRespond(Left("Oops"))
    val client = new HttpJobSeekerServiceClient(uri, apiKey)

    a[RuntimeException] should be thrownBy Await.result(client.findByUuid(ndsmClientJobSeeker.id), patience)
  }

  "search" should "return a list of JobSeekers" in {
    val baseUrl = s"https://$bindHost:$bindPort/search-found"
    val request = basicRequest
      .get(uri"$baseUrl/search")
      .headers(getHeaders(Nvb, apiKey), replaceExisting = true)

    implicit val sttpBackend: SttpBackend[Future, _] = SttpBackendStub.asynchronousFuture
      .whenRequestMatches(r => r.uri == request.uri)
      .thenRespond(Right(SearchResult(List(ndsmClientJobSeeker), 1, locationNotFound = false, invalidQuery = false)))

    val client = new HttpJobSeekerServiceClient(baseUrl, apiKey)

    val searchResult = Await.result(client.search(searchParameters), patience)
    searchResult should be(a[SearchResult[_]])
  }

  "retrieve" should "return a list of jobseekers" in {
    val baseUrl = s"https://$bindHost:$bindPort/retrieve-found"
    val filter  = Filter(Map("found" -> "yes"))

    val request = basicRequest
      .get(uri"$baseUrl/job-seekers".withParams(filter.asMap))
      .headers(getHeaders(Nvb, apiKey), replaceExisting = false)

    implicit val sttpBackend: SttpBackend[Future, _] = SttpBackendStub.asynchronousFuture
      .whenRequestMatches(r => r.uri == request.uri && r.headers == request.headers)
      .thenRespond(Right(List(ndsmClientJobSeeker)))

    val client = new HttpJobSeekerServiceClient(baseUrl, apiKey)

    val searchResult = Await.result(client.retrieveJobSeekers(filter, Nvb), patience)
    searchResult should be(List(ndsmClientJobSeeker))
  }

  private def getHeaders(siteName: String, apiKey: String) = List(
    Header.accept(sttp.model.MediaType.ApplicationJson),
    Header("X-API-Key", apiKey),
    Header("X-NDSM-Token", siteName),
    Header("Accept-Language", "nl_NL")
  )
}
