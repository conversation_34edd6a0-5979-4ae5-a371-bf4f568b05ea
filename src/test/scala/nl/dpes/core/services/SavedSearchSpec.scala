package nl.dpes.core.services

import java.util.concurrent.CompletableFuture
import nl.dpes.b2b.domain.GenericError
import nl.dpes.b2b.salesforce.service.RecruiterService
import nl.dpes.core.domain.Zoekopdracht.{<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, VerwijderDoorRecruiter}
import nl.dpes.core.domain._
import nl.dpes.core.domain.exceptions.AccountNietGevonden
import nl.dpes.core.projections.core.CoreRecruiterAccountProjections.CoreRecruiterAccount
import nl.dpes.core.projections.core.CoreZoekopdrachtProjections.CoreZoekopdracht
import nl.dpes.core.projections.core.{CoreRecruiterAccountProjections, CoreZoekopdrachtProjections}
import nl.dpes.core.projections.index.IndexOpgeslagenZoekopdrachtProjections
import nl.dpes.core.services.MailService.OpgeslagenZoekopdrachtMail
import nl.dpes.testutils.fixtures.domain.ZoekopdrachtFixtures._
import nl.dpes.testutils.fixtures.services.MailServiceFixtures.{jobSeekers, opgeslagenZoekopdracht, recruiter, _}
import org.axonframework.commandhandling.gateway.CommandGateway
import org.mockito.ArgumentMatchers.{any, eq => eqTo}
import org.mockito.Mockito._
import org.scalatest.flatspec.AnyFlatSpec
import org.scalatest.BeforeAndAfter
import org.scalatest.matchers.should.Matchers
import org.scalatestplus.mockito.MockitoSugar
import org.slf4j.Logger

import scala.concurrent.ExecutionContext.Implicits.global
import scala.concurrent.duration._
import scala.concurrent.{Await, Future}

class SavedSearchSpec extends AnyFlatSpec with MockitoSugar with BeforeAndAfter with Matchers {

  private lazy val commandGateway: CommandGateway    = mock[CommandGateway]
  private lazy val zoekopdrachtProjections           = mock[CoreZoekopdrachtProjections]
  private lazy val recruiterProjections              = mock[CoreRecruiterAccountProjections]
  private lazy val identifierService                 = mock[IdentifierService]
  private lazy val mailService                       = mock[MailService]
  private lazy val jobSeekerService                  = mock[JobSeekerService]
  private lazy val recruiterService                  = mock[RecruiterService]
  private lazy val opgeslagenZoekopdrachtProjections = mock[IndexOpgeslagenZoekopdrachtProjections]
  private implicit val logger                        = mock[Logger]

  private lazy val zoekopdrachtService = new SavedSearch(
    zoekopdrachtProjections,
    opgeslagenZoekopdrachtProjections,
    recruiterProjections,
    identifierService,
    commandGateway,
    mailService,
    jobSeekerService,
    recruiterService
  )
  private val patience: FiniteDuration = 10 seconds

  before {
    reset(commandGateway)
    reset(zoekopdrachtProjections)
    reset(commandGateway)
    reset(mailService)
    reset(jobSeekerService)
    reset(recruiterService)
    reset(opgeslagenZoekopdrachtProjections)
  }

  behavior of "Zoekopdracht service"

  it should "create a Zoekopdracht if it does not exists and add Opgeslagen Zoekopdracht" in {
    when(recruiterProjections.findByRecruiterId(RecruiterId)) thenReturn
    Some(CoreRecruiterAccount(RecruiterId, "<EMAIL>", "site"))
    when(zoekopdrachtProjections.findByParameters(any[Zoekparameters])) thenReturn None

    val eventualMaakAanDoorRecruiter = new CompletableFuture[Unit]()
    when(commandGateway.send[Unit](any[MaakAanDoorRecruiter])) thenReturn eventualMaakAanDoorRecruiter
    eventualMaakAanDoorRecruiter.complete(())

    Await.result(zoekopdrachtService.saveByRecruiter(RecruiterId, Parameters, Naam, Frequentie), patience)

    verify(commandGateway, times(1)).send(any[MaakAanDoorRecruiter])
    verify(commandGateway, times(1)).send(any[SlaOpDoorRecruiter])
  }

  it should "fail the future with AccountNietGevonden exception if account is not found" in {
    when(recruiterProjections.findByRecruiterId(RecruiterId)) thenReturn None

    a[AccountNietGevonden] shouldBe
    thrownBy(Await.result(zoekopdrachtService.saveByRecruiter(RecruiterId, Parameters, Naam, Frequentie), patience))
  }

  it should "add a Opgeslagen Zoekopdracht to a existing zoekopdracht" in {
    when(recruiterProjections.findByRecruiterId(RecruiterId)) thenReturn
    Some(CoreRecruiterAccount(RecruiterId, "<EMAIL>", "site"))
    when(zoekopdrachtProjections.findByParameters(any[Zoekparameters])) thenReturn Some(CoreZoekopdracht(ZoekopdrachtId, ""))

    val eventualMaakAanDoorRecruiter = new CompletableFuture[Unit]()
    when(commandGateway.send[Unit](any[MaakAanDoorRecruiter])) thenReturn eventualMaakAanDoorRecruiter
    eventualMaakAanDoorRecruiter.complete(())

    Await.result(zoekopdrachtService.saveByRecruiter(RecruiterId, Parameters, Naam, Frequentie), patience)

    verify(commandGateway, times(1)).send(any[SlaOpDoorRecruiter])
  }

  "update" should "update the zoekopdracht frequency" in {
    when(recruiterProjections.findByRecruiterId(RecruiterId)) thenReturn
    Some(CoreRecruiterAccount(RecruiterId, "<EMAIL>", "site"))

    val eventualMaakAanDoorRecruiter = new CompletableFuture[Unit]()
    when(commandGateway.send[Unit](any[MaakAanDoorRecruiter])) thenReturn eventualMaakAanDoorRecruiter
    eventualMaakAanDoorRecruiter.complete(())

    Await.result(zoekopdrachtService.editByRecruiter(RecruiterId, ZoekopdrachtId, Naam, Frequentie), patience)

    verify(commandGateway, times(1)).send(any[SlaOpDoorRecruiter])
  }

  it should "not update and fail the future with AccountNietGevonden exception if account is not found" in {
    when(recruiterProjections.findByRecruiterId(RecruiterId)) thenReturn None

    a[AccountNietGevonden] shouldBe
    thrownBy(Await.result(zoekopdrachtService.editByRecruiter(RecruiterId, ZoekopdrachtId, Naam, Frequentie), patience))
  }

  it should "delete an existing Opgeslagen Zoekopdracht" in {
    when(commandGateway.sendAndWait[Unit](any[VerwijderDoorRecruiter])) thenReturn (())

    Await.result(zoekopdrachtService.deleteByRecruiter(ZoekopdrachtId, RecruiterId), patience)

    verify(commandGateway, times(1)).sendAndWait(any[VerwijderDoorRecruiter])
  }

  it should "search for jobseekers and send the result as mail" in {

    when(opgeslagenZoekopdrachtProjections.findBySavedSearchId(RecruiterId, ZoekopdrachtId)) thenReturn Some(opgeslagenZoekopdracht)

    when(recruiterProjections.findByRecruiterId(RecruiterId)) thenReturn
    Some(recruiter)

    when(jobSeekerService.search(opgeslagenZoekopdracht.zoekparameters.copy(wijzigingsdatum = Some("Afgelopen 24 uur")))) thenReturn Future(
      searchResults
    )

    when(
      mailService.send(
        OpgeslagenZoekopdrachtMail(
          EMailadres(recruiter.eMailadres),
          Sites.stringToSite(recruiter.site),
          recruiter,
          searchResults.result.toVector,
          opgeslagenZoekopdracht
        )
      )
    ) thenReturn Future.unit

    when(recruiterService.getRecruiterEmailById(RecruiterId)).thenReturn(Future.successful(Right(recruiter.eMailadres)))

    Await.result(zoekopdrachtService.searchAndSend(ZoekopdrachtId, RecruiterId), patience)

    verify(mailService, times(1)).send(
      OpgeslagenZoekopdrachtMail(EMailadres(recruiter.eMailadres), Sites.Nvb, recruiter, jobSeekers, opgeslagenZoekopdracht)
    )
  }

  it should "do nothing if the recruiter email is not available but the recruiter exists" in {

    when(opgeslagenZoekopdrachtProjections.findBySavedSearchId(RecruiterId, ZoekopdrachtId)) thenReturn Some(opgeslagenZoekopdracht)

    when(recruiterProjections.findByRecruiterId(RecruiterId)) thenReturn
    Some(recruiter)

    when(recruiterService.getRecruiterEmailById(RecruiterId)).thenReturn(Future.successful(Left(GenericError(404, "not found"))))
    when(recruiterService.isRecruiterDeleted(RecruiterId)).thenReturn(Future.successful(Right(false)))
    Await.result(zoekopdrachtService.searchAndSend(ZoekopdrachtId, RecruiterId), patience)

    verify(mailService, never).send(any())
    verify(commandGateway, timeout(patience.toMillis).times(0)).send(any[VerwijderDoorRecruiter])
  }

  it should "delete an existing Opgeslagen Zoekopdracht if the recruiter is not available anymore" in {
    when(opgeslagenZoekopdrachtProjections.findBySavedSearchId(RecruiterId, ZoekopdrachtId)) thenReturn Some(opgeslagenZoekopdracht)

    when(recruiterProjections.findByRecruiterId(RecruiterId)) thenReturn
    Some(recruiter)
    when(commandGateway.sendAndWait[Unit](any[VerwijderDoorRecruiter])) thenReturn (())

    when(recruiterService.getRecruiterEmailById(RecruiterId)).thenReturn(Future.successful(Left(GenericError(404, "not found"))))
    when(recruiterService.isRecruiterDeleted(RecruiterId)).thenReturn(Future.successful(Right(true)))

    Await.result(zoekopdrachtService.searchAndSend(ZoekopdrachtId, RecruiterId), patience)

    verify(mailService, never).send(any())
    verify(commandGateway, timeout(patience.toMillis).times(1)).sendAndWait(any[VerwijderDoorRecruiter])
  }
  it should "do nothing if the recruiter email is not available and we can't verify if it is in fact deleted" in {

    when(opgeslagenZoekopdrachtProjections.findBySavedSearchId(RecruiterId, ZoekopdrachtId)) thenReturn Some(opgeslagenZoekopdracht)

    when(recruiterProjections.findByRecruiterId(RecruiterId)) thenReturn
    Some(recruiter)

    when(recruiterService.getRecruiterEmailById(RecruiterId)).thenReturn(Future.successful(Left(GenericError(404, "not found"))))
    when(recruiterService.isRecruiterDeleted(RecruiterId)).thenReturn(Future.successful(Left(GenericError(404, "not found"))))

    Await.result(zoekopdrachtService.searchAndSend(ZoekopdrachtId, RecruiterId), patience)

    verify(mailService, never).send(any())
    verify(commandGateway, timeout(patience.toMillis).times(0)).send(any[VerwijderDoorRecruiter])
  }
}
