package nl.dpes.core.services

import nl.dpes.core.domain.Werkzoekende
import nl.dpes.core.services.NotifyingMessageMonitor.Actions
import nl.dpes.core.services.notifier.Notifier
import org.axonframework.commandhandling.CommandMessage
import org.axonframework.eventhandling.DomainEventMessage
import org.axonframework.eventhandling.tokenstore.UnableToClaimTokenException
import org.scalatest.BeforeAndAfter
import org.mockito.Mockito._
import org.mockito.ArgumentMatchers._
import org.axonframework.messaging.Message
import org.scalatest.flatspec.AnyFlatSpec
import org.scalatestplus.mockito.MockitoSugar
import org.slf4j.Logger

import scala.concurrent.ExecutionContext.Implicits.global
import scala.concurrent.Future

class NotifyingMessageMonitorSpec extends AnyFlatSpec with MockitoSugar with BeforeAndAfter {
  private implicit val logger: Logger = mock[Logger]
  private val notifier                = mock[Notifier]
  private val monitor                 = new NotifyingMessageMonitor(notifier, "test-node")

  before {
    reset(notifier)
  }

  it should "notify on failed command" in {
    when(notifier.notify(any[Notifier.Message])) thenReturn Future.unit

    val commandMessage = mock[CommandMessage[_]]
    monitor.onMessageIngested(commandMessage).reportFailure(new RuntimeException("command failed"))

    verify(notifier, times(1)).notify(any[Notifier.Message])
  }

  it should "notify on failed domain event" in {
    when(notifier.notify(any[Notifier.Message])) thenReturn Future.unit

    val domainEventMessage = mock[DomainEventMessage[Werkzoekende.Geregistreerd]]
    val payloadType        = classOf[Werkzoekende.Geregistreerd]
    when(domainEventMessage.getPayloadType) thenReturn payloadType

    monitor.onMessageIngested(domainEventMessage).reportFailure(new RuntimeException("event failed"))

    verify(notifier, times(1)).notify(any[Notifier.Message])
  }

  it should "notify on failed generic message" in {
    when(notifier.notify(any[Notifier.Message])) thenReturn Future.unit

    val message = mock[Message[_]]
    monitor.onMessageIngested(message).reportFailure(new RuntimeException("message failed"))

    verify(notifier, times(1)).notify(any[Notifier.Message])
  }

  it should "notify a successful domain event message" in {
    when(notifier.notify(any[Notifier.Message])) thenReturn Future.unit

    val message     = mock[DomainEventMessage[Werkzoekende.Geregistreerd]]
    val payloadType = classOf[Werkzoekende.Geregistreerd]
    when(message.getPayloadType) thenReturn payloadType

    monitor.onMessageIngested(message).reportSuccess()

    verify(notifier, times(1)).notify(any[Notifier.Message])
  }

  it should "notify a successful command message" in {
    when(notifier.notify(any[Notifier.Message])) thenReturn Future.unit

    val message = mock[CommandMessage[_]]
    monitor.onMessageIngested(message).reportSuccess()

    verify(notifier, times(1)).notify(any[Notifier.Message])
  }

  it should "notify a successful generic message" in {
    when(notifier.notify(any[Notifier.Message])) thenReturn Future.unit

    val message = mock[Message[_]]
    monitor.onMessageIngested(message).reportSuccess()

    verify(notifier, times(1)).notify(any[Notifier.Message])
  }

  it should "notify an ignored domain event message" in {
    when(notifier.notify(any[Notifier.Message])) thenReturn Future.unit

    val message     = mock[DomainEventMessage[Werkzoekende.Geregistreerd]]
    val payloadType = classOf[Werkzoekende.Geregistreerd]
    when(message.getPayloadType) thenReturn payloadType

    monitor.onMessageIngested(message).reportIgnored()

    verify(notifier, times(1)).notify(any[Notifier.Message])
  }

  it should "notify an ignored command message" in {
    when(notifier.notify(any[Notifier.Message])) thenReturn Future.unit

    val message = mock[CommandMessage[_]]
    monitor.onMessageIngested(message).reportIgnored()

    verify(notifier, times(1)).notify(any[Notifier.Message])
  }

  it should "notify an ignored generic message" in {
    when(notifier.notify(any[Notifier.Message])) thenReturn Future.unit

    val message = mock[Message[_]]
    monitor.onMessageIngested(message).reportIgnored()

    verify(notifier, times(1)).notify(any[Notifier.Message])
  }

  it should "notify if a failure action is defined and set to Log" in {
    val monitor = new NotifyingMessageMonitor(notifier, "test-node", Map(classOf[UnableToClaimTokenException] -> Actions.Notify))

    when(notifier.notify(any[Notifier.Message])) thenReturn Future.unit

    val message = mock[Message[_]]
    monitor.onMessageIngested(message).reportFailure(new UnableToClaimTokenException("foo bar"))

    verify(notifier, times(1)).notify(any[Notifier.Message])
  }

  it should "not notify if a failure action is defined and set to Ignore" in {
    val monitor = new NotifyingMessageMonitor(notifier, "test-node", Map(classOf[UnableToClaimTokenException] -> Actions.Ignore))
    val message = mock[Message[_]]
    monitor.onMessageIngested(message).reportFailure(new UnableToClaimTokenException("foo bar"))

    verify(notifier, never).notify(any[Notifier.Message])
  }

  it should "notify a failure action by default (among other defined failure actions)" in {
    val monitor = new NotifyingMessageMonitor(notifier, "test-node", Map(classOf[UnableToClaimTokenException] -> Actions.Notify))

    when(notifier.notify(any[Notifier.Message])) thenReturn Future.unit

    val message = mock[Message[_]]
    monitor.onMessageIngested(message).reportFailure(new RuntimeException("foo bar"))

    verify(notifier, times(1)).notify(any[Notifier.Message])
  }
}
