package nl.dpes.core.services.eventSubscriber.protobufmarshaller

import nl.dpes.core.domain.Recruiter.{Registreer, WijzigEMailadres}
import nl.dpes.core.domain.Sites
import nl.dpes.core.services.eventSubscriber.protobufmarshaller.ProtobufMarshaller._
import nl.dpes.salesforce.protocol.v1.{ContactCreated => PbContactCreated, ContactEmailAddressChanged => PbContactEmailAddressChanged}
import org.scalatest.flatspec.AnyFlatSpec
import org.scalatest.matchers.should.Matchers

class ProtobufMarshallerSpec extends AnyFlatSpec with Matchers {

  behavior of "mapping kinesis messages to commands"

  it should "map a ContactCreated event to a Registreer command" in {
    PbContactCreated("1234", "<EMAIL>").toDomain shouldBe
    Right(Registreer("1234", "<EMAIL>", Sites.Ndp))
  }

  it should "map a ContactEmailAddressChanged event to a WijzigEMailadres command" in {
    PbContactEmailAddressChanged("1234", "<EMAIL>").toDomain shouldBe
    Right(WijzigEMailadres("1234", "<EMAIL>"))
  }
}
