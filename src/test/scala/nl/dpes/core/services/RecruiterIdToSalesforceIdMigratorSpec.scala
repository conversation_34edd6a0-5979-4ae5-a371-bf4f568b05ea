package nl.dpes.core.services

import akka.actor.{ActorSystem => AkkaActorSystem}
import com.amazonaws.services.dynamodbv2.document.DynamoDB
import com.sksamuel.elastic4s.RefreshPolicy
import com.sksamuel.elastic4s.http.{ElasticClient, ElasticProperties}
import nl.dpes.core.config.Configuration
import nl.dpes.core.config.components.SystemComponent
import nl.dpes.core.domain.Recruiter._
import nl.dpes.core.domain.Zoekopdracht._
import nl.dpes.core.domain.{Frequenties, Sites, Zoekopdracht, Zoekparameters}
import nl.dpes.core.projections.Projections.ElasticSearchIndex
import nl.dpes.core.projections.core.CoreRecruiterAccountProjections
import nl.dpes.core.projections.core.CoreRecruiterAccountProjections.CoreRecruiterAccount
import nl.dpes.core.projections.index.IndexOpgeslagenZoekopdrachtProjections.IndexOpgeslagenZoekopdracht
import nl.dpes.core.projections.index.IndexRecruiterFavorietProjections.RecruiterFavorietDocument
import nl.dpes.core.projections.index.{IndexOpgeslagenZoekopdrachtProjections, IndexRecruiterFavorietProjections}
import nl.dpes.testutils.DynamoDBSupport
import org.apache.pekko.actor.ActorSystem
import org.axonframework.commandhandling.gateway.CommandGateway
import org.axonframework.eventhandling.{AnnotationEventHandlerAdapter, GenericEventMessage, SimpleEventBus}
import org.mockito.Mockito.{mock => _, _}
import org.scalatest.flatspec.AnyFlatSpec
import org.scalatest.matchers.should.Matchers
import org.scalatest.{BeforeAndAfterAll, BeforeAndAfterEach}
import org.scalatestplus.mockito.MockitoSugar
import org.slf4j.Logger

import scala.concurrent.ExecutionContextExecutor

class RecruiterIdToSalesforceIdMigratorSpec
    extends AnyFlatSpec
    with Matchers
    with DynamoDBSupport
    with BeforeAndAfterEach
    with BeforeAndAfterAll
    with Configuration
    with MockitoSugar
    with SystemComponent {

  import Fixtures._

  private val eventBus = SimpleEventBus.builder().build()

  implicit val system: ActorSystem                = ActorSystem("test-sandiego-backend")
  implicit val akkaSystem: AkkaActorSystem        = AkkaActorSystem("test-sandiego-backend")
  implicit val executor: ExecutionContextExecutor = system.dispatcher
  implicit private lazy val logger: Logger        = mock[Logger]
  private lazy val commandGateway: CommandGateway = mock[CommandGateway]

  val recruiterAccountProjections = new CoreRecruiterAccountProjections(new DynamoDB(dynamoDbClient))

  val url = "http://localhost:8996"

  private lazy val elasticSearchClient = ElasticClient(ElasticProperties(url))

  val favorietProjections = new IndexRecruiterFavorietProjections(
    index = "recruiter-favorieten",
    indexType = "favoriet",
    client = elasticSearchClient,
    refreshPolicy = RefreshPolicy.IMMEDIATE
  )

  val opgeslagenZoekopdrachtProjections = new IndexOpgeslagenZoekopdrachtProjections(new DynamoDB(dynamoDbClient))

  val migrator = new RecruiterIdToSalesforceIdMigrator(
    recruiterAccountProjections,
    favorietProjections,
    opgeslagenZoekopdrachtProjections,
    commandGateway
  )

  val allDynamoTables: Seq[String] = recruiterAccountProjections.tables ++ opgeslagenZoekopdrachtProjections.tables

  override def beforeAll(): Unit = {

    favorietProjections.createIndexDefinition()

    val recruiterAccountProjectionsAdapter     = new AnnotationEventHandlerAdapter(recruiterAccountProjections)
    val favorietProjectionsAdapter             = new AnnotationEventHandlerAdapter(favorietProjections)
    val recruiterSavedSearchProjectionsAdapter = new AnnotationEventHandlerAdapter(opgeslagenZoekopdrachtProjections)

    val eventHandlers = List(recruiterAccountProjectionsAdapter, favorietProjectionsAdapter, recruiterSavedSearchProjectionsAdapter)

    eventBus.subscribe(eventMessages =>
      eventMessages.forEach { event =>
        eventHandlers.foreach(handler => handler.handle(event))
      }
    )
  }

  override def beforeEach(): Unit = {
    reset(commandGateway)
    reset(logger)
  }

  override def afterEach(): Unit = {
    import com.sksamuel.elastic4s.http.ElasticDsl._

    super[DynamoDBSupport].afterEach(allDynamoTables)

    elasticSearchClient
      .execute(
        deleteByQuery("recruiter-favorieten", "favoriet", matchAllQuery()).refresh(RefreshPolicy.IMMEDIATE)
      )
      .await
  }

  override def afterAll() {
    import com.sksamuel.elastic4s.http.ElasticDsl._

    elasticSearchClient
      .execute(
        deleteIndex("elasticsearch_recruiter-favorieten_v" + ElasticSearchIndex.version)
      )
      .await

    elasticSearchClient.close()

    super[DynamoDBSupport].afterAll(allDynamoTables)
  }

  behavior of "migrate"

  it should "migrate recruiter" in {
    setupBareRecruiters()

    migrator.migrate(List(RecruiterId), SalesforceId)

    verify(commandGateway, times(1)).sendAndWait(importeerNaarSalesforce)
  }

  it should "delete old recruiter" in {
    setupBareRecruiters()

    migrator.migrate(List(RecruiterId, RecruiterId2), SalesforceId)

    verify(commandGateway, times(1)).sendAndWait(Verwijder(RecruiterId))
    verify(commandGateway, times(1)).sendAndWait(Verwijder(RecruiterId2))
  }

  it should "delete old recruiters saved searches" in {
    setupRecruiterWithActiveSavedSearch()

    migrator.migrate(List(RecruiterId, RecruiterId2), SalesforceId)

    verify(commandGateway, times(1)).sendAndWait(
      VerwijderDoorRecruiter(opgeslagenDoorRecruiter.zoekopdrachtId, opgeslagenDoorRecruiter.recruiterId)
    )
    verify(commandGateway, times(1)).sendAndWait(
      VerwijderDoorRecruiter(opgeslagenDoorRecruiter2.zoekopdrachtId, opgeslagenDoorRecruiter2.recruiterId)
    )
  }

  behavior of "migrateRecruiter"

  it should "migrate recruiter by dispatching commands" in {
    setupBareRecruiters()

    migrator.migrateRecruiters(List(RecruiterAccount, RecruiterAccount2), SalesforceId)

    verify(commandGateway, times(1)).sendAndWait(importeerNaarSalesforce)
  }

  it should "migrate favorites by dispatching commands" in {
    setupRecruiterWithFavorites()

    migrator.migrateRecruiters(List(RecruiterAccount, RecruiterAccount2), SalesforceId)

    verify(commandGateway, times(1)).sendAndWait(MaakFavoriet(SalesforceId, WerkzoekendeId1))
    verify(commandGateway, times(1)).sendAndWait(MaakFavoriet(SalesforceId, WerkzoekendeId2))
  }

  it should "migrate saved searches by dispatching commands" in {

    setupRecruiterWithActiveSavedSearch()

    migrator.migrateRecruiters(List(RecruiterAccount, RecruiterAccount2), SalesforceId)

    verify(commandGateway, times(1)).sendAndWait(SlaOpDoorRecruiter(ZoekopdrachtId1, SalesforceId, Naam, Frequentie))
    verify(commandGateway, times(1)).sendAndWait(SlaOpDoorRecruiter(ZoekopdrachtId2, SalesforceId, Naam, Frequentie))
  }

  behavior of "migrate commands"

  it should "return commands to create recruiter" in {
    migrator.migrateCommands(List(RecruiterAccount, RecruiterAccount2), SalesforceId, List.empty, List.empty) shouldBe
    List(importeerNaarSalesforce)
  }

  it should "return commands to create recruiter with favorites" in {

    val favorites = List(
      RecruiterFavorietDocument("fav-id-1", RecruiterId, WerkzoekendeId1),
      RecruiterFavorietDocument("fav-id-2", RecruiterId2, WerkzoekendeId2)
    )

    migrator.migrateCommands(List(RecruiterAccount, RecruiterAccount2), SalesforceId, favorites, List.empty) shouldBe
    List(
      importeerNaarSalesforce,
      MaakFavoriet(SalesforceId, WerkzoekendeId1),
      MaakFavoriet(SalesforceId, WerkzoekendeId2)
    )
  }

  it should "return commands to create recruiter with her saved searches" in {

    val savedSearches = List(
      IndexOpgeslagenZoekopdracht("id-1", RecruiterId, ZoekopdrachtId1, Naam, Frequentie, Parameters),
      IndexOpgeslagenZoekopdracht("id-2", RecruiterId, ZoekopdrachtId2, Naam, Frequenties.Nooit, Parameters)
    )

    migrator.migrateCommands(List(RecruiterAccount, RecruiterAccount2), SalesforceId, List.empty, savedSearches) shouldBe
    List(
      importeerNaarSalesforce,
      SlaOpDoorRecruiter(ZoekopdrachtId1, SalesforceId, Naam, Frequentie),
      SlaOpDoorRecruiter(ZoekopdrachtId2, SalesforceId, Naam, Frequenties.Nooit)
    )
  }

  private object Fixtures {

    val SanDiegoId      = 1
    val SanDiegoId2     = 2
    val SalesforceId    = "salesforce-id-123"
    val RecruiterId     = "recruiter-id-1"
    val RecruiterId2    = "recruiter-id-2"
    val RecruiterIds    = List(RecruiterId, RecruiterId2)
    val EmailAddress    = "<EMAIL>"
    val WerkzoekendeId1 = "werkzoekende-id-1"
    val WerkzoekendeId2 = "werkzoekende-id-2"

    val ZoekopdrachtId1 = "zoekopdracht-id-1"
    val ZoekopdrachtId2 = "zoekopdracht-id-2"
    val Naam            = "Test zoekopdracht"
    val Frequentie      = Frequenties.Wekelijks
    val Parameters      = Zoekparameters(locatie = Some("place"))

    val RecruiterAccount  = CoreRecruiterAccount(RecruiterId, EmailAddress, Sites.Nvb)
    val RecruiterAccount2 = CoreRecruiterAccount(RecruiterId2, EmailAddress, Sites.Iol)

    /* Recruiter and Favorite EVENTS */
    val geimporteerd        = Geimporteerd(RecruiterId, SanDiegoId, EmailAddress, Sites.Nvb)
    val geimporteerd2       = Geimporteerd(RecruiterId2, SanDiegoId2, EmailAddress, Sites.Iol)
    val geregistreerd       = Geregistreerd(RecruiterId, EmailAddress, Sites.Nvb)
    val geregistreerd2      = Geregistreerd(RecruiterId2, EmailAddress, Sites.Iol)
    val eMailadresGewijzigd = EMailadresGewijzigd(RecruiterId, "<EMAIL>")
    val favorietGemaakt     = FavorietGemaakt(RecruiterId, WerkzoekendeId1)
    val favorietGemaakt2    = FavorietGemaakt(RecruiterId2, WerkzoekendeId2)

    /* Saved Search EVENTS */
    val aangemaaktDoorRecruiter  = AangemaaktDoorRecruiter(ZoekopdrachtId1, Parameters)
    val aangemaaktDoorRecruiter2 = AangemaaktDoorRecruiter(ZoekopdrachtId2, Parameters)
    val opgeslagenDoorRecruiter  = OpgeslagenDoorRecruiter(ZoekopdrachtId1, RecruiterId, Naam, Frequentie, Parameters)
    val opgeslagenDoorRecruiter2 = OpgeslagenDoorRecruiter(ZoekopdrachtId2, RecruiterId2, Naam, Frequentie, Parameters)
    val verwijderdDoorRecruiter  = VerwijderdDoorRecruiter(ZoekopdrachtId1, RecruiterId)
    val verwijderd               = Zoekopdracht.Verwijderd(ZoekopdrachtId1)

    /* Expected Commands */
    val importeerNaarSalesforce = ImporteerNaarSalesforce(SalesforceId, EmailAddress, Sites.Ndp)
    val verwijderDoorRecruiter  = VerwijderDoorRecruiter(ZoekopdrachtId1, RecruiterId)

    def setupBareRecruiters(): Unit = {
      val events = List(geimporteerd, geimporteerd2)
      publish(events)
    }

    def setupEmailChangedRecruiter(): Unit = {
      val events = List(geimporteerd, geimporteerd2, eMailadresGewijzigd)
      publish(events)
    }

    def setupRecruiterWithFavorites(): Unit = {
      val events = List(
        geimporteerd,
        geimporteerd2,
        favorietGemaakt,
        favorietGemaakt2
      )
      publish(events)
    }

    def setupRecruiterWithActiveSavedSearch(): Unit = {
      val events = List(
        geimporteerd,
        geimporteerd2,
        aangemaaktDoorRecruiter,
        aangemaaktDoorRecruiter2,
        opgeslagenDoorRecruiter,
        opgeslagenDoorRecruiter2
      )
      publish(events)
    }

    def setupRecruiterWithOneActiveOneInactiveSavedSearch(): Unit = {
      val events = List(
        geimporteerd,
        aangemaaktDoorRecruiter.copy(zoekopdrachtId = ZoekopdrachtId2),
        opgeslagenDoorRecruiter.copy(zoekopdrachtId = ZoekopdrachtId2),
        aangemaaktDoorRecruiter,
        opgeslagenDoorRecruiter.copy(frequentie = Frequenties.Nooit)
      )
      publish(events)
    }

    private def publish(events: List[Any]): Unit =
      events.foreach(event => eventBus.publish(GenericEventMessage.asEventMessage(event)))

  }

}
