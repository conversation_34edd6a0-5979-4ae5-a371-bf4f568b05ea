package nl.dpes.core.services

import nl.dpes.core.services.security.PasswordHasher.HashedPassword
import nl.dpes.core.services.security.PasswordPolicy.{PasswordValidationError, TooShort}
import nl.dpes.core.services.security.{BCryptPasswordHasher, DefaultPasswordPolicy}
import org.scalatest.flatspec.AnyFlatSpec
import org.scalatest.matchers.should.Matchers
import org.scalatestplus.mockito.MockitoSugar.mock
import org.slf4j.Logger

class PasswordServiceSpec extends AnyFlatSpec with Matchers {
  private final val MinimumPasswordLength = 8
  private final val logger                = mock[Logger]
  private val service                     = new PasswordService(new BCryptPasswordHasher(logger), new DefaultPasswordPolicy(MinimumPasswordLength))

  it should "validate and hash password" in {
    val password = "barbazfooboo"

    val result = service.validateAndHashPassword(password)
    result.isRight shouldBe true
    result.right.get.hash shouldNot be(empty)
    result.right.get.salt shouldNot be(empty)
  }

  it should "validate and return the error if password policy was not met" in {
    val password = "foobar"

    val result = service.validateAndHashPassword(password)
    result shouldBe Left(TooShort(MinimumPasswordLength))
  }

  behavior of "Password validation"

  it should "validate a password against given policy and return the error" in {
    val password = "foo"

    service.validatePassword(password) shouldBe Left(TooShort(MinimumPasswordLength))
  }

  it should "validate a password against given policy" in {
    val password = "12345678"

    service.validatePassword(password) shouldBe Right(())
  }

  behavior of "Password hashing"

  it should "hash a password with given hasher" in {
    val password = "foobarbaz"

    val hashed = service.hashPassword(password)
    hashed shouldBe a[HashedPassword]
    hashed.hash shouldNot be(empty)
    hashed.salt shouldNot be(empty)
  }

  behavior of "Password verification"

  it should "verify that a password is correct" in {
    val password = "foobarbazboo"

    val hashed = service.hashPassword(password)
    service.verifyHashedPassword(hashed, password) shouldBe true
    service.verifyHashedPassword(hashed.hash, hashed.salt, password) shouldBe true
  }

  it should "verify that a password is incorrect" in {
    val hashed = service.hashPassword("foobar")

    service.verifyHashedPassword(hashed, "barfoo") shouldBe false
  }
}
