package nl.dpes.core.services.notifier

import nl.dpes.core.services.notifier.Notifier.Severities._
import org.mockito.ArgumentMatchers._
import org.mockito.Mockito._
import org.scalatest.BeforeAndAfter
import org.scalatest.flatspec.AnyFlatSpec
import org.scalatestplus.mockito.MockitoSugar
import org.slf4j.Logger

import scala.concurrent.Await
import scala.concurrent.ExecutionContext.Implicits.global
import scala.concurrent.duration._

class LoggingNotifierSpec extends AnyFlatSpec with MockitoSugar with BeforeAndAfter {
  private final val Patience = 5 seconds

  private val logger   = mock[Logger]
  private val notifier = new LoggingNotifier(logger, Debug)

  before {
    reset(logger)
  }

  it should "log debug message on debug" in {
    Await.result(notifier.notify(Notifier.Message("title", "body", Debug)), Patience)
    verify(logger, times(1)).debug(any[String])
  }

  it should "log info message on info" in {
    Await.result(notifier.notify(Notifier.Message("title", "body", Info)), Patience)
    verify(logger, times(1)).info(any[String])
  }

  it should "log warning message on warning" in {
    Await.result(notifier.notify(Notifier.Message("title", "body", Warning)), Patience)
    verify(logger, times(1)).warn(any[String])
  }

  it should "log error message on error" in {
    Await.result(notifier.notify(Notifier.Message("title", "body", Error)), Patience)
    verify(logger, times(1)).error(any[String])
  }

  it should "log critical message on error" in {
    Await.result(notifier.notify(Notifier.Message("title", "body", Critical)), Patience)
    verify(logger, times(1)).error(any[String])
  }

  it should "log alert message on error" in {
    Await.result(notifier.notify(Notifier.Message("title", "body", Alert)), Patience)
    verify(logger, times(1)).error(any[String])
  }

  it should "log emergency message on error" in {
    Await.result(notifier.notify(Notifier.Message("title", "body", Emergency)), Patience)
    verify(logger, times(1)).error(any[String])
  }

  it should "not log a message if the level is not contained" in {
    val notifier = new LoggingNotifier(logger, Critical)

    Await.result(notifier.notify(Notifier.Message("title", "body", Info)), Patience)
    verify(logger, never).info(any[String])
  }
}
