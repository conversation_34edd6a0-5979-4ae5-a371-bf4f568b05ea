package nl.dpes.core.services.notifier

import nl.dpes.common.marshalling.JsonSupport
import nl.dpes.core.services.notifier.SlackNotifier.{Colors, SlackMessage, SlackMessageAttachment, SlackMessageField}
import org.scalatest.flatspec.AnyFlatSpec
import org.scalatest.matchers.should.Matchers
import org.scalatest.{BeforeAndAfter, BeforeAndAfterAll}
import spray.json.{DefaultJsonProtocol, JsonFormat, RootJsonFormat}
import sttp.client3.testing.{RecordingSttpBackend, SttpBackendStub}
import sttp.client3.{basicRequest, Response}
import sttp.model.{Header, MediaType, Uri}

import scala.concurrent.Await
import scala.concurrent.duration._

class SlackNotifierSpec extends AnyFlatSpec with BeforeAndAfterAll with BeforeAndAfter with Matchers with JsonSupport {
  final private val Channel  = "some-channel"
  final private val Username = "username"
  final private val Icon     = None

  final val bindHost = "localhost"
  final val bindPort = 9999

  final private val patience = 10 seconds

  private val uri = Uri(s"http://$bindHost:$bindPort/send")

  private object JsonHelper extends DefaultJsonProtocol {
    implicit lazy val slackMessageFormat: RootJsonFormat[SlackMessage]                 = jsonFormat4(SlackMessage)
    implicit lazy val slackMessageAttachmentFormat: JsonFormat[SlackMessageAttachment] = jsonFormat4(SlackMessageAttachment)
    implicit lazy val slackMessageFieldFormat: JsonFormat[SlackMessageField]           = jsonFormat3(SlackMessageField)
  }

  it should "send a Green notification on a debug message" in {
    val title    = "title"
    val body     = "body"
    val severity = Notifier.Severities.Debug

    val message = SlackMessage(
      Channel,
      Username,
      List(
        SlackMessageAttachment(title, title, Colors.Green, List(SlackMessageField(title, body, short = false)))
      )
    )
    val sttpBackend = getSttpBackend(message)
    val notifier    = new SlackNotifier(Notifier.Severities.Debug, uri, Channel, Username, Icon)(sttpBackend)

    Await.result(notifier.notify(Notifier.Message(title, body, severity)), patience)
    sttpBackend.allInteractions.length shouldBe 1
  }

  it should "send a Green notification on an information message" in {
    val title    = "title"
    val body     = "body"
    val severity = Notifier.Severities.Info

    val message = SlackMessage(
      Channel,
      Username,
      List(
        SlackMessageAttachment(title, title, Colors.Green, List(SlackMessageField(title, body, short = false)))
      )
    )

    val sttpBackend = getSttpBackend(message)
    val notifier    = new SlackNotifier(Notifier.Severities.Debug, uri, Channel, Username, Icon)(sttpBackend)

    Await.result(notifier.notify(Notifier.Message(title, body, severity)), patience)
    sttpBackend.allInteractions.length shouldBe 1
  }

  it should "send an Orange notification on a warning message" in {
    val title    = "title"
    val body     = "body"
    val severity = Notifier.Severities.Warning

    val message = SlackMessage(
      Channel,
      Username,
      List(
        SlackMessageAttachment(title, title, Colors.Orange, List(SlackMessageField(title, body, short = false)))
      )
    )

    val sttpBackend = getSttpBackend(message)
    val notifier    = new SlackNotifier(Notifier.Severities.Debug, uri, Channel, Username, Icon)(sttpBackend)

    Await.result(notifier.notify(Notifier.Message(title, body, severity)), patience)
    sttpBackend.allInteractions.length shouldBe 1
  }

  it should "send a Red notification on an error message" in {
    val title    = "title"
    val body     = "body"
    val severity = Notifier.Severities.Error

    val message = SlackMessage(
      Channel,
      Username,
      List(
        SlackMessageAttachment(title, title, Colors.Red, List(SlackMessageField(title, body, short = false)))
      )
    )

    val sttpBackend = getSttpBackend(message)
    val notifier    = new SlackNotifier(Notifier.Severities.Debug, uri, Channel, Username, Icon)(sttpBackend)

    Await.result(notifier.notify(Notifier.Message(title, body, severity)), patience)
    sttpBackend.allInteractions.length shouldBe 1
  }

  it should "send a Red notification on an critical message" in {
    val title    = "title"
    val body     = "body"
    val severity = Notifier.Severities.Critical

    val message =
      SlackMessage(
        Channel,
        Username,
        List(
          SlackMessageAttachment(title, title, Colors.Red, List(SlackMessageField(title, body, short = false)))
        )
      )

    val sttpBackend = getSttpBackend(message)
    val notifier    = new SlackNotifier(Notifier.Severities.Debug, uri, Channel, Username, Icon)(sttpBackend)

    Await.result(notifier.notify(Notifier.Message(title, body, severity)), patience)
    sttpBackend.allInteractions.length shouldBe 1
  }

  it should "send a Red notification on an alert message" in {
    val title    = "title"
    val body     = "body"
    val severity = Notifier.Severities.Alert
    val message = SlackMessage(
      Channel,
      Username,
      List(
        SlackMessageAttachment(title, title, Colors.Red, List(SlackMessageField(title, body, short = false)))
      )
    )

    val sttpBackend = getSttpBackend(message)
    val notifier    = new SlackNotifier(Notifier.Severities.Debug, uri, Channel, Username, Icon)(sttpBackend)

    Await.result(notifier.notify(Notifier.Message(title, body, severity)), patience)
    sttpBackend.allInteractions.length shouldBe 1
  }

  it should "send a Red notification on an emergency message" in {
    val title    = "title"
    val body     = "body"
    val severity = Notifier.Severities.Emergency

    val message =
      SlackMessage(
        Channel,
        Username,
        List(
          SlackMessageAttachment(title, title, Colors.Red, List(SlackMessageField(title, body, short = false)))
        )
      )

    val sttpBackend = getSttpBackend(message)
    val notifier    = new SlackNotifier(Notifier.Severities.Debug, uri, Channel, Username, Icon)(sttpBackend)

    Await.result(notifier.notify(Notifier.Message(title, body, severity)), patience)
    sttpBackend.allInteractions.length shouldBe 1
  }

  it should "adhere to the notification level setting by not sending notifications if the severity is too low" in {
    val title    = "title"
    val body     = "body"
    val severity = Notifier.Severities.Info

    val sttpBackend = new RecordingSttpBackend(
      SttpBackendStub.asynchronousFuture.whenAnyRequest
        .thenRespond(Response.ok(()))
    )
    val notifier =
      new SlackNotifier(Notifier.Severities.Error, Uri(s"http://$bindHost:$bindPort/send"), Channel, Username, Icon)(sttpBackend)

    Await.result(notifier.notify(Notifier.Message(title, body, severity)), patience)
    sttpBackend.allInteractions.length shouldBe 0
  }

  private def getSttpBackend(message: SlackMessage) = {
    val json = JsonHelper.slackMessageFormat.write(message).toString()

    new RecordingSttpBackend(
      SttpBackendStub.asynchronousFuture
        .whenRequestMatches(
          _ == basicRequest
            .post(uri)
            .header(Header.contentType(MediaType.ApplicationXWwwFormUrlencoded))
            .body(Map("payload" -> json))
        )
        .thenRespond(Response.ok(()))
    )
  }
}
