package nl.dpes.core.services.notifier

import org.scalatest.flatspec.AnyFlatSpec
import org.scalatest.matchers.should.Matchers

class NotifierSpec extends AnyFlatSpec with Matchers {
  import Notifier._

  behavior of "Severities"

  it should "retrieve all severities when requesting all higher than Debug" in {
    Severities.higherIncluding(Severities.Debug) shouldEqual Seq(
      Severities.Debug,
      Severities.Info,
      Severities.Warning,
      Severities.Error,
      Severities.Critical,
      Severities.Alert,
      Severities.Emergency
    )
  }

  it should "retrieve all severities but Debug when requesting all higher than Info" in {
    Severities.higherIncluding(Severities.Info) shouldEqual Seq(
      Severities.Info,
      Severities.Warning,
      Severities.Error,
      Severities.Critical,
      Severities.Alert,
      Severities.Emergency
    )
  }

  it should "retrieve all severities but Debug and Info when requesting all higher than Warning" in {
    Severities.higherIncluding(Severities.Warning) shouldEqual Seq(
      Severities.Warning,
      Severities.Error,
      Severities.Critical,
      Severities.Alert,
      Severities.Emergency
    )
  }

  it should "retrieve all higher severities than Error" in {
    Severities.higherIncluding(Severities.Error) shouldEqual Seq(
      Severities.Error,
      Severities.Critical,
      Severities.Alert,
      Severities.Emergency
    )
  }

  it should "retrieve all higher severities than Critical" in {
    Severities.higherIncluding(Severities.Critical) shouldEqual Seq(
      Severities.Critical,
      Severities.Alert,
      Severities.Emergency
    )
  }

  it should "retrieve all higher severities than Alert" in {
    Severities.higherIncluding(Severities.Alert) shouldEqual Seq(
      Severities.Alert,
      Severities.Emergency
    )
  }

  it should "retrieve all higher severities than Emergency" in {
    Severities.higherIncluding(Severities.Emergency) shouldEqual Seq(
      Severities.Emergency
    )
  }
}
