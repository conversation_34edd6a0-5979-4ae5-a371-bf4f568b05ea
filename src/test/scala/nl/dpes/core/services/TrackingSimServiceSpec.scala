package nl.dpes.core.services

import nl.dpes.core.domain.Werkzoekende.AccountOpgezegd
import nl.dpes.core.services.sim.SimClient
import org.mockito.Mockito.{verify, when}
import org.scalatest.flatspec.AnyFlatSpec
import org.scalatestplus.mockito.MockitoSugar
import org.slf4j.Logger

import scala.concurrent.Future

class TrackingSimServiceSpec extends AnyFlatSpec with MockitoSugar {
  behavior of "trackign SIM service"

  private val simClient               = mock[SimClient]
  private implicit val logger: Logger = mock[Logger]
  private val service                 = new TrackingSimService(simClient)

  it should "delete the profile from SIM after account opgezegd" in {
    val event = AccountOpgezegd("uuid", "")

    when(simClient.deleteProfile("uuid")).thenReturn(Future.unit)

    service.onAccountOpgezegd(event)

    verify(simClient).deleteProfile("uuid")
  }
}
