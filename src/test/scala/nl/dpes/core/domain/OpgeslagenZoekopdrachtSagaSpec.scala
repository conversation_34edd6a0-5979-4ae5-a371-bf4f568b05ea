package nl.dpes.core.domain

import nl.dpes.core.services.SavedSearch
import nl.dpes.testutils.fixtures.domain.ZoekopdrachtFixtures._
import nl.dpes.utils.date.SavedSearchClock
import org.axonframework.test.saga.SagaTestFixture
import org.mockito.ArgumentMatchers
import org.mockito.ArgumentMatchers._
import org.mockito.Mockito.{atLeastOnce, reset, verify, when}
import org.scalatest.BeforeAndAfter
import org.scalatest.flatspec.AnyFlatSpec
import org.scalatestplus.mockito.MockitoSugar
import org.slf4j.Logger

import java.time._
import scala.concurrent.Future
import scala.concurrent.duration._

class OpgeslagenZoekopdrachtSagaSpec extends AnyFlatSpec with BeforeAndAfter with MockitoSugar {

  import OpgeslagenZoekopdrachtSaga._

  private val expectedTime = ZonedDateTime
    .of(2018, 1, 1, 4, 0, 0, 0, ZoneId.of("Europe/Amsterdam"))

  private var fixture: SagaTestFixture[OpgeslagenZoekopdrachtSaga] = _
  private val savedSearch: SavedSearch                             = mock[SavedSearch]
  private val savedSearchClock                                     = mock[SavedSearchClock]

  implicit private lazy val logger: Logger = mock[Logger]

  before {
    reset(savedSearch)
    reset(logger)
    reset(savedSearchClock)

    fixture = new SagaTestFixture[OpgeslagenZoekopdrachtSaga](classOf[OpgeslagenZoekopdrachtSaga])
    fixture.registerResource(savedSearch)
    fixture.registerResource(savedSearchClock)
    fixture.registerResource(logger)
  }

  it should "start on ZoekopdrachtOpgeslagenDoorRecruiter" in {
    val creationDate = expectedTime.toInstant.minusSeconds(15.days.toSeconds)

    when(
      savedSearchClock.getFirstScheduleTimeForRecruiter(
        opgeslagenDoorRecruiter.recruiterId,
        opgeslagenDoorRecruiter.frequentie,
        creationDate
      )
    ).thenReturn(Some(expectedTime.toInstant))

    fixture
      .givenCurrentTime(creationDate)
      .whenPublishingA(opgeslagenDoorRecruiter)
      .expectActiveSagas(1)
      .expectAssociationWith("opgeslagenZoekopdrachtId", opgeslagenDoorRecruiter.getOpgeslagenZoekopdrachtId)
      .expectAssociationWith("recruiterId", opgeslagenDoorRecruiter.recruiterId)
      .expectAssociationWith("zoekopdrachtId", opgeslagenDoorRecruiter.zoekopdrachtId)
      .expectScheduledDeadline(
        expectedTime.toInstant,
        FrequentietijdVerstreken(opgeslagenDoorRecruiter.zoekopdrachtId, opgeslagenDoorRecruiter.recruiterId)
      )
  }

  it should "not schedule sending search results if frequentie is Nooit" in {
    val opgeslagenDoorRecruiterThatNeverSendsResults = opgeslagenDoorRecruiter.copy(frequentie = Frequenties.Nooit)

    fixture
      .givenNoPriorActivity()
      .whenPublishingA(opgeslagenDoorRecruiterThatNeverSendsResults)
      .expectActiveSagas(1)
      .expectNoScheduledDeadlines()
  }

  it should "stop on ZoekopdrachtVerwijderdDoorRecruiter" in {
    when(
      savedSearchClock.getFirstScheduleTimeForRecruiter(any(), any(), any())
    ).thenReturn(None)

    when(
      savedSearchClock.getNextScheduleTimeForRecruiter(any(), any())
    ).thenReturn(None)

    fixture
      .givenAPublished(opgeslagenDoorRecruiter)
      .whenPublishingA(verwijderdDoorRecruiter)
      .expectActiveSagas(0)
      .expectNoAssociationWith("opgeslagenZoekopdrachtId", opgeslagenDoorRecruiter.getOpgeslagenZoekopdrachtId)
  }

  it should "change frequentie on ZoekopdrachtGewijzigd" in {
    val creationDate = expectedTime.toInstant.minusSeconds(2.days.toSeconds)
    val changedDate  = expectedTime.toInstant.minusSeconds(1.day.toSeconds)

    when(
      savedSearchClock.getFirstScheduleTimeForRecruiter(
        any(),
        any(),
        any()
      )
    ).thenReturn(None)

    when(
      savedSearchClock.getFirstScheduleTimeForRecruiter(
        any(),
        any(),
        any()
      )
    ).thenReturn(Some(expectedTime.toInstant))

    fixture
      .givenCurrentTime(creationDate)
      .andThenAPublished(opgeslagenDoorRecruiter.copy(frequentie = Frequenties.Nooit))
      .andThenTimeAdvancesTo(changedDate)
      .whenPublishingA(gewijzigd)
      .expectScheduledDeadline(
        expectedTime.toInstant,
        FrequentietijdVerstreken(opgeslagenDoorRecruiter.zoekopdrachtId, opgeslagenDoorRecruiter.recruiterId)
      )
  }

  it should "send search results and schedule a new search on FrequentietijdVerstreken" in {

    val creationDate = expectedTime.toInstant.minusSeconds(1.day.toSeconds)

    val nextFrequencyInstant = ZonedDateTime
      .of(2018, 1, 2, 4, 0, 0, 0, ZoneId.of("Europe/Amsterdam"))
      .toInstant

    when(
      savedSearchClock.getFirstScheduleTimeForRecruiter(
        opgeslagenDoorRecruiter.recruiterId,
        opgeslagenDoorRecruiter.frequentie,
        creationDate
      )
    ).thenReturn(Some(expectedTime.toInstant))

    when(
      savedSearchClock.getNextScheduleTimeForRecruiter(
        any[String],
        any[Frequenties.Frequentie]
      )
    ).thenReturn(Some(nextFrequencyInstant))

    when(savedSearch.searchAndSend(opgeslagenDoorRecruiter.zoekopdrachtId, opgeslagenDoorRecruiter.recruiterId)) thenReturn Future.unit

    fixture
      .givenCurrentTime(creationDate)
      .andThenAPublished(opgeslagenDoorRecruiter)
      .whenTimeAdvancesTo(expectedTime.toInstant)
      .expectScheduledDeadline(
        nextFrequencyInstant,
        FrequentietijdVerstreken(opgeslagenDoorRecruiter.zoekopdrachtId, opgeslagenDoorRecruiter.recruiterId)
      )

    verify(savedSearch).searchAndSend(opgeslagenDoorRecruiter.zoekopdrachtId, opgeslagenDoorRecruiter.recruiterId)
  }

  it should "close the saga when the saved search could not be found" in {

    val creationDate = expectedTime.toInstant.minusSeconds(1.day.toSeconds)

    val nextFrequencyInstant = ZonedDateTime
      .of(2018, 1, 2, 4, 0, 0, 0, ZoneId.of("Europe/Amsterdam"))
      .toInstant

    when(
      savedSearchClock.getFirstScheduleTimeForRecruiter(
        opgeslagenDoorRecruiter.recruiterId,
        opgeslagenDoorRecruiter.frequentie,
        creationDate
      )
    ).thenReturn(Some(expectedTime.toInstant))

    when(
      savedSearchClock.getNextScheduleTimeForRecruiter(
        any[String],
        any[Frequenties.Frequentie]
      )
    ).thenReturn(Some(nextFrequencyInstant))

    when(savedSearch.searchAndSend(opgeslagenDoorRecruiter.zoekopdrachtId, opgeslagenDoorRecruiter.recruiterId)) thenReturn Future.failed(
      new RuntimeException("saved search not found")
    )

    fixture
      .givenCurrentTime(creationDate)
      .andThenAPublished(opgeslagenDoorRecruiter)
      .whenTimeAdvancesTo(nextFrequencyInstant)
      .expectNoScheduledDeadlines()
      .expectActiveSagas(0)

    verify(savedSearch, atLeastOnce).searchAndSend(opgeslagenDoorRecruiter.zoekopdrachtId, opgeslagenDoorRecruiter.recruiterId)
  }

  it should "log on unknown error and schedule a new time" in {

    val creationDate = expectedTime.toInstant.minusSeconds(1.day.toSeconds)

    val nextFrequencyInstant = ZonedDateTime
      .of(2018, 1, 2, 4, 0, 0, 0, ZoneId.of("Europe/Amsterdam"))
      .toInstant

    when(
      savedSearchClock.getFirstScheduleTimeForRecruiter(
        opgeslagenDoorRecruiter.recruiterId,
        opgeslagenDoorRecruiter.frequentie,
        creationDate
      )
    ).thenReturn(Some(expectedTime.toInstant))

    when(
      savedSearchClock.getNextScheduleTimeForRecruiter(
        any[String],
        any[Frequenties.Frequentie]
      )
    ).thenReturn(Some(nextFrequencyInstant))

    when(savedSearch.searchAndSend(opgeslagenDoorRecruiter.zoekopdrachtId, opgeslagenDoorRecruiter.recruiterId)) thenReturn Future.failed(
      new RuntimeException("unknown error")
    )

    fixture
      .givenCurrentTime(creationDate)
      .andThenAPublished(opgeslagenDoorRecruiter)
      .whenTimeAdvancesTo(expectedTime.toInstant)
      .expectScheduledDeadline(
        nextFrequencyInstant,
        FrequentietijdVerstreken(opgeslagenDoorRecruiter.zoekopdrachtId, opgeslagenDoorRecruiter.recruiterId)
      )

    verify(savedSearch, atLeastOnce).searchAndSend(opgeslagenDoorRecruiter.zoekopdrachtId, opgeslagenDoorRecruiter.recruiterId)
    verify(logger).error(any[String], any[Throwable])
  }
}
