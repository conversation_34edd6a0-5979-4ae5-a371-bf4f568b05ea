package nl.dpes.core.domain

import nl.dpes.core.domain.exceptions.SiteWordtNietOndersteund
import org.scalatest.flatspec.AnyFlatSpec
import org.scalatest.matchers.should.Matchers

class SiteSpec extends AnyFlatSpec with Matchers {
  "Site" should "be constructed by the companion object with a supported site name" in {
    noException should be thrownBy Sites.stringToSite("nationalevacaturebank.nl")
  }

  it should "throw an error when constructed with an invalid site name" in {
    a[SiteWordtNietOndersteund] should be thrownBy Sites.stringToSite("somesite.nl")
  }

  it should "get the site name for a site" in {
    Sites.siteToString(Sites.Iol) should be("intermediair.nl")
    Sites.siteToString(Sites.Itb) should be("itbanen.nl")
    Sites.siteToString(Sites.Nvb) should be("nationalevacaturebank.nl")
  }
}
