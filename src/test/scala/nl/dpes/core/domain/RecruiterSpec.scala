package nl.dpes.core.domain

import nl.dpes.core.domain.exceptions.{EMailadresIsAlInGebruik, RecruiterIdIsInGebruik}
import nl.dpes.core.projections.core.CoreRecruiterAccountProjections
import nl.dpes.core.projections.core.CoreRecruiterAccountProjections.CoreRecruiterAccount
import nl.dpes.testutils.fixtures.domain.RecruiterFixtures._
import org.axonframework.test.aggregate.{AggregateTestFixture, FixtureConfiguration}
import org.mockito.ArgumentMatchers._
import org.mockito.Mockito._
import org.scalatest.BeforeAndAfter
import org.scalatest.flatspec.AnyFlatSpec
import org.scalatest.matchers.should.Matchers
import org.scalatestplus.mockito.MockitoSugar

class RecruiterSpec extends AnyFlatSpec with Matchers with BeforeAndAfter with MockitoSugar {
  private var fixture: FixtureConfiguration[Recruiter] = _
  private val recruiterAccountProjections              = mock[CoreRecruiterAccountProjections]

  before {
    reset(recruiterAccountProjections)

    fixture = new AggregateTestFixture(classOf[Recruiter])
      .registerInjectableResource(recruiterAccountProjections)
  }

  behavior of "Importing an account"

  it should "trigger a Geimporteerd event on Importeer command" in {
    when(recruiterAccountProjections.isEMailadresAvailable(importeer.eMailadres, importeer.site)).thenReturn(true)
    fixture
      .givenNoPriorActivity()
      .when(importeer)
      .expectEvents(geimporteerd)
  }

  it should "not trigger a Geimporteerd event if Recruiter is already imported on Importeer command" in {
    when(recruiterAccountProjections.isEMailadresAvailable(importeer.eMailadres, importeer.site)).thenReturn(false)
    fixture
      .given(geimporteerd)
      .when(importeer)
      .expectNoEvents()
  }

  it should "throw an exception when email address is taken" in {
    when(recruiterAccountProjections.isEMailadresAvailable(importeer.eMailadres, importeer.site)).thenReturn(false)
    fixture
      .givenNoPriorActivity()
      .when(importeer)
      .expectException(classOf[EMailadresIsAlInGebruik])
  }

  behavior of "Importing an account to SalesforceId"

  it should "trigger a Geimporteerd event on ImporteerNaarSalesforce command" in {
    when(recruiterAccountProjections.findByRecruiterId(any[String])).thenReturn(None)
    fixture
      .givenNoPriorActivity()
      .when(importeerNaarSalesforce)
      .expectEvents(geregistreerd)
  }

  it should "not trigger a Geimporteerd event when Recruiter is already exists" in {
    when(recruiterAccountProjections.findByRecruiterId(any[String])).thenReturn(
      Some(
        CoreRecruiterAccount("recruiter-id-123", "<EMAIL>", "nationalevacaturebank.nl")
      )
    )

    fixture
      .givenNoPriorActivity()
      .when(importeerNaarSalesforce)
      .expectNoEvents()
  }

  it should "throw RecruiterIdIsInGebruik when Recruiter already exists" in {
    when(recruiterAccountProjections.findByRecruiterId(any[String])).thenReturn(
      Some(
        CoreRecruiterAccount("recruiter-id-123", "<EMAIL>", "nationalevacaturebank.nl")
      )
    )
    fixture
      .givenNoPriorActivity()
      .when(importeerNaarSalesforce)
      .expectException(classOf[RecruiterIdIsInGebruik])
  }

  behavior of "Changing email"

  it should "not trigger eMailadresGewijzigd on WijzigEMailadres if eMailadres is same" in {
    when(recruiterAccountProjections.isEMailadresAvailable(TestEMailadres, importeer.site)).thenReturn(true)
    fixture
      .given(geimporteerd)
      .when(wijzigEMailadres.copy(eMailadres = TestEMailadres))
      .expectNoEvents()
  }

  it should "trigger eMailadresGewijzigd on WijzigEMailadres if eMailadres is not the same" in {
    when(recruiterAccountProjections.isEMailadresAvailable(wijzigEMailadres.eMailadres, importeer.site)).thenReturn(true)
    fixture
      .given(geimporteerd)
      .when(wijzigEMailadres)
      .expectEvents(eMailadresGewijzigd)
  }

  it should "throw an exception on WijzigEMailadres if eMailadres is taken" in {
    when(recruiterAccountProjections.isEMailadresAvailable(wijzigEMailadres.eMailadres, importeer.site)).thenReturn(false)
    fixture
      .given(geimporteerd)
      .when(wijzigEMailadres)
      .expectException(classOf[EMailadresIsAlInGebruik])
  }

  it should "not trigger eMailadresGewijzigd on WijzigEMailadres if event with same eMailadres is already handled" in {
    when(recruiterAccountProjections.isEMailadresAvailable(TestEMailadres, importeer.site)).thenReturn(true)
    fixture
      .given(geimporteerd, eMailadresGewijzigd)
      .when(wijzigEMailadres)
      .expectNoEvents()
  }

  behavior of "Favorites"

  it should "trigger Favorieten on MaakFavoriet" in {
    fixture
      .given(geimporteerd)
      .when(maakFavoriet)
      .expectEvents(favorietGemaakt)
  }

  it should "not trigger Favorieten on MaakFavoriet when it's already saved" in {
    fixture
      .given(geimporteerd, favorietGemaakt)
      .when(maakFavoriet)
      .expectNoEvents()
  }

  it should "trigger FavorietVerwijderd on VerwijderFavoriet" in {
    fixture
      .given(geimporteerd, favorietGemaakt)
      .when(verwijderFavoriet)
      .expectEvents(favorietVerwijderd)
  }

  it should "not trigger FavorietVerwijderd on VerwijderFavoriet when werkzokende is not in favoriten" in {
    fixture
      .given(geimporteerd, favorietGemaakt, favorietVerwijderd)
      .when(verwijderFavoriet)
      .expectNoEvents()
  }

  behavior of "deleting a recruiter"

  it should "trigger Verwijderd" in {
    fixture
      .given(geimporteerd)
      .when(verwijder)
      .expectEvents(verwijderd)
  }

  it should "not trigger Verwijderd when recruiter already deleted" in {
    fixture
      .given(geimporteerd, verwijderd)
      .when(verwijder)
      .expectNoEvents()
  }

  it should "not apply any event when recruiter already deleted" in {
    fixture
      .given(geimporteerd, verwijderd)
      .when(maakFavoriet)
      .expectNoEvents()
  }

  behavior of "Listen to salesforce"

  it should "trigger a Geregistreerd event on Registreer command" in {
    when(recruiterAccountProjections.findByRecruiterId(registreer.recruiterId)).thenReturn(None)
    fixture
      .givenNoPriorActivity()
      .when(registreer)
      .expectEvents(geregistreerd)
  }

  it should "not trigger an event on Registreer command if recruiter is already registered" in {
    when(recruiterAccountProjections.findByRecruiterId(registreer.recruiterId))
      .thenReturn(Some(CoreRecruiterAccount(RecruiterId, TestEMailadres, Site)))
    fixture
      .givenNoPriorActivity()
      .when(registreer)
      .expectNoEvents()
  }
}
