package nl.dpes.core.domain

import nl.dpes.core.services.profileservice.SearchFilters
import nl.dpes.testutils.fixtures.domain.ZoekopdrachtFixtures._
import org.axonframework.test.aggregate.{AggregateTestFixture, FixtureConfiguration}
import org.scalatest.BeforeAndAfter
import org.scalatest.flatspec.AnyFlatSpec
import org.scalatest.matchers.should.Matchers

import scala.concurrent.duration._

class ZoekopdrachtSpec extends AnyFlatSpec with BeforeAndAfter with Matchers {
  private var fixture: FixtureConfiguration[<PERSON>kopdracht] = _

  val zoekParameters: Zoekparameters = Zoekparameters(
    zoektermen = Some(Zoektermen(alles = Some(Seq("Scala developer")))),
    locatie = Some("Amsterdam"),
    wijzigingsdatum = Some("Afgelopen 3 maanden"),
    opleidingsniveaus = Some(Seq("Level")),
    aantallenUren = Some(Seq("10-40")),
    soortenWerk = Some(Seq("soortenWerk")),
    beschikbaarheden = Some(Seq("Immediate")),
    rijbewijzen = Some(Seq("Type A")),
    talen = Some(Seq("language")),
    afstandTotWerklocatie = Some("afstandTotWerklocatie"),
    carriereniveau = Some(Seq("Level")),
    functiegroep = Some(Seq("Group")),
    gewenstSalaris = Some(Seq("1000")),
    provincies = Some(Seq("Noord-Holland"))
  )

  val searchFilters: SearchFilters = SearchFilters(
    searchTerm = Some("Scala developer"),
    city = Some("Amsterdam"),
    provinces = Some(Seq("Noord-Holland")),
    updatedDate = Some("Afgelopen 3 maanden"),
    functionGroups = Some(Seq("Group")),
    workLevels = Some(Seq("Level")),
    workingHours = Some(Seq("10-40")),
    careerLevels = Some(Seq("Level")),
    requestedSalaries = Some(Seq("1000")),
    availabilities = Some(Seq("Immediate")),
    driversLicenses = Some(Seq("Type A")),
    languages = Some(Seq("language"))
  )

  before {
    fixture = new AggregateTestFixture(classOf[Zoekopdracht])
  }

  behavior of "Saved search"

  it should "trigger AangemaaktDoorRecruiter on MaakAanDoorRecruiter" in {
    fixture
      .givenNoPriorActivity()
      .when(maakAanDoorRecruiter)
      .expectEvents(aangemaaktDoorRecruiter)
  }

  it should "trigger OpgeslagenDoorRecruiter on SlaOpDoorRecruiter" in {
    fixture
      .given(aangemaaktDoorRecruiter)
      .when(slaOpDoorRecruiter)
      .expectEvents(opgeslagenDoorRecruiter)
  }

  it should "not trigger OpgeslagenDoorRecruiter on SlaOpDoorRecruiter when recruiter already has it" in {
    fixture
      .given(aangemaaktDoorRecruiter, opgeslagenDoorRecruiter)
      .when(slaOpDoorRecruiter)
      .expectNoEvents()
  }

  it should "trigger VerwijderdDoorRecruiter on VerwijderDoorRecruiter" in {
    fixture
      .given(aangemaaktDoorRecruiter, opgeslagenDoorRecruiter, opgeslagenDoorRecruiter.copy(recruiterId = AnotherRecruiterId))
      .when(verwijderDoorRecruiter)
      .expectEvents(verwijderdDoorRecruiter)
  }

  it should "not trigger VerwijderdDoorRecruiter on VerwijderDoorRecruiter when recruiter already deleted the saved search" in {
    fixture
      .given(
        aangemaaktDoorRecruiter,
        opgeslagenDoorRecruiter,
        opgeslagenDoorRecruiter.copy(recruiterId = AnotherRecruiterId),
        verwijderdDoorRecruiter
      )
      .when(verwijderDoorRecruiter)
      .expectNoEvents()
  }

  it should "trigger Verwijderd on VerwijderDoorRecruiter when there is no recruiter attached to it anymore" in {
    fixture
      .given(aangemaaktDoorRecruiter, opgeslagenDoorRecruiter)
      .when(verwijderDoorRecruiter)
      .expectEvents(verwijderdDoorRecruiter, verwijderd)
  }

  it should "not trigger Verwijderd on VerwijderDoorRecruiter when Zoekopdracht is already deleted" in {
    fixture
      .given(aangemaaktDoorRecruiter, opgeslagenDoorRecruiter, verwijderdDoorRecruiter, verwijderd)
      .when(verwijderDoorRecruiter)
      .expectNoEvents()
  }

  it should "trigger a Gewijzigd on SlaOpDoorRecruiter when saved-search for recruiter already exists and have different frequency" in {
    fixture
      .given(aangemaaktDoorRecruiter, opgeslagenDoorRecruiter)
      .when(slaOpDoorRecruiter.copy(frequentie = Frequenties.Nooit))
      .expectEvents(gewijzigd.copy(frequentie = Frequenties.Nooit))
  }

  it should "trigger a Gewijzigd on SlaOpDoorRecruiter when saved-search for recruiter already exists and have different name" in {
    fixture
      .given(aangemaaktDoorRecruiter, opgeslagenDoorRecruiter)
      .when(slaOpDoorRecruiter.copy(naam = "new name"))
      .expectEvents(gewijzigd.copy(naam = "new name"))
  }

  it should "trigger no event on SlaOpDoorRecruiter when saved-search for recruiter already exists and have same frequency" in {
    fixture
      .given(aangemaaktDoorRecruiter, opgeslagenDoorRecruiter)
      .when(slaOpDoorRecruiter)
      .expectNoEvents()
  }

  behavior of "frequenties"

  it should "be able to convert a frequency to the correct duration" in {
    Frequenties.frequentieToDuration(Frequenties.Dagelijks) shouldBe 1.day
    Frequenties.frequentieToDuration(Frequenties.Wekelijks) shouldBe 7.days
    Frequenties.frequentieToDuration(Frequenties.Nooit) shouldBe Duration.Inf
  }
  it should "always result in an 'afgelopen 24 uur' filter" in {
    Frequenties.frequentieToWijzigingsDatumFilter(Frequenties.Dagelijks) shouldBe "Afgelopen 24 uur"
    Frequenties.frequentieToWijzigingsDatumFilter(Frequenties.Wekelijks) shouldBe "Afgelopen 24 uur"
    Frequenties.frequentieToWijzigingsDatumFilter(Frequenties.Nooit) shouldBe "Afgelopen 24 uur"
  }

  behavior of "Zoekopdracht mapper"

//  it should "be able to map Zoekparameters to SearchFilters" in {
//    ZoekparametersHelper.toSearchFilters(zoekParameters) shouldBe Right(searchFilters)
//  }

  it should "return None for searchTerm if zoektermen is None" in {
    val params = Zoekparameters(zoektermen = None)
    ZoekparametersHelper.toSearchFilters(params).map(_.searchTerm) shouldBe Right(None)
  }

  it should "return None for searchTerm if zoektermen.alles is None" in {
    val params = Zoekparameters(zoektermen = Some(Zoektermen(alles = None)))
    ZoekparametersHelper.toSearchFilters(params).map(_.searchTerm) shouldBe Right(None)
  }

  it should "return None for searchTerm if zoektermen.alles is Some(Seq())" in {
    val params = Zoekparameters(zoektermen = Some(Zoektermen(alles = Some(Seq.empty))))
    ZoekparametersHelper.toSearchFilters(params).map(_.searchTerm) shouldBe Right(None)
  }

//  it should "return Some(SearchTerm) if zoektermen.alles is Some(nonEmptySeq)" in {
//    val params = Zoekparameters(zoektermen = Some(Zoektermen(alles = Some(Seq("foo", "bar")))))
//    ZoekparametersHelper.toSearchFilters(params).map(_.searchTerm) shouldBe Right(Some("foo bar"))
//  }

//  it should "map correctly" in {
//    val params = Zoekparameters(
//      Some(
//        Zoektermen(
//          Some(List("\"allround servicemonteur\" OR \"Elektromonteur\" OR \"E&I monteur\" OR \"E&I technician\"")),
//          None,
//          None,
//          None,
//          None,
//          None,
//          None
//        )
//      ),
//      None,
//      Some("Afgelopen 6 maanden"),
//      Some(List()),
//      Some(List()),
//      Some(List()),
//      Some(List()),
//      Some(List()),
//      Some(List()),
//      None,
//      Some(List()),
//      Some(List()),
//      Some(List()),
//      Some(List())
//    )
//    ZoekparametersHelper.toSearchFilters(params) shouldBe Right(
//      SearchFilters(
//        Some("\"allround servicemonteur\" OR \"Elektromonteur\" OR \"E&I monteur\" OR \"E&I technician\""),
//        None,
//        Some(List()),
//        Some("Afgelopen 6 maanden"),
//        Some(List()),
//        Some(List()),
//        Some(List()),
//        Some(List()),
//        Some(List()),
//        Some(List()),
//        Some(List()),
//        Some(List())
//      )
//    )
//  }
}
