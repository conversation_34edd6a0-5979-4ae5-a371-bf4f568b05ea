package nl.dpes.core.domain

import nl.dpes.core.domain.exceptions.EMailadresIsOngeldig
import org.scalatest.flatspec.AnyFlatSpec
import org.scalatest.matchers.should.Matchers

class EMailadresSpec extends AnyFlatSpec with Matchers {
  "Email address" should "not throw an exception when created with a valid email address" in {
    noException should be thrownBy EMailadres("<EMAIL>")
  }

  it should "accept email addresses containing + signs" in {
    noException should be thrownBy EMailadres("<EMAIL>")
  }

  it should "accept email addresses containing 255 characters" in {
    val iAm255Characters = "x" * 249 + "@xx.nl"

    noException should be thrownBy EMailadres(iAm255Characters)
  }

  it should "throw an exception when the address contains more than 255 characters" in {
    val iAm256Characters = "x" * 250 + "@xx.nl"

    a[EMailadresIsOngeldig] should be thrownBy EMailadres(iAm256Characters)
  }

  it should "throw an exception when the address contains spaces" in {
    a[EMailadresIsOngeldig] should be thrownBy EMailadres("an <EMAIL>")
  }

  it should "throw an exception when the address contains multiple @'s" in {
    a[EMailadresIsOngeldig] should be thrownBy EMailadres("an@@invalid.emailaddress")
  }

  it should "throw an exception when the address contains a local hostname" in {
    a[EMailadresIsOngeldig] should be thrownBy EMailadres("a.local.email.address@local")
  }

  it should "convert uppercase characters into lowercase" in {
    EMailadres("<EMAIL>").value shouldBe "<EMAIL>"
  }

  it should "strip spaces at the beginning or end of an email address" in {
    EMailadres("<EMAIL> ").value shouldBe "<EMAIL>"
    EMailadres(" <EMAIL>").value shouldBe "<EMAIL>"
  }
}
