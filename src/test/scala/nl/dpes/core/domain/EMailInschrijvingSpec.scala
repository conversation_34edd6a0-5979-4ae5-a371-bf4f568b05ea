package nl.dpes.core.domain.werkzoekende

import nl.dpes.core.domain.exceptions.EMailinschrijvingWordtNietOndersteund
import org.scalatest.flatspec.AnyFlatSpec
import org.scalatest.matchers.should.Matchers

class EMailInschrijvingSpec extends AnyFlatSpec with Matchers {
  "Subscription" should "be constructed by the companion object with a supported subscription name" in {
    noException should be thrownBy EMailinschrijvingen.stringToEMailinschrijving("personal")
  }

  it should "throw an error when constructed with an invalid subscription name" in {
    a[EMailinschrijvingWordtNietOndersteund] should be thrownBy EMailinschrijvingen.stringToEMailinschrijving("non-existent-subscription")
  }

  it should "get the site name for a site" in {
    EMailinschrijvingen.eMailinschrijvingToString(EMailinschrijvingen.Persoonlijk) should be("personal")
    EMailinschrijvingen.eMailinschrijvingToString(EMailinschrijvingen.Partner) should be("partner")
    EMailinschrijvingen.eMailinschrijvingToString(EMailinschrijvingen.Nieuwsbrief) should be("newsletter")
  }
}
