package nl.dpes.core.domain

import nl.dpes.core.domain.exceptions.UrlIsOngeldig
import org.scalatest.flatspec.AnyFlatSpec
import org.scalatest.matchers.should.Matchers

class UrlSpec extends AnyFlatSpec with Matchers {
  "Url" should "be constructed with a valid url" in {
    noException should be thrownBy Url("https://www.valid-url-with-port.digital:1290/some-number1?query-parameter")
  }

  it should "throw an error when constructed with an invalid url" in {
    a[UrlIsOngeldig] should be thrownBy Url("an invalid url")
  }

  it should "throw an error when constructed without a schema" in {
    a[UrlIsOngeldig] should be thrownBy Url("host.com/login.html")
  }

  it should "throw an error when constructed without a host" in {
    a[UrlIsOngeldig] should be thrownBy Url("http:/this-url-starts-with-a-path-instead-of-host/login.html")
  }

  it should "be converted to a string" in {
    Url.urlToString(Url("http://itb.nl")) should be("http://itb.nl")
  }

  it should "append query parameters" in {
    Url("http://itb.nl") + ("token" -> "abcd") should be(Url("http://itb.nl?token=abcd"))
  }

  it should "append extra query parameters" in {
    Url("http://itb.nl?foo=bar") + ("token" -> "abcd") should be(Url("http://itb.nl?token=abcd&foo=bar"))
  }

  it should "replace duplicate query string parameters" in {
    Url("http://itb.nl?foo=bar&key=value") setQuery ("foo" -> "baz") should be(Url("http://itb.nl?foo=baz&key=value"))
  }

  it should "set a query string parameter if it's not already there" in {
    Url("http://itb.nl?key=value") setQuery ("foo" -> "baz") should be(Url("http://itb.nl?foo=baz&key=value"))
  }
}
