package nl.dpes.core.domain.werkzoekende

import java.time.{Clock, Instant}
import nl.dpes.axon4s.commandhandling.CommandGateway
import nl.dpes.common.conversions._
import nl.dpes.core.domain.Werkzoekende.{Opzeg<PERSON><PERSON>n, ZegAccountOp}
import nl.dpes.core.domain.werkzoekende.VerificatieSaga.VerificatieHerinneringGewenst
import nl.dpes.core.services.MailService
import nl.dpes.core.services.MailService.VerificatieHerinneringsMail
import nl.dpes.testutils.fixtures.domain.WerkzoekendeFixtures._
import org.axonframework.test.saga.SagaTestFixture
import org.mockito.ArgumentMatchers.any
import org.mockito.Mockito._
import org.scalatest.flatspec.AnyFlatSpec
import org.scalatest.BeforeAndAfter
import org.scalatestplus.mockito.MockitoSugar

import scala.concurrent.Future
import scala.concurrent.duration._

class VerificatieSagaSpec extends AnyFlatSpec with BeforeAndAfter with MockitoSugar {
  private var fixture: SagaTestFixture[VerificatieSaga] = _

  private val mailService: MailService       = mock[MailService]
  private val commandGateway: CommandGateway = mock[CommandGateway]
  private val clock: Clock                   = mock[Clock]

  before {
    reset(mailService)
    reset(commandGateway)
    reset(clock)

    val now = Instant.now()

    when(clock.instant())
      .thenReturn(
        now,
        now.plusSeconds((2 days).toSeconds),
        now.plusSeconds((4 days).toSeconds),
        now.plusSeconds((6 days).toSeconds),
        now.plusSeconds((8 days).toSeconds),
        now.plusSeconds((10 days).toSeconds),
        now.plusSeconds((12 days).toSeconds),
        now.plusSeconds((14 days).toSeconds)
      )

    when(mailService.send(any[VerificatieHerinneringsMail]))
      .thenReturn(Future.unit)

    fixture = new SagaTestFixture[VerificatieSaga](classOf[VerificatieSaga])
    fixture.registerResource(mailService)
    fixture.registerResource(clock)
    fixture.registerCommandGateway(classOf[CommandGateway], commandGateway)
  }

  behavior of "saga lifecycle"

  it should "be started on Geregistreerd" in {
    fixture
      .givenNoPriorActivity()
      .whenPublishingA(geregistreerd)
      .expectActiveSagas(1)
      .expectAssociationWith("werkzoekendeId", geregistreerd.werkzoekendeId)
  }

  it should "be closed on Geverifieerd" in {
    fixture
      .givenAPublished(geregistreerd)
      .whenPublishingA(geverifieerd)
      .expectActiveSagas(0)
      .expectNoAssociationWith("werkzoekendeId", geregistreerd.werkzoekendeId)
  }

  it should "be closed on AccountOpgezegd" in {
    fixture
      .givenAPublished(geregistreerd)
      .whenPublishingA(accountOpgezegd)
      .expectActiveSagas(0)
      .expectNoAssociationWith("werkzoekendeId", geregistreerd.werkzoekendeId)
  }

  behavior of "scheduling reminder mails"

  it should "schedule sending a reminder mail 48 hours after registration" in {
    fixture
      .givenNoPriorActivity()
      .whenPublishingA(geregistreerd)
      .expectScheduledDeadline(48 hours, VerificatieHerinneringGewenst(geregistreerd.werkzoekendeId))
  }

  it should "schedule sending a reminder mail 48 hours after sending the last reminder" in {
    fixture
      .givenAPublished(geregistreerd)
      .whenTimeElapses(48 hours)
      .expectScheduledDeadline(48 hours, VerificatieHerinneringGewenst(geregistreerd.werkzoekendeId))
  }

  it should "remove all scheduled reminder mails after verification" in {
    fixture
      .givenAPublished(geregistreerd)
      .whenPublishingA(geverifieerd)
      .expectNoScheduledDeadlines()
  }

  it should "remove all scheduled reminder mails after closing the account" in {
    fixture
      .givenAPublished(geregistreerd)
      .whenPublishingA(accountOpgezegd)
      .expectNoScheduledDeadlines()
  }

  behavior of "sending reminder mails"

  it should "send a reminder mail whenever a scheduled mail is triggered" in {
    fixture
      .givenAPublished(geregistreerd)
      .whenTimeElapses(48 hours)

    verify(mailService, times(1))
      .send(
        VerificatieHerinneringsMail(
          geregistreerd.eMailadres,
          geregistreerd.site,
          geregistreerd.verificatieUrl,
          geregistreerd.verificatieToken,
          "12 dagen"
        )
      )
  }

  it should "not send an email if there are less than 24 hours left to verify the account" in {
    fixture
      .givenAPublished(geregistreerd)
      .whenTimeElapses(14 days)

    verify(mailService, times(6))
      .send(any[VerificatieHerinneringsMail])
  }

  it should "not send an email if the account has already been verified" in {
    fixture
      .givenAPublished(geregistreerd)
      .andThenAPublished(geverifieerd)
      .whenTimeElapses(48 hours)

    verify(mailService, never).send(any[VerificatieHerinneringsMail])
  }

  it should "not send an email if the account has already been deleted" in {
    fixture
      .givenAPublished(geregistreerd)
      .andThenAPublished(accountOpgezegd)
      .whenTimeElapses(48 hours)

    verify(mailService, never).send(any[VerificatieHerinneringsMail])
  }

  behavior of "closing the account after the verification period expires"

  it should "close the account associated with the saga after the verification period expires" in {
    fixture
      .givenAPublished(geregistreerd)
      .whenTimeElapses(14 days)
      .expectDispatchedCommands(ZegAccountOp(geregistreerd.werkzoekendeId, Opzegredenen.VerificatieperiodeVerstreken))
  }

  behavior of "replaying the saga"

  it should "not schedule any deadlines after the verification period has ended" in {
    val registerDate = Instant.now().minusSeconds(15.days.toSeconds)

    fixture
      .givenCurrentTime(registerDate)
      .whenPublishingA(geregistreerd)
      .expectNoScheduledDeadlines()
  }
}
