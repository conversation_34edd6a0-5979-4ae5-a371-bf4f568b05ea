package nl.dpes.core.domain

import nl.dpes.core.domain.Werkzoekende.{
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  StelWachtwoordIn,
  StelWachtwoordInVoorExternalWerkzoekende,
  WachtwoordIngesteldVoorExternalWerkzoekende
}
import nl.dpes.core.domain.exceptions._
import nl.dpes.core.projections.core.CoreWerkzoekendeAccountProjections
import nl.dpes.core.services.security.PasswordHasher.HashedPassword
import nl.dpes.core.services.PasswordService
import nl.dpes.testutils.fixtures.domain.WerkzoekendeFixtures._
import org.axonframework.eventhandling.EventMessage
import org.axonframework.test.aggregate.{AggregateTestFixture, FixtureConfiguration}
import org.axonframework.test.matchers.IgnoreField
import org.axonframework.test.matchers.Matchers._
import org.hamcrest.Matcher
import org.mockito.ArgumentMatchers._
import org.mockito.Mockito._
import org.scalatest.BeforeAndAfter
import org.scalatest.flatspec.AnyFlatSpec
import org.scalatest.matchers.should.Matchers
import org.scalatestplus.mockito.MockitoSugar

class WerkzoekendeSpec extends AnyFlatSpec with MockitoSugar with Matchers with BeforeAndAfter {
  private var fixture: FixtureConfiguration[Werkzoekende] = _
  private val werkzoekendeAccountProjections              = mock[CoreWerkzoekendeAccountProjections]
  private val passwordService                             = mock[PasswordService]

  before {
    reset(werkzoekendeAccountProjections)
    reset(passwordService)

    fixture = new AggregateTestFixture(classOf[Werkzoekende])
      .registerInjectableResource(werkzoekendeAccountProjections)
      .registerInjectableResource(passwordService)
  }

  behavior of "Importing an account"

  it should "publish AccountGeimporteerd on ImporteerAccount" in {
    fixture
      .givenNoPriorActivity()
      .when(importeerAccount)
      .expectEvents(accountGeimporteerd)
  }

  behavior of "Registering an account"

  it should "publish Geregistreerd when successfully registered" in {
    when(werkzoekendeAccountProjections.eMailadresAvailable(registreer.eMailadres, registreer.site)).thenReturn(true)

    fixture
      .givenNoPriorActivity()
      .when(registreer)
      .expectEventsMatching(
        exactSequenceOf(
          messageWithPayload(
            equalTo(geregistreerd, new IgnoreField(classOf[Geregistreerd], "verificatieToken"))
          ).asInstanceOf[Matcher[EventMessage[_]]]
        )
      )
  }

  behavior of "Verify an account"

  it should "publish Geverifieerd when handling Verifieer for the first time" in {
    fixture
      .given(geregistreerd)
      .when(verifieer)
      .expectEvents(geverifieerd)
  }

  it should "not publish Geverifieerd when handling Verifieer for the second time" in {
    fixture
      .given(geregistreerd, geverifieerd)
      .when(verifieer)
      .expectNoEvents()
  }

  behavior of "Set account password"

  it should "publish a WachtwoordIngesteld event on VerzoekTotWachtwoordInstellen" in {
    fixture
      .given(geregistreerd, geverifieerd)
      .when(stelWachtwoordIn)
      .expectEvents(wachtwoordIngesteld)
  }

  it should "throw an exception when account is not verified when setting password" in {
    fixture
      .given(geregistreerd)
      .when(StelWachtwoordIn(WerkzoekendeId, wachtwoord))
      .expectException(classOf[EMailadresIsNietGeverifieerd])
  }

  it should "throw an exception when the password is already set" in {
    fixture
      .given(aVerifiedAccountWithPassword: _*)
      .when(StelWachtwoordIn(WerkzoekendeId, wachtwoord))
      .expectException(classOf[WachtwoordAlIngesteld])
  }

  behavior of "Set account password for external werkzoekende"

  it should "publish a WachtwoordIngesteldVoorExternalWerkzoekende event on StelWachtwoordInVoorExternalWerkzoekende" in {
    fixture
      .given(geregistreerdExternal)
      .when(StelWachtwoordInVoorExternalWerkzoekende(WerkzoekendeId, wachtwoord))
      .expectEvents(wachtwoordIngesteldVoorExternalWerkzoekende)
  }

  it should "throw an exception when the password is already set" in {
    fixture
      .given(geregistreerdExternal, wachtwoordIngesteldVoorExternalWerkzoekende)
      .when(StelWachtwoordInVoorExternalWerkzoekende(WerkzoekendeId, wachtwoord))
      .expectException(classOf[WachtwoordAlIngesteld])
  }

  behavior of "Forgot password"

  it should "publish a WachtwoordVergeten event on VergeetWachtwoord" in {
    fixture
      .given(aVerifiedAccountWithPassword: _*)
      .when(vergeetWachtwoord)
      .expectEvents(wachtwoordVergeten)
  }

  behavior of "Reset password"

  it should "publish a WachtwoordOpnieuwIngesteld on StelWachtwoordOpnieuwIn" in {
    fixture
      .given(aVerifiedAccountWithPassword :+ wachtwoordVergeten: _*)
      .when(stelWachtwoordOpnieuwIn)
      .expectEvents(wachtwoordOpnieuwIngesteld)
  }

  it should "publish a WachtwoordOpnieuwIngesteld with the last published token in case multiple requests for password reset are done" in {
    val secondForgotPasswordTokenId = "some-forgot-password-token"

    fixture
      .given(
        aVerifiedAccountWithPassword :+ wachtwoordVergeten :+
        wachtwoordVergeten.copy(herstelTokenId = secondForgotPasswordTokenId): _*
      ) // twice requested
      .when(stelWachtwoordOpnieuwIn.copy(herstelTokenId = secondForgotPasswordTokenId))
      .expectEvents(wachtwoordOpnieuwIngesteld)
  }

  it should "not publish a WachtwoordOpnieuwIngesteld if NOT the last published token is given and multiple requests for password reset are done" in {
    fixture
      .given(
        aVerifiedAccountWithPassword :+ wachtwoordVergeten :+
        wachtwoordVergeten.copy(herstelTokenId = "some-forgot-password-token"): _*
      ) // twice requested
      .when(stelWachtwoordOpnieuwIn)
      .expectNoEvents()
  }

  it should "publish a WachtwoordIngesteld on StelWachtwoordOpnieuwIn on a verified Werkzoekende with no password" in {
    fixture
      .given(geregistreerd, geverifieerd, wachtwoordVergeten)
      .when(stelWachtwoordOpnieuwIn)
      .expectEvents(wachtwoordIngesteld)
  }

  it should "publish a Geverifieerd and WachtwoordIngesteld on StelWachtwoordOpnieuwIn on a non-verified Werkzoekende with no password" in {
    fixture
      .given(geregistreerd, wachtwoordVergeten)
      .when(stelWachtwoordOpnieuwIn)
      .expectEvents(geverifieerd, wachtwoordIngesteld)
  }

  it should "not publish a WachtwoordOpnieuwIngesteld on StelWachtwoordOpnieuwIn when using the same token twice" in {
    fixture
      .given(aVerifiedAccountWithPassword :+ wachtwoordVergeten :+ wachtwoordOpnieuwIngesteld: _*)
      .when(stelWachtwoordOpnieuwIn)
      .expectException(classOf[WachtwoordTokenOngeldig])
  }

  behavior of "Set password"

  behavior of "Replace password"
  it should "publish a WachtwoordVervangen on VervangWachtwoord when current password matches" in {
    when(passwordService.verifyHashedPassword(any[String], any[String], any[String])).thenReturn(true)
    when(passwordService.hashPassword(any[String])).thenReturn(HashedPassword("new hash", "new salt"))

    fixture
      .given(aVerifiedAccountWithPassword: _*)
      .when(wijzigWachtwoord)
      .expectEvents(wachtwoordGewijzigd)
  }

  it should "not publish a WachtwoordVervangen on VervangWachtwoord when current password does not match" in {
    when(passwordService.verifyHashedPassword(any[String], any[String], any[String])).thenReturn(false)

    fixture
      .given(aVerifiedAccountWithPassword: _*)
      .when(Werkzoekende.WijzigWachtwoord(geregistreerd.werkzoekendeId, "incorrect current password", "new password"))
      .expectNoEvents()
  }

  behavior of "Changing email address"

  it should "publish an EMailadresWijzigingVerzocht on VerzoekEMailadresWijziging" in {
    when(werkzoekendeAccountProjections.eMailadresAvailable(verzoekEMailadresWijziging.nieuwEMailadres, geregistreerd.site))
      .thenReturn(true)

    fixture
      .given(aVerifiedAccountWithPassword: _*)
      .when(verzoekEMailadresWijziging)
      .expectEvents(eMailadresWijzigingVerzocht)
  }

  it should "not publish an EMailadresWijzigingVerzocht on VerzoekEMailadresWijziging when email address was already taken in NDP" in {
    when(werkzoekendeAccountProjections.eMailadresAvailable(verzoekEMailadresWijziging.nieuwEMailadres, geregistreerd.site))
      .thenReturn(false)

    fixture
      .given(aVerifiedAccountWithPassword: _*)
      .when(verzoekEMailadresWijziging)
      .expectNoEvents()
      .expectException(classOf[EMailadresIsAlInGebruik])
  }

  it should "publish an EMailadresGewijzigd on BevestigEMailadresWijziging" in {
    when(werkzoekendeAccountProjections.eMailadresAvailable(verzoekEMailadresWijziging.nieuwEMailadres, geregistreerd.site))
      .thenReturn(true)

    fixture
      .given(aVerifiedAccountWithPassword :+ eMailadresWijzigingVerzocht: _*)
      .when(bevestigEMailadresWijziging)
      .expectEvents(eMailadresGewijzigd)
  }

  it should "not publish an EMailadresGewijzigd on BevestigEMailadresWijziging with an invalid token" in {
    when(werkzoekendeAccountProjections.eMailadresAvailable(verzoekEMailadresWijziging.nieuwEMailadres, geregistreerd.site))
      .thenReturn(true)

    fixture
      .given(aVerifiedAccountWithPassword :+ eMailadresWijzigingVerzocht: _*)
      .when(bevestigEMailadresWijziging.copy(verificatieTokenId = "wrong-token-id"))
      .expectNoEvents()
  }

  it should "not publish an EMailadresGewijzigd when issuing BevestigEMailadresWijziging more than once" in {
    fixture
      .given(aVerifiedAccountWithPassword :+ eMailadresWijzigingVerzocht :+ eMailadresGewijzigd: _*)
      .when(bevestigEMailadresWijziging)
      .expectNoEvents()
      .expectException(classOf[WijzigEMailadresTokenOngeldig])
  }

  it should "not publish an EMailadresGewijzigd when the email address has been taken in the mean time" in {
    when(werkzoekendeAccountProjections.eMailadresAvailable(verzoekEMailadresWijziging.nieuwEMailadres, geregistreerd.site))
      .thenReturn(false)

    fixture
      .given(aVerifiedAccountWithPassword :+ eMailadresWijzigingVerzocht: _*)
      .when(bevestigEMailadresWijziging)
      .expectNoEvents()
      .expectException(classOf[EMailadresIsAlInGebruik])
  }

  behavior of "Subscribing to email subscriptions"

  it should "publish an event for all types of email on SchrijfInVoorAlleEMails" in {
    fixture
      .given(aVerifiedAccountWithPassword: _*)
      .when(schrijfInVoorAlleEMails)
      .expectEvents(ingeschrevenVoorPersoonlijkeEMails, ingeschrevenVoorNieuwsbrief, ingeschrevenVoorPartnerEMail)
  }

  it should "not publish an event for any type of email that the user is already subscribed to on SchrijfInVoorAlleEMails" in {
    fixture
      .given(aVerifiedAccountWithPassword :+ ingeschrevenVoorNieuwsbrief: _*)
      .when(schrijfInVoorAlleEMails)
      .expectEvents(ingeschrevenVoorPersoonlijkeEMails, ingeschrevenVoorPartnerEMail)
  }

  it should "publish an event for a single type of email on SchrijfInVoorEMail" in {
    fixture
      .given(aVerifiedAccountWithPassword: _*)
      .when(schrijfInVoorEMail)
      .expectEvents(ingeschrevenVoorPartnerEMail)
  }

  it should "not publish an event for a type of email that the user is already subscribed to on SchrijfInVoorEMail" in {
    fixture
      .given(aVerifiedAccountWithPassword :+ ingeschrevenVoorPartnerEMail: _*)
      .when(schrijfInVoorEMail)
      .expectNoEvents()
  }

  behavior of "Unsubscribing to email subscriptions"

  it should "publish an event for a single type of email on UitschrijfVoorEMail" in {
    fixture
      .given(aVerifiedAccountWithPassword :+ ingeschrevenVoorPartnerEMail: _*)
      .when(uitschrijfVoorEmail)
      .expectEvents(uitgeschrevenVoorEMail)
  }

  it should "not publish an event for a type of email that the user is not subscribed to on UitschrijfVoorEMail" in {
    fixture
      .given(aVerifiedAccountWithPassword :+ uitgeschrevenVoorEMail: _*)
      .when(uitschrijfVoorEmail)
      .expectNoEvents()
  }

  it should "not publish an event for a type of email that the user is already unsubscribed from on UitschrijfVoorEMail" in {
    fixture
      .given(aVerifiedAccountWithPassword: _*)
      .when(uitschrijfVoorEmail)
      .expectNoEvents()
  }

  behavior of "Terminating an account"
  it should "publish an AccountOpgezegd event on ZegAccountOp" in {
    fixture
      .given(opzeggingVerzocht)
      .when(zegAccountOp)
      .expectEvents(accountOpgezegd)
  }

  it should "again publish an AccountOpgezegd event when already AccountOpgezegd" in {
    fixture
      .given(opzeggingVerzocht, accountOpgezegd)
      .when(zegAccountOp)
      .expectEvents(accountOpgezegd)
  }

  it should "not publish an AccountOpgezegd event when AccountOpgezegd and no prior activity" in {
    fixture
      .givenNoPriorActivity()
      .when(zegAccountOp)
      .expectNoEvents()
  }

  it should "publish an AccountOpgezegd event on ZegAccountOp without opzeggingVerzocht" in {
    fixture
      .given(aVerifiedAccountWithPassword: _*)
      .when(zegAccountOp)
      .expectEvents(accountOpgezegd)
  }
}
