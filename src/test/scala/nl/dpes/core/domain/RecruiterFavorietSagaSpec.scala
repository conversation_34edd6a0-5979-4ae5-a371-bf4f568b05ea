package nl.dpes.core.domain

import nl.dpes.core.domain.Recruiter.VerwijderFavoriet
import nl.dpes.testutils.fixtures.domain.RecruiterFixtures._
import nl.dpes.testutils.fixtures.domain.WerkzoekendeFixtures
import org.axonframework.commandhandling.gateway.CommandGateway
import org.axonframework.test.saga.SagaTestFixture
import org.mockito.Mockito._
import org.scalatest.BeforeAndAfter
import org.scalatest.flatspec.AnyFlatSpec
import org.scalatestplus.mockito.MockitoSugar

class RecruiterFavorietSagaSpec extends AnyFlatSpec with BeforeAndAfter with MockitoSugar {
  private var fixture: SagaTestFixture[RecruiterFavorietSaga] = _
  private val commandGateway                                  = mock[CommandGateway]

  before {
    reset(commandGateway)

    fixture = new SagaTestFixture[RecruiterFavorietSaga](classOf[RecruiterFavorietSaga])
    fixture.registerCommandGateway(classOf[CommandGateway], commandGateway)
  }

  it should "start on FavorietGemaakt" in {
    fixture
      .givenNoPriorActivity()
      .whenPublishingA(favorietGemaakt)
      .expectActiveSagas(1)
      .expectAssociationWith("favorietId", favorietGemaakt.getFavorietId)
      .expectAssociationWith("recruiterId", favorietGemaakt.recruiterId)
      .expectAssociationWith("werkzoekendeId", favorietGemaakt.werkzoekendeId)
  }

  it should "stop on FavorietVerwijderd" in {
    fixture
      .givenAPublished(favorietGemaakt)
      .whenPublishingA(favorietVerwijderd)
      .expectActiveSagas(0)
      .expectNoAssociationWith("favorietId", favorietVerwijderd.getFavorietId)
      .expectNoAssociationWith("recruiterId", favorietGemaakt.recruiterId)
      .expectNoAssociationWith("werkzoekendeId", favorietGemaakt.werkzoekendeId)
  }

  it should "remove favoriet from recruiter on AccountOpgezegd" in {
    fixture
      .givenAPublished(favorietGemaakt)
      .whenPublishingA(WerkzoekendeFixtures.accountOpgezegd.copy(werkzoekendeId = favorietGemaakt.werkzoekendeId))
      .expectDispatchedCommands(
        VerwijderFavoriet(favorietGemaakt.recruiterId, favorietGemaakt.werkzoekendeId)
      )
  }

  it should "remove favoriet when Recruiter.Verwijderd" in {
    fixture
      .givenAPublished(favorietGemaakt)
      .whenPublishingA(verwijderd)
      .expectActiveSagas(0)
      .expectNoAssociationWith("favorietId", favorietVerwijderd.getFavorietId)
      .expectNoAssociationWith("recruiterId", favorietGemaakt.recruiterId)
      .expectNoAssociationWith("werkzoekendeId", favorietGemaakt.werkzoekendeId)
  }

  it should "remove multiple favoriet when Recruiter.Verwijderd" in {
    fixture
      .givenAPublished(favorietGemaakt)
      .andThenAPublished(favorietGemaakt.copy(werkzoekendeId = "another-werkzoekende-id"))
      .whenPublishingA(verwijderd)
      .expectActiveSagas(0)
  }
}
