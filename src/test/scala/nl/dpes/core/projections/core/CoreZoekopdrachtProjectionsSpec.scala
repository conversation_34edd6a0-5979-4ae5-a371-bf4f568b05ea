package nl.dpes.core.projections.core

import com.amazonaws.services.dynamodbv2.document.DynamoDB
import com.thoughtworks.xstream.XStream
import nl.dpes.core.domain.{<PERSON>kparame<PERSON>, Zoektermen}
import nl.dpes.core.projections.DynamoDBProjectionsSpec
import nl.dpes.testutils.fixtures.domain.ZoekopdrachtFixtures._
import org.axonframework.serialization.xml.{CompactDriver, XStreamSerializer}

class CoreZoekopdrachtProjectionsSpec extends DynamoDBProjectionsSpec[CoreZoekopdrachtProjections] {
  val xstream = new XStream(new CompactDriver)
  xstream.allowTypesByWildcard(Array("nl.dpes.**", "scala.**", "cats.**", "ch.qos.logback.**"))
  xstream.ignoreUnknownElements()

  private val serializer: XStreamSerializer = XStreamSerializer.builder().xStream(xstream).build()

  override protected def projections: CoreZoekopdrachtProjections =
    new CoreZoekopdrachtProjections(new DynamoDB(dynamoDbClient), serializer)

  val zoektermen = Zoektermen(
    Some(Seq("all1", "all2")),
    Some(Seq("a", "m")),
    Some(Seq("b", "l")),
    Some(Seq("c", "k")),
    Some(Seq("d")),
    Some(Seq("f")),
    Some(Seq("g", "h"))
  )

  val testZoekparameters = Zoekparameters(
    Some(zoektermen),
    Some("Amsterdam"),
    Some("3 maanden"),
    Some(Seq("op1", "op2")),
    Some(Seq("uur1", "uur2")),
    Some(Seq("soort1", "soort2")),
    Some(Seq("be1", "be2")),
    Some(Seq("rij1", "rij2")),
    Some(Seq("ta1", "ta2")),
    Some("-*20"),
    Some(Seq("ca1", "ca2")),
    Some(Seq("fu1", "fu2")),
    Some(Seq("gs1", "gs2")),
    Some(Seq("provincie1", "provincie2"))
  )

  behavior of "Zoekopdrachten"

  it should "be findable when it is saved" in {
    publish(aangemaaktDoorRecruiter)

    val maybeZoekopdracht = projections.findByParameters(aangemaaktDoorRecruiter.zoekparameters)

    maybeZoekopdracht shouldNot be(None)
    maybeZoekopdracht.get.zoekopdrachtId shouldBe aangemaaktDoorRecruiter.zoekopdrachtId
  }

  it should "be deleted on Verwijderd" in {
    publish(aangemaaktDoorRecruiter, opgeslagenDoorRecruiter, verwijderdDoorRecruiter, verwijderd)

    projections.findByParameters(aangemaaktDoorRecruiter.zoekparameters) shouldBe None
  }

  it should "find CoreZoekparameters with same zoekparamaters" in {
    val aangemaaktEvent = aangemaaktDoorRecruiter.copy(
      zoekparameters = testZoekparameters
    )

    publish(aangemaaktEvent)

    val maybeZoekopdracht = projections.findByParameters(aangemaaktEvent.zoekparameters.copy())
    maybeZoekopdracht shouldNot be(None)
    maybeZoekopdracht.get.zoekopdrachtId shouldBe aangemaaktEvent.zoekopdrachtId
  }

  it should "not find CoreZoekparameters with different zoekparamaters" in {
    val aangemaaktEvent = aangemaaktDoorRecruiter.copy(
      zoekparameters = testZoekparameters
    )

    val testZoekparameters2 = testZoekparameters.copy(
      functiegroep = Some(Seq("fu1", "fu3"))
    )

    publish(aangemaaktEvent)

    val maybeZoekopdracht = projections.findByParameters(testZoekparameters2)
    maybeZoekopdracht should be(None)
  }
}
