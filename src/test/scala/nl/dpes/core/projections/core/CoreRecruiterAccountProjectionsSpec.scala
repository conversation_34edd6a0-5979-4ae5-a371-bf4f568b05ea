package nl.dpes.core.projections.core

import com.amazonaws.services.dynamodbv2.document.DynamoDB
import nl.dpes.core.projections.DynamoDBProjectionsSpec
import nl.dpes.core.projections.core.CoreRecruiterAccountProjections.CoreRecruiterAccount
import nl.dpes.testutils.fixtures.domain.RecruiterFixtures._

class CoreRecruiterAccountProjectionsSpec extends DynamoDBProjectionsSpec[CoreRecruiterAccountProjections] {
  override protected def projections: CoreRecruiterAccountProjections = new CoreRecruiterAccountProjections(new DynamoDB(dynamoDbClient))

  behavior of "Recruiter Account Projections"

  it should "be unavailable when it is taken after importing" in {
    publish(geimporteerd)

    projections.isEMailadresAvailable(geimporteerd.eMailadres, geimporteerd.site) shouldBe false
  }

  it should "be unavailable when it is taken after registration" in {
    publish(geregistreerd)

    projections.isEMailadresAvailable(geimporteerd.eMailadres, geimporteerd.site) shouldBe false
  }

  it should "be available if it is not taken" in {
    projections.isEMailadresAvailable(geimporteerd.eMailadres, geimporteerd.site) shouldBe true
  }

  it should "be updated on EMailadresGewijzigd" in {
    publish(geimporteerd, eMailadresGewijzigd)

    projections.isEMailadresAvailable(eMailadresGewijzigd.eMailadres, geimporteerd.site) shouldBe false
  }

  it should "be found by eMailadres and Site" in {
    publish(geimporteerd)

    projections.findByEMailadresAndSite(geimporteerd.eMailadres, geimporteerd.site) shouldBe
    Some(CoreRecruiterAccount(geimporteerd.recruiterId, geimporteerd.eMailadres, geimporteerd.site))
  }

  it should "be deleted on Verwijderd" in {
    publish(geimporteerd, verwijderd)

    projections.findByEMailadresAndSite(geimporteerd.eMailadres, geimporteerd.site) shouldBe None
  }
}
