package nl.dpes.core.projections.core

import com.amazonaws.services.dynamodbv2.document.DynamoDB
import nl.dpes.core.domain.Sites
import nl.dpes.core.domain.Werkzoekende.Geregistreerd
import nl.dpes.core.projections.DynamoDBProjectionsSpec
import nl.dpes.core.projections.core.CoreWerkzoekendeAccountProjections.CoreWerkzoekendeAccount
import nl.dpes.testutils.fixtures.domain.WerkzoekendeFixtures._
import spray.json._

import java.time.Instant

class CoreWerkzoekendeAccountProjectionsSpec extends DynamoDBProjectionsSpec[CoreWerkzoekendeAccountProjections] {

  private object JsonSupport extends DefaultJsonProtocol {
    implicit lazy val coreAccountFormat: RootJsonFormat[CoreWerkzoekendeAccount] = jsonFormat8(CoreWerkzoekendeAccount)
  }
  import JsonSupport._

  override protected val projections = new CoreWerkzoekendeAccountProjections(new DynamoDB(dynamoDbClient))

  private val werkzoekendeAccount = CoreWerkzoekendeAccount(
    geregistreerd.werkzoekendeId,
    None,
    geregistreerd.eMailadres,
    Sites.siteToString(geregistreerd.site),
    None,
    None
  )

  private val importedWerkzoekendeAccount = CoreWerkzoekendeAccount(
    accountGeimporteerd.werkzoekendeId,
    Some(accountGeimporteerd.sanDiegoId),
    accountGeimporteerd.eMailadres,
    accountGeimporteerd.site,
    Some(accountGeimporteerd.wachtwoord.hash),
    Some(accountGeimporteerd.wachtwoord.salt),
    hasLegacyPassword = Some(true)
  )

  behavior of "Werkzoekende account"

  it should "be inserted on Geregistreerd event" in {
    publish(geregistreerd)

    projections.findByWerkzoekendeId(geregistreerd.werkzoekendeId) shouldBe Some(werkzoekendeAccount)
  }

  it should "be findable by werkzoekende id" in {
    publish(geregistreerd)

    projections.findByWerkzoekendeId(geregistreerd.werkzoekendeId) shouldBe Some(werkzoekendeAccount)
  }

  it should "be findable by sandiego id" in {
    publish(accountGeimporteerd)

    projections.findBySanDiegoId(accountGeimporteerd.sanDiegoId) shouldBe Some(importedWerkzoekendeAccount)
  }

  it should "be inserted on geverifieerd event" in {
    publish(geregistreerd, geverifieerd)

    projections.findByWerkzoekendeId(geregistreerd.werkzoekendeId) shouldBe Some(werkzoekendeAccount)
  }

  it should "be inserted on geimporteerd event with a non-legacy password by default" in {
    publish(accountGeimporteerd.copy(wachtwoord = wachtwoord))

    projections.findBySanDiegoId(accountGeimporteerd.sanDiegoId) shouldBe Some(importedWerkzoekendeAccount.copy(hasLegacyPassword = None))
  }

  it should "be updated with an encrypted password on wachtwoord ingesteld" in {
    publish(accountGeimporteerd, geverifieerd, wachtwoordIngesteld)

    projections.findByWerkzoekendeId(accountGeimporteerd.werkzoekendeId) shouldBe
    Some(
      importedWerkzoekendeAccount.copy(
        wachtwoordHash = Some(wachtwoordIngesteld.wachtwoord.hash),
        salt = Some(wachtwoordIngesteld.wachtwoord.salt),
        hasLegacyPassword = None
      )
    )
  }

  it should "be updated with an encrypted password on wachtwoord opnieuw ingesteld" in {
    publish(geregistreerd, geverifieerd, wachtwoordOpnieuwIngesteld)

    projections.findByWerkzoekendeId(geregistreerd.werkzoekendeId) shouldBe
    Some(
      werkzoekendeAccount.copy(
        wachtwoordHash = Some(wachtwoordOpnieuwIngesteld.wachtwoord.hash),
        salt = Some(wachtwoordOpnieuwIngesteld.wachtwoord.salt),
        hasLegacyPassword = None
      )
    )
  }

  it should "be updated with an encrypted password on wachtwoord gewijzigd" in {
    publish(geregistreerd, geverifieerd, wachtwoordGewijzigd)

    projections.findByWerkzoekendeId(geregistreerd.werkzoekendeId) shouldBe
    Some(
      werkzoekendeAccount.copy(
        wachtwoordHash = Some(wachtwoordGewijzigd.nieuwWachtwoord.hash),
        salt = Some(wachtwoordGewijzigd.nieuwWachtwoord.salt),
        hasLegacyPassword = None
      )
    )
  }

  it should "be removed when AccountOpgezegd" in {
    publish(geregistreerd, accountOpgezegd)

    projections.findByWerkzoekendeId(geregistreerd.werkzoekendeId) shouldBe None
  }

  it should "unmark hasLegacyPassword for jobseeker on WachtwoordIngesteld" in {
    publish(accountGeimporteerd, wachtwoordIngesteld)

    projections.findByWerkzoekendeId(accountGeimporteerd.werkzoekendeId).get.hasLegacyPassword shouldBe None
  }

  it should "unmark hasLegacyPassword for jobseeker on WachtwoordOpnieuwIngesteld" in {
    publish(accountGeimporteerd, wachtwoordOpnieuwIngesteld)

    projections.findByWerkzoekendeId(accountGeimporteerd.werkzoekendeId).get.hasLegacyPassword shouldBe None
  }

  it should "unmark hasLegacyPassword for jobseeker on WachtwoordGewijzigd" in {
    publish(accountGeimporteerd, wachtwoordGewijzigd)

    projections.findByWerkzoekendeId(accountGeimporteerd.werkzoekendeId).get.hasLegacyPassword shouldBe None
  }

  behavior of "Werkzoekende email address"

  it should "be unavailable for the same site after insert" in {
    publish(geregistreerd)

    projections.eMailadresAvailable(geregistreerd.eMailadres, geregistreerd.site) shouldBe false
  }

  it should "only return the last registered account by the same werkzoekende" in {
    val emailAddress = "<EMAIL>"
    val site         = "itbanen.nl"

    // original registration
    val originalRegistratie = Geregistreerd("original id", emailAddress, site, "http://some-verification-url", "some verification token")

    // new registration made by the same werkzoekende
    val newRegistratie = Geregistreerd("new id", emailAddress, site, "http://some-verification-url", "some verification token")

    publish(originalRegistratie, newRegistratie)

    // verify only the last account is found by the system
    val lastInsertedWerkzoekendeAccount = CoreWerkzoekendeAccount(newRegistratie.werkzoekendeId, None, emailAddress, site, None, None)

    projections.findByEMailadres(emailAddress, site) should
    be(Some(lastInsertedWerkzoekendeAccount))
  }

  behavior of "Changing email address"

  it should "update the email address on WijzigEMailadres" in {
    publish(geregistreerd, geverifieerd, eMailadresGewijzigd)

    val expected =
      Some(CoreWerkzoekendeAccount(geregistreerd.werkzoekendeId, None, eMailadresGewijzigd.nieuwEMailadres, geregistreerd.site, None, None))

    projections.findByEMailadres(eMailadresGewijzigd.nieuwEMailadres, geregistreerd.site) should
    be(expected)
  }

  behavior of "Serializing werkzoekende accounts"

  it should "correctly serialize the werkzoekende account" in {
    coreAccountFormat.read("""{
        |"werkzoekendeId":"uuid",
        |"sanDiegoId":12,
        |"eMailadres":"<EMAIL>",
        |"site":"nationalevacaturebank.nl",
        |"wachtwoordHash":"hash",
        |"salt":"salt",
        |"hasLegacyPassword":true
        |}""".stripMargin.parseJson) shouldBe
    CoreWerkzoekendeAccount("uuid", Some(12), "<EMAIL>", "nationalevacaturebank.nl", Some("hash"), Some("salt"), Some(true))
  }

  it should "default to false when no value was set for legacy password" in {
    coreAccountFormat.read("""{
        |"werkzoekendeId":"uuid",
        |"sanDiegoId":12,
        |"eMailadres":"<EMAIL>",
        |"site":"nationalevacaturebank.nl",
        |"wachtwoordHash":"hash",
        |"salt":"salt"
        |}""".stripMargin.parseJson) shouldBe
    CoreWerkzoekendeAccount("uuid", Some(12), "<EMAIL>", "nationalevacaturebank.nl", Some("hash"), Some("salt"), None)
  }

  behavior of "Updating lastActivity field"

  it should "set the field to the current timestamp" in {
    val timestamp = Instant.now.toEpochMilli

    publish(geregistreerd, geverifieerd)

    projections.updateLastActivity(werkzoekendeAccount.werkzoekendeId, timestamp)

    projections.findByWerkzoekendeId(werkzoekendeAccount.werkzoekendeId) shouldBe Some(
      werkzoekendeAccount.copy(lastActivity = Some(timestamp))
    )
  }
}
