package nl.dpes.core.projections.core

import com.amazonaws.services.dynamodbv2.document.DynamoDB
import nl.dpes.core.projections.DynamoDBProjectionsSpec
import nl.dpes.core.projections.core.CoreSanDiegoEmployerProjections.CoreSanDiegoEmployer
import nl.dpes.testutils.fixtures.domain.RecruiterFixtures._

class CoreSanDiegoEmployerProjectionsSpec extends DynamoDBProjectionsSpec[CoreSanDiegoEmployerProjections] {
  override protected val projections = new CoreSanDiegoEmployerProjections(new DynamoDB(dynamoDbClient))

  private val sanDiegoEmployer = CoreSanDiegoEmployer(
    geimporteerd.sanDiegoId,
    geimporteerd.recruiterId
  )

  behavior of "Recruiter San Diego ID"

  it should "be found when recruiter is Geimporteerd" in {
    publish(geimporteerd)

    projections.findBySanDiegoId(geimporteerd.sanDiegoId) shouldBe Some(sanDiegoEmployer)
  }

  it should "not be found if recruiter is not Geimporteerd" in {
    projections.findBySanDiegoId(geimporteerd.sanDiegoId) shouldBe None
  }
}
