package nl.dpes.core.projections.index

import com.sksamuel.elastic4s.http.{ElasticClient, ElasticProperties}
import nl.dpes.testutils.EventBusSupport
import org.mockito.Mockito._
import org.scalatest.flatspec.AnyFlatSpecLike
import org.scalatest.matchers.should.Matchers
import org.scalatest.{BeforeAndAfter, BeforeAndAfterAll}
import org.scalatestplus.mockito.MockitoSugar
import org.slf4j.Logger

class IndexRecruiterFavorietProjectionsSpec
    extends AnyFlatSpecLike
    with BeforeAndAfter
    with BeforeAndAfterAll
    with Matchers
    with MockitoSugar
    with EventBusSupport {

  import nl.dpes.testutils.fixtures.domain.RecruiterFixtures._

  private val indexName                  = "recruiter-favorieten"
  private val indexType                  = "favoriet"
  protected lazy val elasticSearchClient = ElasticClient(ElasticProperties("http://localhost:8996"))

  override protected lazy val service = mock[IndexRecruiterFavorietProjections]

  /** Mock objects * */
  implicit private lazy val logger: Logger = mock[Logger]

  before {
    reset(logger)
  }

  it should "handle FavorietGemaakt" in {
    publish(favorietGemaakt)

    verify(service, times(1)).onFavorietGemaakt(favorietGemaakt)
  }

  it should "handle FavorietVerwijderd" in {
    publish(favorietGemaakt, favorietVerwijderd)

    verify(service, times(1)).onFavorietVerwijderd(favorietVerwijderd)
  }

  it should "handle Recruiter Verwijderd" in {
    publish(favorietGemaakt, verwijderd)

    verify(service, times(1)).onRecruiterVerwijderd(verwijderd)
  }

}
