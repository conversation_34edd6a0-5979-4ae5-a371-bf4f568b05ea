package nl.dpes.core.projections.index

import com.sksamuel.elastic4s.RefreshPolicy
import com.sksamuel.elastic4s.http.{ElasticClient, ElasticProperties}
import nl.dpes.core.projections.index.IndexRecruiterFavorietProjections.{RecruiterFavoriet, RecruiterFavorietDocument}
import nl.dpes.testutils.EventBusSupport
import nl.dpes.testutils.fixtures.domain.RecruiterFixtures._
import org.mockito.Mockito._
import org.scalatest.flatspec.AnyFlatSpecLike
import org.scalatest.matchers.should.Matchers
import org.scalatest.{BeforeAndAfter, BeforeAndAfterAll, BeforeAndAfterEach}
import org.scalatestplus.mockito.MockitoSugar
import org.slf4j.Logger

import java.util.UUID
import scala.concurrent.ExecutionContext.Implicits.global
import scala.concurrent.duration._

class IndexRecruiterFavorietProjectionsIntegrationSpec
    extends AnyFlatSpecLike
    with BeforeAndAfter
    with BeforeAndAfterAll
    with BeforeAndAfterEach
    with Matchers
    with MockitoSugar
    with EventBusSupport {

  import com.sksamuel.elastic4s.http.ElasticDsl._
  import nl.dpes.core.projections.ElasticSearchProjections._

  private val indexName                  = "recruiter-favorieten"
  private val indexType                  = "favoriet"
  protected lazy val elasticSearchClient = ElasticClient(ElasticProperties("http://localhost:8996"))

  private val projection = new IndexRecruiterFavorietProjections(
    indexName,
    indexType,
    elasticSearchClient,
    RefreshPolicy.IMMEDIATE
  )
  override protected lazy val service: IndexRecruiterFavorietProjections = projection

  /** Mock objects * */
  implicit private lazy val logger: Logger = mock[Logger]

  implicit private val duration: Duration = 20 seconds

  /** Fixtures * */
  private val testPage = 1
  private val testSize = 25

  override def beforeAll(): Unit = {
    super.beforeAll()
    projection.createIndexDefinition()
  }

  before {
    reset(logger)

    elasticSearchClient
      .execute(
        deleteIndex(indexName)
      )
      .await
  }

  override def afterEach(): Unit = {
    import com.sksamuel.elastic4s.http.ElasticDsl._

    val x = elasticSearchClient
      .execute(
        deleteByQuery(indexName, "favoriet", matchAllQuery()).refresh(RefreshPolicy.IMMEDIATE)
      )
      .await

  }

  override def afterAll(): Unit = {
    super.afterAll()

    elasticSearchClient
      .execute(
        deleteIndex(indexName)
      )
      .await

    elasticSearchClient.close()
  }

  it should "search for favorieten" in {
    val testRecruiterFavoriet = RecruiterFavoriet(UUID.randomUUID().toString, "werkzoekende-id")
    projection.insertFavoriet(testRecruiterFavoriet).await

    val result = projection.searchFavoriet(testRecruiterFavoriet.recruiterId, testPage, testSize).await
    result shouldBe a[ResultSet[_]]
    result.items foreach { f =>
      f shouldBe a[RecruiterFavorietDocument]
    }
    result.items.size shouldBe 1
  }

  it should "search for favorieten with werkzoekendeId" in {
    val recruiterId            = UUID.randomUUID().toString
    val testRecruiterFavoriet1 = RecruiterFavoriet(recruiterId, "werkzoekende-id-1")
    val testRecruiterFavoriet2 = RecruiterFavoriet(recruiterId, "werkzoekende-id-2")
    val testRecruiterFavoriet3 = RecruiterFavoriet(recruiterId, "werkzoekende-id-3")
    projection.insertFavoriet(testRecruiterFavoriet1).await
    projection.insertFavoriet(testRecruiterFavoriet2).await
    projection.insertFavoriet(testRecruiterFavoriet3).await

    val result = projection
      .searchFavorietByWerkzoekendeIds(
        testRecruiterFavoriet1.recruiterId,
        Seq(testRecruiterFavoriet1.werkzoekendeId, testRecruiterFavoriet2.werkzoekendeId)
      )
      .await
    result.items.size shouldBe 2

    val resultNoResult = projection.searchFavorietByWerkzoekendeIds(testRecruiterFavoriet1.recruiterId, Seq.empty).await
    resultNoResult.items.size shouldBe 0
  }

  it should "insert favorieten" in {
    val testRecruiterFavoriet = RecruiterFavoriet(UUID.randomUUID().toString, "werkzoekende-id")
    projection.searchFavoriet(testRecruiterFavoriet.recruiterId, testPage, testSize).await.items.size shouldBe 0
    projection.insertFavoriet(testRecruiterFavoriet).await

    val result = projection.searchFavoriet(testRecruiterFavoriet.recruiterId, testPage, testSize).await

    result shouldBe a[ResultSet[_]]
    result.items foreach { f =>
      f shouldBe a[RecruiterFavorietDocument]
    }
    result.items.size shouldBe 1
  }

  it should "delete favorieten" in {
    val testRecruiterFavoriet = RecruiterFavoriet(UUID.randomUUID().toString, "werkzoekende-id")
    projection.insertFavoriet(testRecruiterFavoriet).await
    projection.deleteFavoriet(testRecruiterFavoriet.recruiterId, testRecruiterFavoriet.werkzoekendeId).await

    projection.searchFavoriet(testRecruiterFavoriet.recruiterId, testPage, testSize).await.items.size shouldBe 0
  }

  it should "support pagination " in {
    val recruiterId            = UUID.randomUUID().toString
    val testRecruiterFavoriet1 = RecruiterFavoriet(recruiterId, "werkzoekende-id-1")
    val testRecruiterFavoriet2 = RecruiterFavoriet(recruiterId, "werkzoekende-id-2")

    projection.insertFavoriet(testRecruiterFavoriet1).await
    projection.insertFavoriet(testRecruiterFavoriet2).await

    projection.searchFavoriet(recruiterId, 1, 1).await.items.size shouldBe 1
    projection.searchFavoriet(recruiterId, 2, 1).await.items.size shouldBe 1
    projection.searchFavoriet(recruiterId, 3, 1).await.items.size shouldBe 0
  }

  it should "insert a favoriet on FavorietGemaakt" in {
    val recruiterId = UUID.randomUUID().toString
    publish(favorietGemaakt.copy(recruiterId = recruiterId))

    projection.searchFavoriet(recruiterId, 1, 1).await.items.size shouldBe 1
  }

  it should "delete a favoriet on FavorietVerwijderd" in {
    val recruiterId = UUID.randomUUID().toString
    publish(
      favorietGemaakt.copy(recruiterId = recruiterId),
      favorietVerwijderd.copy(recruiterId = recruiterId)
    )

    projection.searchFavoriet(recruiterId, 1, 1).await.items.size shouldBe 0
  }

  "deleteAllByRecruiter" should "delete all favorites of recruiter" in {
    val recruiterId            = UUID.randomUUID().toString
    val testRecruiterFavoriet1 = RecruiterFavoriet(recruiterId, "werkzoekende-id-1")
    val testRecruiterFavoriet2 = RecruiterFavoriet(recruiterId, "werkzoekende-id-2")

    projection.insertFavoriet(testRecruiterFavoriet1).await
    projection.insertFavoriet(testRecruiterFavoriet2).await

    projection.searchFavoriet(recruiterId, 1, 100).await.items.size shouldBe 2

    projection.deleteAllByRecruiter(recruiterId).await

    projection.searchFavoriet(recruiterId, 1, 100).await.items.size shouldBe 0
  }
}
