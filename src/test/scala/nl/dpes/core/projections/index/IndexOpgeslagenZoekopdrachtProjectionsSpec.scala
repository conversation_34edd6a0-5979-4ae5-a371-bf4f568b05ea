package nl.dpes.core.projections.index

import com.amazonaws.services.dynamodbv2.document.DynamoDB
import nl.dpes.core.domain.Frequenties
import nl.dpes.core.domain.Zoekopdracht.OpgeslagenDoorRecruiter
import nl.dpes.core.projections.DynamoDBProjectionsSpec
import nl.dpes.core.projections.index.IndexOpgeslagenZoekopdrachtProjections.IndexOpgeslagenZoekopdracht
import nl.dpes.testutils.fixtures.domain.ZoekopdrachtFixtures._

class IndexOpgeslagenZoekopdrachtProjectionsSpec extends DynamoDBProjectionsSpec[IndexOpgeslagenZoekopdrachtProjections] {
  override protected val projections = new IndexOpgeslagenZoekopdrachtProjections(new DynamoDB(dynamoDbClient))

  behavior of "Index Opgeslagen Zoekopdracht projections"

  it should "retrieve a empty list of Zoekopdrachten saved by a Recruiter if there's nothing found" in {
    projections.findByRecruiterId(RecruiterId) shouldBe List.empty
  }

  it should "retrieve a list of Zoekopdrachten saved by a Recruiter" in {
    publish(opgeslagenDoorRecruiter)

    val indexOpgeslagenZoekopdracht = IndexOpgeslagenZoekopdracht(
      s"${opgeslagenDoorRecruiter.recruiterId}_${opgeslagenDoorRecruiter.zoekopdrachtId}",
      opgeslagenDoorRecruiter.recruiterId,
      opgeslagenDoorRecruiter.zoekopdrachtId,
      opgeslagenDoorRecruiter.naam,
      opgeslagenDoorRecruiter.frequentie,
      opgeslagenDoorRecruiter.zoekparameters
    )

    projections.findByRecruiterId(opgeslagenDoorRecruiter.recruiterId) should contain(indexOpgeslagenZoekopdracht)
  }

  it should "delete a Zoekopdracht saved by a recruiter when recruiter deletes it" in {
    publish(opgeslagenDoorRecruiter, verwijderdDoorRecruiter)

    projections.findByRecruiterId(RecruiterId) shouldBe List.empty
  }

  it should "retrieve one Zoekopdracht saved by a Recruiter" in {
    publish(opgeslagenDoorRecruiter)

    val indexOpgeslagenZoekopdracht = IndexOpgeslagenZoekopdracht(
      s"${opgeslagenDoorRecruiter.recruiterId}_${opgeslagenDoorRecruiter.zoekopdrachtId}",
      opgeslagenDoorRecruiter.recruiterId,
      opgeslagenDoorRecruiter.zoekopdrachtId,
      opgeslagenDoorRecruiter.naam,
      opgeslagenDoorRecruiter.frequentie,
      opgeslagenDoorRecruiter.zoekparameters
    )

    projections.findBySavedSearchId(opgeslagenDoorRecruiter.recruiterId, opgeslagenDoorRecruiter.zoekopdrachtId) should contain(
      indexOpgeslagenZoekopdracht
    )
  }

  it should "update OpgeslagenZoekopdracht" in {
    publish(opgeslagenDoorRecruiter, gewijzigd.copy(naam = "new name", frequentie = Frequenties.Nooit))

    val expectedIndexOpgeslagenZoekopdracht = IndexOpgeslagenZoekopdracht(
      s"${opgeslagenDoorRecruiter.recruiterId}_${opgeslagenDoorRecruiter.zoekopdrachtId}",
      opgeslagenDoorRecruiter.recruiterId,
      opgeslagenDoorRecruiter.zoekopdrachtId,
      "new name",
      Frequenties.Nooit,
      opgeslagenDoorRecruiter.zoekparameters
    )

    projections.findBySavedSearchId(
      opgeslagenDoorRecruiter.recruiterId,
      opgeslagenDoorRecruiter.zoekopdrachtId
    ) shouldBe Some(expectedIndexOpgeslagenZoekopdracht)

  }

  it should "retrieve NONE saved search if there's nothing found" in {
    projections.findBySavedSearchId(opgeslagenDoorRecruiter.recruiterId, opgeslagenDoorRecruiter.zoekopdrachtId) shouldBe None
  }

}
