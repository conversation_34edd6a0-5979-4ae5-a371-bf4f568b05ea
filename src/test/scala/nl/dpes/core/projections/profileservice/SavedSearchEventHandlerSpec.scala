package nl.dpes.core.projections.profileservice

import cats.effect.IO
import nl.dpes.core.domain.{<PERSON>e<PERSON><PERSON>, <PERSON>kpar<PERSON><PERSON>, Zoekter<PERSON>}
import nl.dpes.core.domain.Zoekopdracht.{Aangemaakt<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>erwijderdDoorRecruiter}
import nl.dpes.core.repositories.SavedSearchRepository
import nl.dpes.core.services.profileservice.{Frequency, RecruiterId, SavedSearchId, SavedSearchName, SearchFilters}
import nl.dpes.testutils.EventBusSupport
import org.mockito.Mockito.{reset, times, verify, when}
import org.scalatest.{BeforeAndAfter, BeforeAndAfterAll}
import org.scalatest.flatspec.AnyFlatSpecLike
import org.scalatest.matchers.should.Matchers
import org.scalatestplus.mockito.MockitoSugar
import org.slf4j.Logger

class SavedSearchEventHandlerSpec
    extends AnyFlatSpecLike
    with BeforeAndAfter
    with BeforeAndAfterAll
    with Matchers
    with EventBusSupport
    with MockitoSugar {

  val recruiterId: RecruiterId         = RecruiterId("123456789123456789")
  val invalidRecruiterId: RecruiterId  = RecruiterId("123")
  val savedSearchId: SavedSearchId     = SavedSearchId("123456789123456789123456789123456789")
  val savedSearchName: SavedSearchName = SavedSearchName("Scala developer")
  val frequency: Frequency             = Frequency("Dagelijks")

  val searchFilters: SearchFilters = SearchFilters(
    searchTerm = Some("Scala developer"),
    city = Some("Amsterdam"),
    provinces = Some(Seq("Noord-Holland")),
    updatedDate = Some("Afgelopen 3 maanden"),
    functionGroups = Some(Seq("Group")),
    workLevels = Some(Seq("Level")),
    workingHours = Some(Seq("10-40")),
    careerLevels = Some(Seq("Level")),
    requestedSalaries = Some(Seq("1000")),
    availabilities = Some(Seq("Immediate")),
    driversLicenses = Some(Seq("Type A")),
    languages = Some(Seq("language"))
  )

  val zoekParameters: Zoekparameters = Zoekparameters(
    zoektermen = Some(Zoektermen(alles = Some(Seq("Scala developer")))),
    locatie = Some("Amsterdam"),
    wijzigingsdatum = Some("Afgelopen 3 maanden"),
    opleidingsniveaus = Some(Seq("Level")),
    aantallenUren = Some(Seq("10-40")),
    soortenWerk = Some(Seq("soortenWerk")),
    beschikbaarheden = Some(Seq("Immediate")),
    rijbewijzen = Some(Seq("Type A")),
    talen = Some(Seq("language")),
    afstandTotWerklocatie = Some("afstandTotWerklocatie"),
    carriereniveau = Some(Seq("Level")),
    functiegroep = Some(Seq("Group")),
    gewenstSalaris = Some(Seq("1000")),
    provincies = Some(Seq("Noord-Holland"))
  )

  val repo: SavedSearchRepository[IO]                     = mock[SavedSearchRepository[IO]]
  override protected val service: SavedSearchEventHandler = new SavedSearchEventHandler(repo)
  private implicit val logger: Logger                     = mock[Logger]

  before {
    reset(logger, repo)
  }

//  it should "handle AangemaaktDoorRecruiter" in {
//    when(repo.create(savedSearchId, searchFilters)).thenReturn(IO.unit)
//
//    publish(AangemaaktDoorRecruiter(savedSearchId.value, zoekParameters))
//
//    verify(repo, times(1)).create(savedSearchId, searchFilters)
//  }
//
//  it should "handle OpgeslagenDoorRecruiter" in {
//    when(repo.create(savedSearchId, savedSearchName, recruiterId, searchFilters, frequency)).thenReturn(IO.unit)
//
//    publish(OpgeslagenDoorRecruiter(savedSearchId.value, recruiterId.value, savedSearchName.value, Frequenties.Dagelijks, zoekParameters))
//
//    verify(repo, times(1)).create(savedSearchId, savedSearchName, recruiterId, searchFilters, frequency)
//  }

  it should "not save a search when receiving an invalid recruiter id" in {
    publish(
      OpgeslagenDoorRecruiter(savedSearchId.value, invalidRecruiterId.value, savedSearchName.value, Frequenties.Dagelijks, zoekParameters)
    )

    verify(repo, times(0)).create(savedSearchId, savedSearchName, invalidRecruiterId, searchFilters, frequency)
  }

  it should "handle Gewijzigd" in {
    when(repo.updateFrequency(savedSearchId, Frequency("Nooit"))).thenReturn(IO.unit)

    publish(Gewijzigd(savedSearchId.value, recruiterId.value, savedSearchName.value, Frequenties.Nooit))

    verify(repo, times(1)).updateFrequency(savedSearchId, Frequency("Nooit"))
  }

  it should "not update a saved search when receiving an invalid recruiter id" in {
    publish(Gewijzigd(savedSearchId.value, invalidRecruiterId.value, savedSearchName.value, Frequenties.Nooit))
    verify(repo, times(0)).updateFrequency(savedSearchId, Frequency("Nooit"))
  }

  it should "handle Verwijderd" in {
    when(repo.delete(savedSearchId)).thenReturn(IO.unit)

    publish(Verwijderd(savedSearchId.value))

    verify(repo, times(1)).delete(savedSearchId)
  }

  it should "handle VerwijderdDoorRecruiter" in {
    when(repo.delete(savedSearchId, recruiterId)).thenReturn(IO.unit)

    publish(VerwijderdDoorRecruiter(savedSearchId.value, recruiterId.value))

    verify(repo, times(1)).delete(savedSearchId, recruiterId)
  }

  it should "not delete a saved search when receiving an invalid recruiter id" in {
    publish(VerwijderdDoorRecruiter(savedSearchId.value, invalidRecruiterId.value))

    verify(repo, times(0)).delete(savedSearchId, invalidRecruiterId)
  }
}
