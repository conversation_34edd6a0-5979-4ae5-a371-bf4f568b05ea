package nl.dpes.core.projections.profileservice

import cats.effect.IO
import nl.dpes.core.domain.Recruiter.{FavorietGemaakt, FavorietVerwijderd}
import nl.dpes.core.repositories.FavoriteProfilesRepository
import nl.dpes.testutils.EventBusSupport
import org.mockito.Mockito.{reset, times, verify, when}
import org.scalatest.flatspec.AnyFlatSpecLike
import org.scalatest.matchers.should.Matchers
import org.scalatest.{BeforeAndAfter, BeforeAndAfterAll}
import org.scalatestplus.mockito.MockitoSugar
import org.slf4j.Logger

class FavoriteProfilesEventHandlerSpec
    extends AnyFlatSpecLike
    with BeforeAndAfter
    with BeforeAndAfterAll
    with Matchers
    with EventBusSupport
    with MockitoSugar {

  val recruiterId: String = "123456789123456789"
  val profileId: String   = "123456789123456789123456789123456789"

  val repo: FavoriteProfilesRepository[IO]                     = mock[FavoriteProfilesRepository[IO]]
  override protected val service: FavoriteProfilesEventHandler = new FavoriteProfilesEventHandler(repo)
  private implicit val logger: Logger                          = mock[Logger]

  before {
    reset(logger)
  }

  it should "handle FavorietGemaakt" in {
    when(repo.saveFavorite(recruiterId, profileId)).thenReturn(IO.unit)

    publish(FavorietGemaakt(recruiterId, profileId))

    verify(repo, times(1)).saveFavorite(recruiterId, profileId)
  }

  it should "handle FavorietVerwijderd" in {
    when(repo.deleteFavorite(recruiterId, profileId)).thenReturn(IO.unit)

    publish(FavorietVerwijderd(recruiterId, profileId))

    verify(repo, times(1)).deleteFavorite(recruiterId, profileId)
  }

}
