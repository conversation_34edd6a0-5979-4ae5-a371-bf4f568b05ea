package nl.dpes.core.projections

import nl.dpes.core.config.Configuration
import nl.dpes.testutils.{DynamoDBSupport, EventBusSupport}
import org.scalatest.flatspec.AnyFlatSpec
import org.scalatest.matchers.should.Matchers
import org.scalatest.{BeforeAndAfterAll, BeforeAndAfterEach}

abstract class DynamoDBProjectionsSpec[P <: DynamoDBProjections]
    extends AnyFlatSpec
    with DynamoDBSupport
    with Configuration
    with Matchers
    with BeforeAndAfterAll
    with BeforeAndAfterEach
    with EventBusSupport {

  protected def projections: P
  override protected lazy val service: P = projections

  override def afterEach(): Unit =
    super[DynamoDBSupport].afterEach(projections.tables)

  override def afterAll(): Unit =
    super[DynamoDBSupport].afterAll(projections.tables)
}
