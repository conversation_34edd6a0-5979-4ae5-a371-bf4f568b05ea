package nl.dpes.core.repositories

import cats.effect.{IO, Resource}
import doobie.Fragment
import doobie.implicits._
import doobie.util.transactor.Transactor
import nl.dpes.testutils.testcontainers.MySqlDatabaseGenerator
import org.typelevel.log4cats.LoggerFactory
import org.typelevel.log4cats.slf4j.Slf4jFactory
import weaver.IOSuite

import java.util.UUID

object FavoriteProfilesRepositorySpec extends IOSuite with MySqlDatabaseGenerator {

  override type Res = Transactor[IO]
  override def sharedResource: Resource[IO, FavoriteProfilesRepositorySpec.Res] = transactor

  implicit val logger: LoggerFactory[IO] = Slf4jFactory.create[IO]

  case class Repo(favoritesRepo: FavoriteProfilesRepository[IO], tableName: String)

  val recruiterId: String = "123456789123456789"
  val profileId: String   = "123456789123456789123456789123456789"

  def randomRepo(xa: Transactor[IO]): Resource[IO, Repo] = {
    val id = UUID.randomUUID().toString.replace("-", "")
    Resource.eval(IO(Repo(new FavoriteProfilesRepository(s"favorites_$id".trim, xa), s"favorites_$id".trim)))
  }

  def createTable(tableName: String, xa: Transactor[IO]): IO[Unit] =
    sql"""
       CREATE TABLE IF NOT EXISTS ${Fragment.const(tableName)} (
          recruiterId VARCHAR(36),
          profileId   VARCHAR(36),
          timestamp   TIMESTAMP NOT NULL,
          PRIMARY KEY (recruiterId, profileId),
          INDEX (recruiterId),
          INDEX (profileId)
       )
      """.update.run.transact(xa).void

  def getProfiles(recruiterId: String, tableName: Fragment, xa: Transactor[IO]): IO[List[String]] =
    sql"""
        SELECT profileId
        FROM $tableName
        WHERE recruiterId = $recruiterId
        ORDER BY timestamp
       """
      .query[String]
      .to[List]
      .transact(xa)

  test("It should be able to save favorite profiles") { xa =>
    randomRepo(xa).use { repo =>
      for {
        _         <- createTable(repo.tableName, xa)
        _         <- repo.favoritesRepo.saveFavorite(recruiterId, profileId).attempt
        _         <- repo.favoritesRepo.saveFavorite(recruiterId, profileId).attempt
        _         <- repo.favoritesRepo.saveFavorite(recruiterId, profileId).attempt
        _         <- repo.favoritesRepo.saveFavorite(recruiterId, "profile 2").attempt
        _         <- repo.favoritesRepo.saveFavorite(recruiterId, "profile 3").attempt
        _         <- repo.favoritesRepo.saveFavorite("recruiter 2", profileId).attempt
        favorites <- getProfiles(recruiterId, Fragment.const(repo.tableName), xa)
      } yield expect(favorites == List(profileId, "profile 2", "profile 3"))
    }
  }

  test("It should be able to delete favorite profiles") { xa =>
    randomRepo(xa).use { repo =>
      for {
        _         <- createTable(repo.tableName, xa)
        _         <- repo.favoritesRepo.saveFavorite(recruiterId, profileId).attempt
        _         <- repo.favoritesRepo.saveFavorite(recruiterId, "profile 2").attempt
        _         <- repo.favoritesRepo.saveFavorite(recruiterId, "profile 3").attempt
        _         <- repo.favoritesRepo.deleteFavorite(recruiterId, "profile 2").attempt
        _         <- repo.favoritesRepo.deleteFavorite(recruiterId, "profile 3").attempt
        _         <- repo.favoritesRepo.deleteFavorite(recruiterId, "profile 4").attempt
        favorites <- getProfiles(recruiterId, Fragment.const(repo.tableName), xa)
      } yield expect(favorites == List(profileId))
    }
  }
}
