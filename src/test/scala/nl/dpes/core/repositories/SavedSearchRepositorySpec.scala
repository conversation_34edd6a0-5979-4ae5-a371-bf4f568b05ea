package nl.dpes.core.repositories

import cats.effect.{IO, Resource}
import doobie.implicits._
import doobie.util.fragment.Fragment
import doobie.util.transactor.Transactor
import nl.dpes.core.services.profileservice.{Frequency, RecruiterId, SavedSearchId, SavedSearchName, SearchFilters}
import nl.dpes.testutils.testcontainers.MySqlDatabaseGenerator
import org.typelevel.log4cats.LoggerFactory
import org.typelevel.log4cats.slf4j.Slf4jFactory
import weaver.IOSuite

import java.util.UUID

object SavedSearchRepositorySpec extends IOSuite with MySqlDatabaseGenerator {

  override type Res = Transactor[IO]
  override def sharedResource: Resource[IO, SavedSearchRepositorySpec.Res] = transactor

  implicit val logger: LoggerFactory[IO] = Slf4jFactory.create[IO]

  case class TestSavedSearch(id: String, name: String, recruiterId: String, filters: String, frequency: String)
  case class Repo(savedSearchRepository: SavedSearchRepository[IO], tableName: String)

  def randomRepo(xa: Transactor[IO]): Resource[IO, Repo] = {
    val id = UUID.randomUUID().toString.replace("-", "")
    Resource.eval(IO(Repo(new SavedSearchRepository(s"saved_search_$id", xa), s"saved_search_$id")))
  }

  val recruiterId: RecruiterId         = RecruiterId("123456789123456789")
  val savedSearchId: SavedSearchId     = SavedSearchId("123456789123456789123456789123456789")
  val savedSearchName: SavedSearchName = SavedSearchName("Scala developer")
  val frequency: Frequency             = Frequency("Dagelijks")

  val searchFilters: SearchFilters = SearchFilters(
    searchTerm = Some("Scala developer"),
    city = Some("Amsterdam"),
    provinces = Some(Seq("Noord-Holland")),
    updatedDate = Some("Afgelopen 3 maanden"),
    functionGroups = Some(Seq("Group")),
    workLevels = Some(Seq("Level")),
    workingHours = Some(Seq("10-40")),
    careerLevels = Some(Seq("Level")),
    requestedSalaries = Some(Seq("1000")),
    availabilities = Some(Seq("Immediate")),
    driversLicenses = Some(Seq("Type A")),
    languages = Some(Seq("language"))
  )

  val emptySearchFilters: SearchFilters = SearchFilters(
    searchTerm = None,
    city = None,
    provinces = None,
    updatedDate = None,
    functionGroups = None,
    workLevels = None,
    workingHours = None,
    careerLevels = None,
    requestedSalaries = None,
    availabilities = None,
    driversLicenses = None,
    languages = None
  )

  val testSavedSearch: TestSavedSearch = TestSavedSearch(
    id = "123456789123456789123456789123456789",
    name = "Scala developer",
    recruiterId = "123456789123456789",
    filters =
      "{\"city\": \"Amsterdam\", \"languages\": [\"language\"], \"provinces\": [\"Noord-Holland\"], \"searchTerm\": \"Scala developer\", \"workLevels\": [\"Level\"], \"updatedDate\": \"Afgelopen 3 maanden\", \"careerLevels\": [\"Level\"], \"workingHours\": [\"10-40\"], \"availabilities\": [\"Immediate\"], \"functionGroups\": [\"Group\"], \"driversLicenses\": [\"Type A\"], \"requestedSalaries\": [\"1000\"]}",
    frequency = "Dagelijks"
  )

  def createTable(tableName: String, xa: Transactor[IO]): IO[Unit] =
    sql"""
       CREATE TABLE IF NOT EXISTS ${Fragment.const(tableName)} (
          id            VARCHAR(36)  NOT NULL,
          name          VARCHAR(100) NOT NULL,
          recruiterId   VARCHAR(36)  NOT NULL,
          filters       JSON         NOT NULL,
          frequency     VARCHAR(36)  NOT NULL,
          PRIMARY KEY (id),
          INDEX (recruiterId)
       )
      """.update.run.transact(xa).void

  test("It should create a saved search with only id and filters") { xa =>
    randomRepo(xa).use { repo =>
      for {
        _      <- createTable(repo.tableName, xa)
        _      <- repo.savedSearchRepository.create(savedSearchId, searchFilters)
        result <- getSavedSearch(savedSearchId, repo.tableName, xa)
      } yield expect(result.id == savedSearchId.value) and
      expect(result.filters.contains("Scala developer")) and
      expect(result.name == "") and
      expect(result.recruiterId == "") and
      expect(result.frequency == "Nooit")
    }
  }

  test("It should be able to create an empty saved search with only id and filters") { xa =>
    randomRepo(xa).use { repo =>
      for {
        _      <- createTable(repo.tableName, xa)
        _      <- repo.savedSearchRepository.create(savedSearchId, emptySearchFilters)
        result <- getSavedSearch(savedSearchId, repo.tableName, xa)
      } yield expect(result.id == savedSearchId.value) and
      expect(
        result.filters == "{\"city\": null, \"languages\": null, \"provinces\": null, \"searchTerm\": null, \"workLevels\": null, \"updatedDate\": null, \"careerLevels\": null, \"workingHours\": null, \"availabilities\": null, \"functionGroups\": null, \"driversLicenses\": null, \"requestedSalaries\": null}"
      ) and
      expect(result.name == "") and
      expect(result.recruiterId == "") and
      expect(result.frequency == "Nooit")
    }
  }

  test("It should be able to save a search") { xa =>
    randomRepo(xa).use { repo =>
      for {
        _           <- createTable(repo.tableName, xa)
        _           <- repo.savedSearchRepository.create(savedSearchId, savedSearchName, recruiterId, searchFilters, frequency).attempt
        result      <- repo.savedSearchRepository.create(savedSearchId, savedSearchName, recruiterId, searchFilters, frequency).attempt
        savedSearch <- getSavedSearch(savedSearchId, repo.tableName, xa)
      } yield expect(result == Right(())) and expect(savedSearch == testSavedSearch)
    }
  }

  test("It should update the frequency of a saved search") { xa =>
    randomRepo(xa).use { repo =>
      val newFrequency = Frequency("Wekelijks")
      for {
        _       <- createTable(repo.tableName, xa)
        _       <- repo.savedSearchRepository.create(savedSearchId, savedSearchName, recruiterId, searchFilters, frequency)
        _       <- repo.savedSearchRepository.updateFrequency(savedSearchId, newFrequency)
        updated <- getSavedSearch(savedSearchId, repo.tableName, xa)
      } yield expect(updated.frequency == newFrequency.value)
    }
  }

  test("It should delete a saved search by id") { xa =>
    randomRepo(xa).use { repo =>
      for {
        _      <- createTable(repo.tableName, xa)
        _      <- repo.savedSearchRepository.create(savedSearchId, savedSearchName, recruiterId, searchFilters, frequency)
        count  <- countSavedSearch(savedSearchId, repo.tableName, xa, None)
        _      <- repo.savedSearchRepository.delete(savedSearchId)
        _      <- repo.savedSearchRepository.delete(savedSearchId)
        noData <- countSavedSearch(savedSearchId, repo.tableName, xa, None)
      } yield expect(count == 1) and expect(noData == 0)
    }
  }

  test("It should delete a saved search by id and recruiterId") { xa =>
    randomRepo(xa).use { repo =>
      for {
        _      <- createTable(repo.tableName, xa)
        _      <- repo.savedSearchRepository.create(savedSearchId, savedSearchName, recruiterId, searchFilters, frequency)
        count  <- countSavedSearch(savedSearchId, repo.tableName, xa, Some(recruiterId))
        _      <- repo.savedSearchRepository.delete(savedSearchId, recruiterId)
        _      <- repo.savedSearchRepository.delete(savedSearchId, recruiterId)
        noData <- countSavedSearch(savedSearchId, repo.tableName, xa, Some(recruiterId))
      } yield expect(count == 1) and expect(noData == 0)
    }
  }

  def getSavedSearch(id: SavedSearchId, tableName: String, transactor: Transactor[IO]): IO[TestSavedSearch] =
    sql"""
    SELECT id, name, recruiterId, filters, frequency
    FROM ${Fragment.const(tableName)}
    WHERE id = ${id.value}
  """.query[TestSavedSearch].unique.transact(transactor)

  def countSavedSearch(id: SavedSearchId, tableName: String, xa: Transactor[IO], recruiterId: Option[RecruiterId] = None): IO[Int] = {
    val base  = fr"SELECT COUNT(*) FROM" ++ Fragment.const(tableName) ++ fr"WHERE id = ${id.value}"
    val query = recruiterId.fold(base)(rid => base ++ fr"AND recruiterId = ${rid.value}")
    query.query[Int].unique.transact(xa)
  }
}
