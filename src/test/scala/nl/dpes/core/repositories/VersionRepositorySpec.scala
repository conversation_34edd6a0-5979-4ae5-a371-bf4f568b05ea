package nl.dpes.core.repositories

import com.amazonaws.services.dynamodbv2.document.DynamoDB
import nl.dpes.core.repositories.VersionRepository.VersionInformation
import nl.dpes.testutils.fixtures.repositories.VersionRepositoryFixtures._

class VersionRepositorySpec extends RepositorySpec[VersionRepository] {
  override protected val repository = new VersionRepository(new DynamoDB(dynamoDbClient))

  it should "be able to update version" in {
    repository.updateVersion(VersionName, VersionNumber)

    repository.findByNameAndVersion(VersionName, VersionNumber) shouldBe Some(VersionInformation(VersionName, VersionNumber, ready = false))
  }

  it should "be able to finalize version" in {
    repository.updateVersion(VersionName, VersionNumber)
    repository.finalizeVersion(VersionName, VersionNumber)

    repository.findByNameAndVersion(VersionName, VersionNumber) shouldBe Some(VersionInformation(VersionName, VersionNumber, ready = true))
  }
}
