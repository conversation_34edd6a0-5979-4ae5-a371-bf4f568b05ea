package nl.dpes.core.repositories

import com.amazonaws.services.dynamodbv2.document.DynamoDB
import nl.dpes.core.repositories.TokenRepository.EventStoreMaintenanceToken
import org.scalatestplus.mockito.MockitoSugar
import org.slf4j.Logger

class TokenRepositorySpec extends RepositorySpec[TokenRepository] with MockitoSugar {
  private implicit val logger: Logger = mock[Logger]
  override protected val repository   = new TokenRepository(new DynamoDB(dynamoDbClient), "owner")

  it should "claim if token is not claimed before" in {
    repository.claim(EventStoreMaintenanceToken) shouldBe true
  }

  it should "not claim if token is already claimed" in {
    repository.claim(EventStoreMaintenanceToken)

    repository.claim(EventStoreMaintenanceToken) shouldBe false
  }

  it should "claim a previously released token" in {
    repository.claim(EventStoreMaintenanceToken)

    repository.release(EventStoreMaintenanceToken)

    repository.claim(EventStoreMaintenanceToken) shouldBe true
  }

  it should "ensure release is idempotent" in {
    repository.release(EventStoreMaintenanceToken)
    repository.release(EventStoreMaintenanceToken)

    repository.claim(EventStoreMaintenanceToken) shouldBe true
  }

  it should "not release other owner's token" in {
    val repositoryOtherOwner = new TokenRepository(new DynamoDB(dynamoDbClient), "owner2")

    repository.claim(EventStoreMaintenanceToken)

    repositoryOtherOwner.release(EventStoreMaintenanceToken)
    repositoryOtherOwner.fetchToken(EventStoreMaintenanceToken) shouldNot be(None)
  }
}
