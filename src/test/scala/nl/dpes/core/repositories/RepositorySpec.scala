package nl.dpes.core.repositories

import nl.dpes.core.config.Configuration
import nl.dpes.testutils.DynamoDBSupport
import org.scalatest.flatspec.AnyFlatSpec
import org.scalatest.matchers.should.Matchers
import org.scalatest.{BeforeAndAfterAll, BeforeAndAfterEach}

abstract class RepositorySpec[R <: Repository]
    extends AnyFlatSpec
    with DynamoDBSupport
    with Configuration
    with Matchers
    with BeforeAndAfterAll
    with BeforeAndAfterEach {

  protected def repository: R

  override def afterEach(): Unit =
    super[DynamoDBSupport].afterEach(repository.tables)

  override def afterAll(): Unit =
    super[DynamoDBSupport].afterAll(repository.tables)
}
