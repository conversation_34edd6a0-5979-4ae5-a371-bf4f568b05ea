package nl.dpes.testutils.testcontainers

import cats.effect.{IO, Resource}
import nl.dpes.testutils.testcontainers.MySqlDatabaseGenerator.WrappedTransactor
import weaver.{GlobalResource, GlobalWrite}

object SharedResources extends GlobalResource with MySqlDatabaseGenerator {

  def sharedResources(global: GlobalWrite): Resource[IO, Unit] = for {
    db <- transactor
    _  <- global.putR(WrappedTransactor(db))
  } yield ()
}
