package nl.dpes.testutils.fixtures.domain

import nl.dpes.core.domain.{Fre<PERSON><PERSON>, Zoekparameters, Zoektermen}
import nl.dpes.core.domain.Zoekopdracht._

object ZoekopdrachtFixtures {
  val ZoekopdrachtId     = "zoekopdracht-id-1"
  val RecruiterId        = "recruiter-id-1"
  val AnotherRecruiterId = "another-recruiter-id-1"

  val zoektermen = Zoektermen(
    Some(Seq("all1", "all2")),
    Some(Seq("a", "m")),
    Some(Seq("b", "l")),
    Some(Seq("c", "k")),
    Some(Seq("d")),
    Some(Seq("f")),
    Some(Seq("g", "h"))
  )

  val Parameters = Zoekparameters(
    Some(zoektermen),
    Some("Amsterdam"),
    Some("Afgelopen 3 maanden"),
    Some(Seq("Directie")),
    Some(Seq("16 tot 24 uur", "24 tot 32 uur"))
  )
  val Naam       = "Zoekopdracht naam"
  val Frequentie = Frequenties.Dagelijks

  def maakAanDoorRecruiter: MaakAanDoorRecruiter = MaakAanDoorRecruiter(ZoekopdrachtId, Parameters)

  def aangemaaktDoorRecruiter: AangemaaktDoorRecruiter = AangemaaktDoorRecruiter(ZoekopdrachtId, Parameters)

  def slaOpDoorRecruiter: SlaOpDoorRecruiter = SlaOpDoorRecruiter(
    ZoekopdrachtId,
    RecruiterId,
    Naam,
    Frequentie
  )

  def opgeslagenDoorRecruiter: OpgeslagenDoorRecruiter = OpgeslagenDoorRecruiter(
    ZoekopdrachtId,
    RecruiterId,
    Naam,
    Frequentie,
    Parameters
  )

  def verwijderDoorRecruiter: VerwijderDoorRecruiter = VerwijderDoorRecruiter(
    ZoekopdrachtId,
    RecruiterId
  )

  def verwijderdDoorRecruiter: VerwijderdDoorRecruiter = VerwijderdDoorRecruiter(
    ZoekopdrachtId,
    RecruiterId
  )
  def verwijderd: Verwijderd = Verwijderd(ZoekopdrachtId)

  def gewijzigd = Gewijzigd(
    opgeslagenDoorRecruiter.zoekopdrachtId,
    opgeslagenDoorRecruiter.recruiterId,
    opgeslagenDoorRecruiter.naam,
    Frequenties.Dagelijks
  )
}
