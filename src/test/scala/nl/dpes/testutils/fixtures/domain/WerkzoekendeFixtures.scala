package nl.dpes.testutils.fixtures.domain

import nl.dpes.core.api.v1.jobseekers.JobSeekerEndpoints.{Password, Reason, Registration}
import nl.dpes.core.config.Configuration
import nl.dpes.core.domain.Sites._
import nl.dpes.core.domain._
import nl.dpes.core.domain.werkzoekende.EMailinschrijvingen
import nl.dpes.core.services.TokenService
import nl.dpes.core.services.security.{JWTProvider, RsaKeys}
import nl.dpes.core.services.security.tokens.{ResetPasswordToken, VerificationToken}
import org.mindrot.jbcrypt.BCrypt

import scala.concurrent.duration._

object WerkzoekendeFixtures extends Configuration {
  private val rsaKeys = RsaKeys.fromStrings(Security.Rsa.privateKey, Security.Rsa.publicKey)

  private val jwt = rsaKeys match {
    case Right(keys)     => new JWTProvider(Security.JWT.issuer, keys, Security.Rsa.id)
    case Left(throwable) => throw new Exception(s"RSA keys are not available or cannot be processed: $throwable.")
  }

  private lazy val tokenService = new TokenService(jwt)

  val WerkzoekendeId              = "core-ndp-werkzoekende-id-1"
  val WerkzoekendeEmailAddress    = "<EMAIL>"
  val WerkzoekendeNewEmailAddress = "<EMAIL>"
  val SanDiegoId                  = 42
  val reden                       = "not in search of a job anymore"
  val VerificatieUrl              = "https://www.valid-url.digital/verify"
  val Site: Sites.Site            = Sites.Nvb
  val template                    = "password bogus slug"

  val WijzigEMailadresToken   = "some-email-token"
  val WijzigEMailadresTokenId = "email-token"
  val WijzigEmailUrl          = "https://www.valid-url.digital/email"

  val WachtwoordHerstelUrl      = "https://www.valid-url.digital/reset"
  val WachtwoordVergetenTokenId = "wachtwoord-reset-token"

  val VerificatieToken: String = tokenService.generate(VerificationToken(WerkzoekendeId, Rollen.Werkzoekende))

  val ExpiredVerificatieToken: String = jwt.createAndSignJWTToken(
    WerkzoekendeId,
    List(Rollen.Werkzoekende),
    -10 days,
    Map("type" -> "verification")
  )

  val StelWachtwoordOpnieuwInToken: String =
    tokenService.generate(ResetPasswordToken(WerkzoekendeId, Rollen.Werkzoekende, WachtwoordVergetenTokenId))

  val PlainWachtwoord  = "pl@in-p@ssw0rd"
  val WachtwoordHash   = "bcrypted-p@ssw0rd"
  val Salt: String     = BCrypt.gensalt()
  val wachtwoord       = Wachtwoord(WachtwoordHash, Salt)
  val legacyWachtwoord = Wachtwoord(WachtwoordHash, Salt, true)

  val reason           = Reason(reden)
  val registratie      = Registration(WerkzoekendeEmailAddress, Site, VerificatieUrl)
  val registratieToken = Password(VerificatieToken, PlainWachtwoord)

  val importeerAccount: Werkzoekende.ImporteerAccount = Werkzoekende.ImporteerAccount(
    WerkzoekendeId,
    SanDiegoId,
    WerkzoekendeEmailAddress,
    Iol,
    legacyWachtwoord
  )

  val accountGeimporteerd: Werkzoekende.AccountGeimporteerd = Werkzoekende.AccountGeimporteerd(
    WerkzoekendeId,
    SanDiegoId,
    WerkzoekendeEmailAddress,
    Iol,
    legacyWachtwoord
  )

  val registreer: Werkzoekende.Registreer =
    Werkzoekende.Registreer(WerkzoekendeId, EMailadres(WerkzoekendeEmailAddress), Site, Url(VerificatieUrl), VerificatieToken)

  val geregistreerd: Werkzoekende.Geregistreerd =
    Werkzoekende.Geregistreerd(WerkzoekendeId, EMailadres(WerkzoekendeEmailAddress), Site, VerificatieUrl, VerificatieToken)

  val geregistreerdExternal: Werkzoekende.GeregistreerdExternal =
    Werkzoekende.GeregistreerdExternal(WerkzoekendeId, EMailadres(WerkzoekendeEmailAddress), Site)

  val verifieer: Werkzoekende.Verifieer       = Werkzoekende.Verifieer(WerkzoekendeId)
  val verifieerUUID: Werkzoekende.Verifieer   = Werkzoekende.Verifieer(WerkzoekendeId)
  val geverifieerd: Werkzoekende.Geverifieerd = Werkzoekende.Geverifieerd(WerkzoekendeId)

  val stelWachtwoordIn: Werkzoekende.StelWachtwoordIn = Werkzoekende.StelWachtwoordIn(WerkzoekendeId, wachtwoord)

  val wachtwoordIngesteld: Werkzoekende.WachtwoordIngesteld =
    Werkzoekende.WachtwoordIngesteld(WerkzoekendeId, wachtwoord)

  val wachtwoordIngesteldVoorExternalWerkzoekende: Werkzoekende.WachtwoordIngesteldVoorExternalWerkzoekende =
    Werkzoekende.WachtwoordIngesteldVoorExternalWerkzoekende(WerkzoekendeId, wachtwoord)

  val vergeetWachtwoord: Werkzoekende.VergeetWachtwoord =
    Werkzoekende.VergeetWachtwoord(
      WerkzoekendeId,
      WerkzoekendeEmailAddress,
      Site,
      WachtwoordHerstelUrl,
      StelWachtwoordOpnieuwInToken,
      WachtwoordVergetenTokenId,
      Some(template)
    )

  val wachtwoordVergeten: Werkzoekende.WachtwoordVergeten =
    Werkzoekende.WachtwoordVergeten(
      WerkzoekendeId,
      WerkzoekendeEmailAddress,
      Site,
      WachtwoordHerstelUrl,
      StelWachtwoordOpnieuwInToken,
      WachtwoordVergetenTokenId,
      Some(template)
    )

  val stelWachtwoordOpnieuwIn: Werkzoekende.StelWachtwoordOpnieuwIn =
    Werkzoekende.StelWachtwoordOpnieuwIn(WerkzoekendeId, wachtwoord, WachtwoordVergetenTokenId)

  val wachtwoordOpnieuwIngesteld: Werkzoekende.WachtwoordOpnieuwIngesteld =
    Werkzoekende.WachtwoordOpnieuwIngesteld(WerkzoekendeId, wachtwoord)

  val wijzigWachtwoord    = Werkzoekende.WijzigWachtwoord(WerkzoekendeId, PlainWachtwoord, "nieuw wachtwoord")
  val wachtwoordGewijzigd = Werkzoekende.WachtwoordGewijzigd(WerkzoekendeId, Wachtwoord("new hash", "new salt"))

  val verzoekEMailadresWijziging: Werkzoekende.VerzoekEMailadresWijziging =
    Werkzoekende.VerzoekEMailadresWijziging(
      WerkzoekendeId,
      WerkzoekendeNewEmailAddress,
      VerificatieUrl,
      WijzigEMailadresToken,
      WijzigEMailadresTokenId
    )

  val eMailadresWijzigingVerzocht: Werkzoekende.EMailadresWijzigingVerzocht =
    Werkzoekende.EMailadresWijzigingVerzocht(
      WerkzoekendeId,
      Site,
      WerkzoekendeNewEmailAddress,
      VerificatieUrl,
      WijzigEMailadresToken,
      WijzigEMailadresTokenId
    )

  val bevestigEMailadresWijziging: Werkzoekende.BevestigEMailadresWijziging =
    Werkzoekende.BevestigEMailadresWijziging(WerkzoekendeId, WijzigEMailadresTokenId)

  val eMailadresGewijzigd: Werkzoekende.EMailadresGewijzigd =
    Werkzoekende.EMailadresGewijzigd(WerkzoekendeId, WerkzoekendeNewEmailAddress)

  val schrijfInVoorAlleEMails            = Werkzoekende.SchrijfInVoorAlleEMails(WerkzoekendeId)
  val schrijfInVoorEMail                 = Werkzoekende.SchrijfInVoorEMail(WerkzoekendeId, EMailinschrijvingen.Partner)
  val uitschrijfVoorEmail                = Werkzoekende.UitschrijfVoorEmail(WerkzoekendeId, EMailinschrijvingen.Partner)
  val ingeschrevenVoorPartnerEMail       = Werkzoekende.IngeschrevenVoorEMail(WerkzoekendeId, EMailinschrijvingen.Partner, Site)
  val ingeschrevenVoorNieuwsbrief        = Werkzoekende.IngeschrevenVoorEMail(WerkzoekendeId, EMailinschrijvingen.Nieuwsbrief, Site)
  val ingeschrevenVoorPersoonlijkeEMails = Werkzoekende.IngeschrevenVoorEMail(WerkzoekendeId, EMailinschrijvingen.Persoonlijk, Site)
  val uitgeschrevenVoorEMail             = Werkzoekende.UitgeschrevenVoorEMail(WerkzoekendeId, EMailinschrijvingen.Partner, Site)

  val opzeggingVerzocht: Werkzoekende.OpzeggingVerzocht = Werkzoekende.OpzeggingVerzocht(WerkzoekendeId, SanDiegoId, reden)

  val zegAccountOp: Werkzoekende.ZegAccountOp       = Werkzoekende.ZegAccountOp(WerkzoekendeId, reden)
  val accountOpgezegd: Werkzoekende.AccountOpgezegd = Werkzoekende.AccountOpgezegd(WerkzoekendeId, reden)

  /** Commonly used sequences of events
    */
  val aVerifiedAccountWithPassword: Seq[Event] = Seq(geregistreerd, geverifieerd, wachtwoordIngesteld)
}
