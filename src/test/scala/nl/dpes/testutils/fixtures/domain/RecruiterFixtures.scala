package nl.dpes.testutils.fixtures.domain

import nl.dpes.core.domain.Recruiter._

object RecruiterFixtures {
  val RecruiterId       = "recruiter-id-1"
  val RecruiterId2      = "recruiter-id-2"
  val WerkzoekendeId    = "werkzoekende-id-1"
  val SanDiegoId        = 42
  val TestEMailadres    = "<EMAIL>"
  val ChangedEMailadres = "<EMAIL>"
  val Site              = "nationalevacaturebank.nl"

  def importeer: Importeer                             = Importeer(RecruiterId, SanDiegoId, TestEMailadres, Site)
  def importeerNaarSalesforce: ImporteerNaarSalesforce = ImporteerNaarSalesforce(RecruiterId, TestEMailadres, Site)
  def geimporteerd: Geimporteerd                       = Geimporteerd(RecruiterId, SanDiegoId, TestEMailadres, Site)
  def geregistreerd: Geregistreerd                     = Geregistreerd(RecruiterId, TestEMailadres, Site)
  def registreer: Registreer                           = Registreer(RecruiterId, TestEMailadres, Site)

  def wijzigEMailadres: WijzigEMailadres       = WijzigEMailadres(RecruiterId, ChangedEMailadres)
  def eMailadresGewijzigd: EMailadresGewijzigd = EMailadresGewijzigd(RecruiterId, ChangedEMailadres)

  def maakFavoriet: MaakFavoriet       = MaakFavoriet(RecruiterId, WerkzoekendeId)
  def favorietGemaakt: FavorietGemaakt = FavorietGemaakt(RecruiterId, WerkzoekendeId)

  def verwijderFavoriet: VerwijderFavoriet   = VerwijderFavoriet(RecruiterId, WerkzoekendeId)
  def favorietVerwijderd: FavorietVerwijderd = FavorietVerwijderd(RecruiterId, WerkzoekendeId)

  def verwijder: Verwijder   = Verwijder(RecruiterId)
  def verwijderd: Verwijderd = Verwijderd(RecruiterId)
}
