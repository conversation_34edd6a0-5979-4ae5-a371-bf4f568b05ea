package nl.dpes.testutils.fixtures.services

object SanDiegoClientFixtures {

  val testEmployerRequest1 = List(101, 102)

  val testEmployerResponse1 =
    """
      |{"data":
      | [
      |   {"employerId": 101,"emailAddress": "test1@local","companyId": 10001,"site": "NationaleVacaturebank"},
      |   {"employerId": 102,"emailAddress": "test2@local","companyId": 10002,"site": "NationaleVacaturebank"}
      | ]
      |}
    """.stripMargin

  val testEmployerRequest2 = List(101, 103)

  val testEmployerResponse2 =
    """
      |{"data":
      | [
      |   {"employerId": 101,"emailAddress": "test1@local","companyId": 10001,"site": "NationaleVacaturebank"}
      | ],
      | "unknown_employer_ids": ["103"]
      |}
    """.stripMargin
}
