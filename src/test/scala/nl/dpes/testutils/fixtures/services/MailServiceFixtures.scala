package nl.dpes.testutils.fixtures.services

import nl.dpes.core.domain._
import nl.dpes.core.projections.core.CoreRecruiterAccountProjections.CoreRecruiterAccount
import nl.dpes.core.projections.index.IndexOpgeslagenZoekopdrachtProjections.IndexOpgeslagenZoekopdracht
import nl.dpes.core.services.JobSeekerService.JobSeeker
import nl.dpes.core.services.ndsm.JobSeekerServiceClient.{SearchResult, JobSeeker => ClientJobSeeker}

object MailServiceFixtures {

  val jobSeeker = JobSeeker(
    "job-seeker-id",
    None,
    Sites.Nvb.toString,
    "<PERSON>",
    "Doe",
    "<EMAIL>"
  )

  val jobSeekers = Vector(jobSeeker)

  val recruiter = CoreRecruiterAccount("account-id-1", "<EMAIL>", Sites.Nvb.toString)

  val opgeslagenZoekopdracht = IndexOpgeslagenZoekopdracht(
    "opgeslagen-zoekopdracht-id-1",
    "account-id-1",
    "zoekopdracht-id-1",
    "Name",
    Frequenties.Dagelijks,
    Zoekparameters(Some(Zoektermen(Some(Seq("Scala")))))
  )

  val searchResults = SearchResult(
    Seq(JobSeeker("job-seeker-id", None, Sites.Nvb.toString, "John", "Doe", "<EMAIL>")),
    1,
    locationNotFound = false,
    invalidQuery = false
  )
}
