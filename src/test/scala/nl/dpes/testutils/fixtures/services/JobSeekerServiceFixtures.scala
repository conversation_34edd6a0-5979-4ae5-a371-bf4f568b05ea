package nl.dpes.testutils.fixtures.services

import nl.dpes.core.services.JobSeekerService.JobSeeker

object JobSeekerServiceFixtures {
  val NdsmJobSeekerId           = "6f4fb8e2-9ed1-4abd-a323-bb7e9d8b99ca"
  val LegacyJobSeekerId         = 42
  val NdsmJobSeekerSite         = "intermediair.nl"
  val NdsmJobSeekerFirstName    = "Foo"
  val NdsmJobSeekerLastName     = "Bar"
  val NdsmJobSeekerEmailAddress = "<EMAIL>"

  def ndsmJobSeeker: JobSeeker =
    JobSeeker(
      NdsmJobSeekerId,
      Some(LegacyJobSeekerId),
      NdsmJobSeekerSite,
      NdsmJobSeekerFirstName,
      NdsmJobSeekerLastName,
      NdsmJobSeekerEmailAddress
    )
}
