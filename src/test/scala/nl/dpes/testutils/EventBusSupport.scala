package nl.dpes.testutils

import org.axonframework.eventhandling.{AnnotationEventHandlerAdapter, EventBus, GenericEventMessage, SimpleEventBus}
import org.scalatest.BeforeAndAfterAll

trait EventBusSupport {
  this: BeforeAndAfterAll =>

  private var eventBus: EventBus = _

  protected def service: Any

  override def beforeAll(): Unit = {
    eventBus = SimpleEventBus
      .builder()
      .build()

    val adapter = new AnnotationEventHandlerAdapter(service)
    eventBus.subscribe(eventMessages =>
      eventMessages.forEach { event =>
        adapter.handle(event)
      }
    )
  }

  protected def publish(events: Object*): Unit =
    events.foreach(event => eventBus.publish(GenericEventMessage.asEventMessage(event)))
}
