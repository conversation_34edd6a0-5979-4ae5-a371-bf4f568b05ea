package nl.dpes.testutils

import com.amazonaws.client.builder.AwsClientBuilder.EndpointConfiguration
import com.amazonaws.services.dynamodbv2.model.{Condition, KeyType, ResourceNotFoundException}
import com.amazonaws.services.dynamodbv2.{AmazonDynamoDB, AmazonDynamoDBClientBuilder}
import nl.dpes.core.config.Configuration
import org.scalatest.{BeforeAndAfterAll, BeforeAndAfterEach}

import scala.collection.JavaConverters._
import scala.util.Try

trait DynamoDBSupport {
  this: BeforeAndAfterAll with BeforeAndAfterEach with Configuration =>

  protected lazy val dynamoDbClient: AmazonDynamoDB =
    AmazonDynamoDBClientBuilder
      .standard()
      .withEndpointConfiguration(
        new EndpointConfiguration(DynamoDB.url, DynamoDB.region)
      )
      .build()

  protected def afterEach(tables: Seq[String]): Unit =
    tables.foreach(truncate)

  protected def afterAll(tables: Seq[String]): Unit = {
    tables.foreach(dynamoDbClient.deleteTable)
    dynamoDbClient.shutdown()
  }

  private def truncate(table: String): Unit =
    Try {
      val primaryKey = dynamoDbClient
        .describeTable(table)
        .getTable
        .getKeySchema
        .asScala
        .find(k => k.getKeyType == KeyType.HASH.name())
        .map(_.getAttributeName)
        .get

      val items = dynamoDbClient.scan(table, Map.empty[String, Condition].asJava).getItems.asScala

      items.map(_.asScala).foreach { item =>
        dynamoDbClient.deleteItem(table, Map(primaryKey -> item(primaryKey)).asJava)
      }
    } recover { case _: ResourceNotFoundException =>
      ()
    }
}
