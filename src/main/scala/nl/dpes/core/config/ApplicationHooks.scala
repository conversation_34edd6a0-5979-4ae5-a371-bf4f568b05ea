package nl.dpes.core.config

import nl.dpes.core.config.components.LoggingComponent

import scala.util.Try

trait ApplicationHooks {
  this: LoggingComponent =>

  private val beforeStartHooks: scala.collection.mutable.ArrayBuffer[() => Unit] =
    scala.collection.mutable.ArrayBuffer.empty[() => Unit]

  private val startHooks: scala.collection.mutable.ArrayBuffer[() => Unit] =
    scala.collection.mutable.ArrayBuffer.empty[() => Unit]

  private val afterStartHooks: scala.collection.mutable.ArrayBuffer[() => Unit] =
    scala.collection.mutable.ArrayBuffer.empty[() => Unit]

  private val shutdownHooks: scala.collection.mutable.ArrayBuffer[() => Unit] =
    scala.collection.mutable.ArrayBuffer.empty[() => Unit]

  private val afterShutdownHooks: scala.collection.mutable.ArrayBuffer[() => Unit] =
    scala.collection.mutable.ArrayBuffer.empty[() => Unit]

  def addBeforeStartHook(f: => Unit): Unit   = beforeStartHooks.append(() => f)
  def addStartHook(f: => Unit): Unit         = startHooks.append(() => f)
  def addAfterStartHook(f: => Unit): Unit    = afterStartHooks.append(() => f)
  def addShutdownHook(f: => Unit): Unit      = shutdownHooks.append(() => f)
  def addAfterShutdownHook(f: => Unit): Unit = afterShutdownHooks.append(() => f)

  final def onBeforeStart(): Try[Unit] = Try(beforeStartHooks.foreach(_.apply()))
  final def onStart(): Try[Unit]       = Try(startHooks.foreach(_.apply()))
  final def onAfterStart(): Try[Unit]  = Try(afterStartHooks.foreach(_.apply()))

  final def onShutdown(): Unit = shutdownHooks.foreach { f =>
    Try(f.apply()) recover { case e: Exception =>
      logger.error(e.toString, e)
    }
  }

  final def onAfterShutdown(): Unit = afterShutdownHooks.foreach { f =>
    Try(f.apply()) recover { case e: Exception =>
      logger.error(e.toString, e)
    }
  }
}
