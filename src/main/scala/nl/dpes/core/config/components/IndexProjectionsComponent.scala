package nl.dpes.core.config.components

import com.amazonaws.services.dynamodbv2.document.DynamoDB
import com.sksamuel.elastic4s.RefreshPolicy
import nl.dpes.core.config.{ApplicationHooks, Configuration}
import nl.dpes.core.projections.index.{IndexOpgeslagenZoekopdrachtProjections, IndexRecruiterFavorietProjections}

trait IndexProjectionsComponent {
  this: Configuration with ApplicationHooks with LoggingComponent with DynamoDBComponent with ElasticSearchComponent =>

  addStartHook {
    logger.info("Initializing IndexOpgeslagenZoekopdrachtProjections...")
    indexOpgeslagenZoekopdrachtenProjections.initialize()
    logger.info("IndexOpgeslagenZoekopdrachtProjections initialized")
  }

  addStartHook {
    logger.info("Initializing IndexEsRecruiterFavorietProjections...")
    indexRecruiterFavorietProjections.initialize()
    logger.info("IndexEsRecruiterFavorietProjections initialized")
  }

  lazy val indexOpgeslagenZoekopdrachtenProjections = new IndexOpgeslagenZoekopdrachtProjections(new DynamoDB(dynamoDBClient))

  lazy val indexRecruiterFavorietProjections = new IndexRecruiterFavorietProjections(
    "recruiter-favorieten",
    "favoriet",
    elasticSearchClient,
    RefreshPolicy.IMMEDIATE
  )
}
