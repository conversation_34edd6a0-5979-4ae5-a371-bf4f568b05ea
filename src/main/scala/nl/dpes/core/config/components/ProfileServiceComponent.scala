package nl.dpes.core.config.components

import cats.effect.unsafe.implicits.global
import cats.effect.IO
import doobie.hikari.HikariTransactor
import nl.dpes.core.config.Configuration
import nl.dpes.core.projections.profileservice.{FavoriteProfilesEventHandler, SavedSearchEventHandler}
import nl.dpes.core.repositories.{FavoriteProfilesRepository, SavedSearchRepository}

trait ProfileServiceComponent {
  this: AxonComponent with Configuration with LoggingComponent =>

  implicit val transactor: HikariTransactor[IO] = DatabaseTransactor
    .resource[IO](
      ProfileServiceConfig.dbConnectionString,
      ProfileServiceConfig.dbUsername,
      ProfileServiceConfig.dbPassword,
      ProfileServiceConfig.threadCount
    )
    .allocated
    .unsafeRunSync()
    ._1

  protected[components] implicit lazy val favoriteProfilesRepository: FavoriteProfilesRepository[IO] =
    new FavoriteProfilesRepository(
      ProfileServiceConfig.favoritesTableName,
      transactor
    )

  protected[components] implicit lazy val savedSearchRepository: SavedSearchRepository[IO] =
    new SavedSearchRepository(
      ProfileServiceConfig.savedSearchTableName,
      transactor
    )

  protected[components] implicit lazy val favoriteProfilesEventHandler: FavoriteProfilesEventHandler =
    new FavoriteProfilesEventHandler(favoriteProfilesRepository)

  protected[components] implicit lazy val savedSearchEventHandler: SavedSearchEventHandler =
    new SavedSearchEventHandler(savedSearchRepository)
}
