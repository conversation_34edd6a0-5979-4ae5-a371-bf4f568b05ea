package nl.dpes.core.config.components

import java.io.PrintWriter

import scalikejdbc._
import java.sql.Connection
import java.util.logging.Logger

import javax.sql.DataSource
import nl.dpes.core.config.{ApplicationHooks, Configuration}
import org.axonframework.common.jdbc.{ConnectionProvider, UnitOfWorkAwareConnectionProviderWrapper}

import scala.util.{Failure, Success, Try}

trait MySQLComponent {
  this: Configuration with LoggingComponent with ApplicationHooks =>

  // required to pre-load MySQL driver
  private val driver = Class.forName("com.mysql.jdbc.Driver")

  private val settings = ConnectionPoolSettings(
    initialSize = MySQL.ConnectionPool.initialConnections,
    maxSize = MySQL.ConnectionPool.maxConnections,
    connectionTimeoutMillis = MySQL.connectionTimeout,
    validationQuery = "select 1 from dual"
  )

  ConnectionPool.singleton(
    MySQL.connectionString,
    MySQL.user,
    MySQL.password,
    settings
  )

  private lazy val parentDataSource = ConnectionPool.dataSource()

  lazy val dataSource: DataSource = new DataSource() {

    override def getConnection: Connection = {
      val connection = parentDataSource.getConnection
      connection.setReadOnly(false)
      connection
    }

    override def getConnection(username: String, password: String): Connection = getConnection()

    override def getLogWriter: PrintWriter = parentDataSource.getLogWriter

    override def setLogWriter(printWriter: PrintWriter): Unit = parentDataSource.setLogWriter(printWriter)

    override def setLoginTimeout(i: Int): Unit = parentDataSource.setLoginTimeout(i)

    override def getLoginTimeout: Int = parentDataSource.getLoginTimeout

    override def getParentLogger: Logger = parentDataSource.getParentLogger

    override def unwrap[T](aClass: Class[T]): T = parentDataSource.unwrap(aClass)

    override def isWrapperFor(aClass: Class[_]): Boolean = parentDataSource.isWrapperFor(aClass)
  }

  implicit val session: DBSession = AutoSession

  implicit lazy val connectionProvider: ConnectionProvider = new UnitOfWorkAwareConnectionProviderWrapper(
    new ConnectionProvider {

      override def getConnection: Connection = {
        val connection = ConnectionPool.borrow()
        connection.setReadOnly(false)
        connection
      }
    }
  )

  addAfterShutdownHook {
    logger.info("Close DB connection pool")
    ConnectionPool.closeAll()
    logger.info("DB connection pool closed")
  }
}
