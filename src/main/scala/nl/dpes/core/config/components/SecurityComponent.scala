package nl.dpes.core.config.components

import nl.dpes.core.config.Configuration
import nl.dpes.core.services.sandiego.SanDiegoPasswordHasher
import nl.dpes.core.services.{PasswordService, TokenService}
import nl.dpes.core.services.security.{BCryptPasswordHasher, DefaultPasswordPolicy, JWTProvider, RsaKeys}

trait SecurityComponent {
  this: Configuration with LoggingComponent =>

  val maybeRsaKeys: Either[Throwable, RsaKeys] = RsaKeys.fromStrings(Security.Rsa.privateKey, Security.Rsa.publicKey)

  private val jwt = maybeRsaKeys match {
    case Right(rsaKeys)  => new JWTProvider(Security.JWT.issuer, rsaKeys, Security.Rsa.id)
    case Left(throwable) => throw new Exception(s"RSA keys are not available or cannot be processed: $throwable.")
  }

  lazy val tokenService = new TokenService(jwt)

  lazy val passwordService =
    new PasswordService(new BCryptPasswordHasher(logger), new DefaultPasswordPolicy(Security.minimumPasswordLength))

  lazy val sanDiegoPasswordService = new PasswordService(
    new SanDiegoPasswordHasher(PHP.executable),
    new DefaultPasswordPolicy(Security.minimumPasswordLength)
  )

  lazy val apiKeys = Security.ApiKey.keys
}
