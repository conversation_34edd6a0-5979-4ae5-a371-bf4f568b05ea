package nl.dpes.core.config.components

import nl.dpes.core.config.Configuration
import nl.dpes.core.services.{SimService, TrackingSimService}
import nl.dpes.core.services.sim.HttpSimClient

trait SimComponent {
  this: CoreProjectionsComponent with Configuration with <PERSON>Component with LoggingComponent with HttpComponent =>

  lazy val simClient                              = new HttpSimClient(s"http://${SIM.host}:${SIM.port}${SIM.basePath}")
  lazy val simService: SimService                 = new SimService(simClient, werkzoekendeAccountProjections)
  lazy val trackingSimService: TrackingSimService = new TrackingSimService(simClient)
}
