package nl.dpes.core.config.components

import nl.dpes.core.config.Configuration
import nl.dpes.core.services.notifier.{LoggingNotifier, Notifier, SlackNotifier}
import sttp.model.Uri

trait NotifierComponent {
  this: Configuration with LoggingComponent with SystemComponent with HttpComponent =>

  lazy val eventNotifier: Notifier = {
    if (Notifier.Events.webhookUrl.nonEmpty) {
      new SlackNotifier(
        Notifier.Events.level,
        parseOrFail(Notifier.Events.webhookUrl),
        Notifier.Events.channel,
        Notifier.Events.username,
        Notifier.Events.icon
      )
    } else {
      new LoggingNotifier(logger, Notifier.Events.level)
    }
  }

  lazy val rebuildNotifier: Notifier = {
    if (Notifier.Rebuild.webhookUrl.nonEmpty) {
      new SlackNotifier(
        Notifier.Rebuild.level,
        parseOrFail(Notifier.Rebuild.webhookUrl),
        Notifier.Rebuild.channel,
        Notifier.Rebuild.username,
        Notifier.Rebuild.icon
      )
    } else {
      new LoggingNotifier(logger, Notifier.Rebuild.level)
    }
  }

  // We throw an exception to preserve the logic of the previous (akka) client logic
  private def parseOrFail(uri: String): Uri = Uri.parse(uri) match {
    case Right(parsedUri) => parsedUri
    case Left(error)      => throw new Exception(s"Illegal URI exception: $error")
  }
}
