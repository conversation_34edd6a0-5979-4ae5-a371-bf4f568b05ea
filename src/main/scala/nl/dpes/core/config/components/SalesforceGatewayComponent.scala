package nl.dpes.core.config.components

import akka.grpc.GrpcClientSettings
import nl.dpes.core.config.Configuration
import nl.dpes.b2b.salesforce.service._

trait SalesforceGatewayComponent {
  this: Configuration with SystemComponent with LoggingComponent =>

  private lazy val settings: GrpcClientSettings = {
    logger.info(s"Connecting to SalesforceGateway: grpc://${SalesforceGateway.host}:${SalesforceGateway.grpcPort}")
    GrpcClientSettings.connectToServiceAt(SalesforceGateway.host, SalesforceGateway.grpcPort)
  }.withTls(false)

  lazy val b2bRecruiterService: RecruiterService = new GrpcRecruiterService(
    nl.dpes.b2b.salesforce.v1.RecruiterServiceClient(settings)
  )
}
