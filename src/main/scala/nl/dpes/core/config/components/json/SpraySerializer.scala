package nl.dpes.core.config.components.json

import nl.dpes.core.domain.Event
import org.axonframework.serialization._
import org.slf4j.LoggerFactory
import spray.json.{JsValue, RootJsonFormat, _}

import java.nio.charset.StandardCharsets
import scala.reflect.ClassTag
import scala.util.Try
import scala.util.control.NonFatal
import nl.dpes.core.domain._
import JsonProtocol._
import nl.dpes.core.domain.OpgeslagenZoekopdrachtSaga.FrequentietijdVerstreken
import nl.dpes.core.domain.Recruiter.{EMailadresGewijzigd, Geregistreerd}
import nl.dpes.core.domain.Werkzoekende.{GeregistreerdExternal, IngeschrevenVoorEMail}
import nl.dpes.core.domain.Zoekopdracht.{Gewijzigd, OpgeslagenDoorRecruiter, VerwijderdDoorRecruiter}
import nl.dpes.core.domain.werkzoekende.VerificatieSaga

import scala.language.existentials

object DefaultConverters {

  val converter = {
    val charset = StandardCharsets.UTF_8
    new ChainingConverter() {
      registerConverter(new ByteArrayToJsValueConverter(charset))
      registerConverter(new JsValueToByteArrayConverter(charset))
    }
  }
}

class SpraySerializer(
  revisionResolver: RevisionResolver = new AnnotationRevisionResolver,
  converter: Converter = DefaultConverters.converter
) extends Serializer {

  import SpraySerializer._

  val logger = LoggerFactory.getLogger(classOf[SpraySerializer])

  override def serialize[R](value: Any, expectedRepresentation: Class[R]): SerializedObject[R] = {
    implicit def anyAsJobEvent[T <: Event](value: Any): T = value.asInstanceOf[T]

    try expectedRepresentation match {
      case representation if representation.equals(classOf[String]) =>
        new SimpleSerializedObject(
          JsonFormattingHelper.writeJson(value).toString().asInstanceOf[R],
          expectedRepresentation,
          typeForClass(value.getClass)
        )

      case _ =>
        new SimpleSerializedObject(
          converter.convert(
            JsonFormattingHelper.writeJson(value).compactPrint.getBytes(),
            expectedRepresentation
          ),
          expectedRepresentation,
          typeForClass(value.getClass)
        )
    } catch {
      case NonFatal(t) =>
        logger.error(
          s"Failed to serialize object from class ${value.getClass} to expectedRepresentation $expectedRepresentation, value:\n$value",
          t
        )
        throw new org.axonframework.serialization.SerializationException("Unable to serialize object")
    }
  }

  override def canSerializeTo[T](expectedRepresentation: Class[T]): Boolean =
    classOf[JsValue].equals(expectedRepresentation) || classOf[String].equals(expectedRepresentation) ||
    converter.canConvert(classOf[Array[Byte]], expectedRepresentation)

  override def deserialize[S, T](serializedObject: SerializedObject[S]): T =
    try if (SerializedType.emptyType.equals(serializedObject.getType)) {
      null.asInstanceOf[T]
    } else {
      val typeOfSerializedObject: Class[_] = classForType(serializedObject.getType)
      if (classOf[UnknownSerializedType].isAssignableFrom(typeOfSerializedObject)) {
        new UnknownSerializedType(this, serializedObject).asInstanceOf[T]
      } else {
        if (classOf[JsValue].equals(serializedObject.getContentType)) {
          JsonFormattingHelper.readJson[T](serializedObject.getData.asInstanceOf[JsValue], typeOfSerializedObject)
        } else {
          val byteSerialized: SerializedObject[Array[Byte]] = converter.convert(serializedObject, classOf[Array[Byte]])
          JsonFormattingHelper.readJson[T](converter.convert(byteSerialized.getData, classOf[JsValue]), typeOfSerializedObject)
        }
      }
    } catch {
      case NonFatal(e) => throw new org.axonframework.serialization.SerializationException("Error while deserializing object", e)
    }

  override def classForType(`type`: SerializedType): Class[_] =
    if (SerializedType.isEmptyType(`type`)) {
      classOf[Void]
    } else {
      Try {
        Class.forName(`type`.getName, true, Thread.currentThread.getContextClassLoader)
      }.recover { case _: ClassNotFoundException =>
        classOf[UnknownSerializedType]
      }.get
    }

  override def typeForClass(`type`: Class[_]): SerializedType = `type` match {
    case null                         => SimpleSerializedType.emptyType
    case java.lang.Void.TYPE          => SimpleSerializedType.emptyType
    case e if e.equals(classOf[Void]) => SimpleSerializedType.emptyType
    case clazz =>
      new SimpleSerializedType(
        clazz.getName,
        revisionResolver.revisionOf(clazz)
      )
  }

  override def getConverter: Converter = converter
}

object SpraySerializer {

  object JsonFormattingHelper {

    def format[A](implicit tag: ClassTag[A], fmt: RootJsonFormat[A]) = tag.runtimeClass -> fmt

    val objMap: Map[Class[_], RootJsonFormat[_]] = Map(
      // events
      format[Geregistreerd],
      format[GeregistreerdExternal],
      format[VerwijderdDoorRecruiter],
      format[FrequentietijdVerstreken],
      format[OpgeslagenDoorRecruiter],
      format[EMailadresGewijzigd],
      format[Recruiter.Verwijderd],
      format[Recruiter.FavorietGemaakt],
      format[Recruiter.Geimporteerd],
      format[Recruiter.FavorietVerwijderd],
      format[Werkzoekende.EMailadresWijzigingVerzocht],
      format[Werkzoekende.OpzeggingVerzocht],
      format[Werkzoekende.AccountOpgezegd],
      format[Werkzoekende.WachtwoordOpnieuwIngesteld],
      format[Werkzoekende.Geregistreerd],
      format[Werkzoekende.Geverifieerd],
      format[Werkzoekende.EMailadresGewijzigd],
      format[Werkzoekende.WachtwoordVergeten],
      format[Werkzoekende.AccountGeimporteerd],
      format[Werkzoekende.WachtwoordIngesteld],
      format[Werkzoekende.WachtwoordIngesteldVoorExternalWerkzoekende],
      format[Werkzoekende.WachtwoordGewijzigd],
      format[Zoekopdracht.AangemaaktDoorRecruiter],
      format[Zoekopdracht.Verwijderd],
      format[Zoekopdracht.Gewijzigd],
      format[IngeschrevenVoorEMail],
      //sagas
      format[VerificatieSaga],
      format[RecruiterFavorietSaga],
      format[OpgeslagenZoekopdrachtSaga]
    )

    private def getReaderFor[T](clazz: Class[T]): JsonReader[T] = objMap(clazz).asInstanceOf[RootJsonReader[T]]

    def readJson[T](value: JsValue, _type: Class[_]): T =
      getReaderFor(_type).read(value).asInstanceOf[T]

    private def getWriterFor[T](clazz: Class[_]): RootJsonWriter[T] = objMap(clazz).asInstanceOf[RootJsonWriter[T]]

    implicit def writeJson(event: Any): JsValue =
      getWriterFor(event.getClass).write(event)
  }

}
