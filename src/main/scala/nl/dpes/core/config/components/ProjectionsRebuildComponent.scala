package nl.dpes.core.config.components

import com.amazonaws.services.dynamodbv2.document.DynamoDB
import com.thoughtworks.xstream.XStream
import nl.dpes.core.config.{ApplicationHooks, Configuration}
import nl.dpes.core.projections.rebuilding._
import nl.dpes.core.repositories.TokenRepository.{CoreProjectionsRebuildToken, IndexProjectionsRebuildToken}
import nl.dpes.core.repositories.VersionRepository
import nl.dpes.utils.events.upcasting.zoekopdracht.SerializationHelper
import nl.dpes.utils.events.upcasting.zoekopdracht.SerializationHelper.ListSerializeEnd
import org.axonframework.eventhandling.TrackingToken
import org.axonframework.eventhandling.tokenstore.jdbc.JdbcTokenStore
import org.axonframework.serialization.xml.{CompactDriver, XStreamSerializer}

import java.time.Duration
import scala.concurrent.Future

trait ProjectionsRebuildComponent {
  this: Configuration
    with ApplicationHooks
    with LoggingComponent
    with AxonComponent
    with DynamoDBComponent
    with MySQLComponent
    with TokenRepositoryComponent
    with NotifierComponent =>

  addBeforeStartHook {
    rebuild()
  }

  addShutdownHook {
    tokenRepository.release(CoreProjectionsRebuildToken)
    tokenRepository.release(IndexProjectionsRebuildToken)
  }

  implicit private lazy val versionRepository: VersionRepository = new VersionRepository(new DynamoDB(dynamoDBClient))
  implicit private lazy val tokenEntryService: TokenEntryService = new TokenEntryService()

  private lazy val xstream = new XStream(new CompactDriver)
  xstream.alias("scala.collection.immutable.List$SerializationProxy", classOf[SerializationHelper[_]])
  xstream.alias("scala.collection.immutable.ListSerializeEnd$", ListSerializeEnd.getClass)
  xstream.allowTypesByWildcard(Array("nl.dpes.**", "scala.**", "cats.**", "ch.qos.logback.**"))
  xstream.ignoreUnknownElements()
  private lazy val xstreamSerializer: XStreamSerializer = XStreamSerializer.builder().xStream(xstream).build()

  lazy val token: TrackingToken = JdbcTokenStore
    .builder()
    .claimTimeout(Duration.ofMinutes(1))
    .connectionProvider(connectionProvider)
    .serializer(xstreamSerializer)
    .build()
    .fetchToken(CoreProjectionsRebuildToken.name, 0)

  private def rebuild(): Future[Unit] = Future.unit
}
