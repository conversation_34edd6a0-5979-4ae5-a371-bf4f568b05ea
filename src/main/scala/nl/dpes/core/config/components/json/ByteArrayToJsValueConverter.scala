package nl.dpes.core.config.components.json

import org.axonframework.serialization.ContentTypeConverter
import spray.json._

import java.nio.charset.Charset

class ByteArrayToJsValueConverter(charset: Charset) extends ContentTypeConverter[Array[Byte], JsValue] {
  override def convert(original: Array[Byte]): JsValue = new String(original, charset).parseJson

  override def expectedSourceType(): Class[Array[Byte]] = classOf[Array[Byte]]

  override def targetType(): Class[JsValue] = classOf[JsValue]
}
