package nl.dpes.core.config.components

import cats.effect._
import doobie.ExecutionContexts
import doobie.hikari.HikariTransactor

object DatabaseTransactor {

  def resource[F[_]: Async](url: String, username: String, password: String, threadCount: Int): Resource[F, HikariTransactor[F]] =
    for {
      ec <- ExecutionContexts.fixedThreadPool(threadCount)
      xa <- HikariTransactor.newHikariTransactor[F](
        "com.mysql.jdbc.Driver",
        url,
        username,
        password,
        ec
      )
    } yield xa
}
