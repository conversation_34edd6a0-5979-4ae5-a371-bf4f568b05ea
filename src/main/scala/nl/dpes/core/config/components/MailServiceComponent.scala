package nl.dpes.core.config.components

import nl.dpes.core.config.Configuration
import nl.dpes.core.services.MailService
import nl.dpes.core.services.mail.HttpMailServiceClient

trait MailServiceComponent {
  this: Configuration with <PERSON>Component with LoggingComponent with HttpComponent =>

  private lazy val mailServiceClient = new HttpMailServiceClient(MailService.url)
  lazy val mailService               = new MailService(mailServiceClient)
}
