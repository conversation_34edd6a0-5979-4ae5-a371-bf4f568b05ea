package nl.dpes.core.config.components

import com.sksamuel.elastic4s.http.{ElasticClient, ElasticProperties}
import nl.dpes.core.config.{ApplicationHooks, Configuration}

trait ElasticSearchComponent {
  this: LoggingComponent with ApplicationHooks with Configuration =>

  implicit lazy val elasticSearchClient = ElasticClient(ElasticProperties(s"http://${ElasticSearch.url}:${ElasticSearch.port}"))

  addAfterShutdownHook {
    logger.info("Shutting down ElasticSearch client")
    elasticSearchClient.close()
    logger.info("Shut down ElasticSearch client")
  }
}
