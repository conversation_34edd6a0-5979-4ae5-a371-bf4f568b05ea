package nl.dpes.core.config.components

import nl.dpes.core.config.{ApplicationHooks, Configuration}
import nl.dpes.core.projections.rebuilding.Monitor
import org.axonframework.commandhandling.SimpleCommandBus
import org.axonframework.config.Configurer
import org.axonframework.messaging.Message
import org.axonframework.monitoring.MultiMessageMonitor

trait ReplayAxonComponent extends AxonComponent {
  this: Configuration
    with LoggingComponent
    with ApplicationHooks
    with IdentifierComponent
    with CoreProjectionsComponent
    with IndexProjectionsComponent
    with ProjectionsRebuildComponent
    with ProfileServiceComponent
    with MySQLComponent
    with SystemComponent
    with NotifierComponent
    with SecurityComponent
    with EventPublisherComponent =>

  override def axonConfigurer: Configurer = super.axonConfigurer
//    .configureMessageMonitor(classOf[MultiMessageMonitor[_]], (_: org.axonframework.config.Configuration) => new MultiMessageMonitor[Message[_]](
//      new Monitor(logger),
//      new Monitor(rebuildNotifier)
//    ))
    .configureCommandBus(_ => SimpleCommandBus.builder().build())
}
