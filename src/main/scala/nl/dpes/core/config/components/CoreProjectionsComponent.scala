package nl.dpes.core.config.components

import com.amazonaws.services.dynamodbv2.document.DynamoDB
import com.thoughtworks.xstream.XStream
import nl.dpes.core.config.{ApplicationHooks, Configuration}
import nl.dpes.core.projections.core._
import nl.dpes.utils.events.upcasting.zoekopdracht.SerializationHelper
import nl.dpes.utils.events.upcasting.zoekopdracht.SerializationHelper.ListSerializeEnd
import org.axonframework.serialization.xml.{CompactDriver, XStreamSerializer}

trait CoreProjectionsComponent {
  this: DynamoDBComponent with LoggingComponent with ApplicationHooks with Configuration with LoggingComponent =>

  addStartHook {
    logger.info("Initializing CoreWerkzoekendeAccountProjections...")
    werkzoekendeAccountProjections.initialize()
    logger.info("CoreWerkzoekendeAccountProjections initialized")
  }

  addStartHook {
    logger.info("Initializing CoreZoekopdrachtProjections...")
    zoekopdrachtProjections.initialize()
    logger.info("CoreZoekopdrachtProjections initialized")
  }

  addStartHook {
    logger.info("Initializing CoreRecruiterAccountProjections...")
    recruiterAccountProjections.initialize()
    logger.info("CoreRecruiterAccountProjections initialized")
  }

  addStartHook {
    logger.info("Initializing CoreSanDiegoEmployerProjections...")
    sanDiegoEmployerProjections.initialize()
    logger.info("CoreSanDiegoEmployerProjections initialized")
  }

  val xstream = new XStream(new CompactDriver)
  xstream.alias("scala.collection.immutable.List$SerializationProxy", classOf[SerializationHelper[_]])
  xstream.alias("scala.collection.immutable.ListSerializeEnd$", ListSerializeEnd.getClass)
  xstream.allowTypesByWildcard(Array("nl.dpes.**", "scala.**", "cats.**", "ch.qos.logback.**"))
  xstream.ignoreUnknownElements()

  private lazy val serializer: XStreamSerializer = XStreamSerializer.builder().xStream(xstream).build()

  lazy val werkzoekendeAccountProjections = new CoreWerkzoekendeAccountProjections(new DynamoDB(dynamoDBClient))
  lazy val recruiterAccountProjections    = new CoreRecruiterAccountProjections(new DynamoDB(dynamoDBClient))
  lazy val zoekopdrachtProjections        = new CoreZoekopdrachtProjections(new DynamoDB(dynamoDBClient), serializer)
  lazy val sanDiegoEmployerProjections    = new CoreSanDiegoEmployerProjections(new DynamoDB(dynamoDBClient))
}
