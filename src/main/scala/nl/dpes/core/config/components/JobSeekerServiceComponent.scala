package nl.dpes.core.config.components

import nl.dpes.core.config.Configuration
import nl.dpes.core.services.JobSeekerService
import nl.dpes.core.services.ndsm.HttpJobSeekerServiceClient

trait JobSeekerServiceComponent {
  this: Configuration with SystemComponent with LoggingComponent with HttpComponent =>

  private lazy val jobSeekerServiceClient =
    new HttpJobSeekerServiceClient(s"http://${JobSeekerService.url}/api/v1", JobSeekerService.apiKey)
  lazy val jobSeekerService = new JobSeekerService(jobSeekerServiceClient)
}
