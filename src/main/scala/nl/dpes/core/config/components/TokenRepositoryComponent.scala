package nl.dpes.core.config.components

import com.amazonaws.services.dynamodbv2.document.DynamoDB
import nl.dpes.core.config.{Configuration, Environment}
import nl.dpes.core.repositories.TokenRepository

trait TokenRepositoryComponent {
  this: DynamoDBComponent with Configuration with LoggingComponent =>

  protected[components] implicit lazy val tokenRepository: TokenRepository =
    new TokenRepository(new DynamoDB(dynamoDBClient), Application.nodeName)
}
