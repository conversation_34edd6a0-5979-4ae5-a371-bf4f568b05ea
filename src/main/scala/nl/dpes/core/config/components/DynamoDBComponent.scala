package nl.dpes.core.config.components

import com.amazonaws.auth.DefaultAWSCredentialsProviderChain
import com.amazonaws.client.builder.AwsClientBuilder.EndpointConfiguration
import com.amazonaws.services.dynamodbv2.{AmazonDynamoDB, AmazonDynamoDBClientBuilder}
import nl.dpes.core.config.{ApplicationHooks, Configuration}

trait DynamoDBComponent {
  this: LoggingComponent with ApplicationHooks with Configuration =>

  lazy val dynamoDBClient: AmazonDynamoDB = {
    val client = AmazonDynamoDBClientBuilder
      .standard()
      .withCredentials(new DefaultAWSCredentialsProviderChain)

    if (DynamoDB.url.nonEmpty) {
      client.withEndpointConfiguration(
        new EndpointConfiguration(
          DynamoDB.url,
          DynamoDB.region
        )
      )
    }

    client.build()
  }

  addAfterShutdownHook {
    logger.info("Shutting down DynamoDB client")
    dynamoDBClient.shutdown()
    logger.info("Shut down DynamoDB client")
  }
}
