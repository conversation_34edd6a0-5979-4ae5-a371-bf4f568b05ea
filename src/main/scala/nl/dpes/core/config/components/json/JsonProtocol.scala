package nl.dpes.core.config.components.json

import nl.dpes.core.domain._
import nl.dpes.core.domain.Frequenties
import nl.dpes.core.domain.OpgeslagenZoekopdrachtSaga.FrequentietijdVerstreken
import nl.dpes.core.domain.Recruiter.Geregistreerd
import nl.dpes.core.domain.Werkzoekende.{EMailadresGewijzigd, IngeschrevenVoorEMail, WachtwoordGewijzigd}
import nl.dpes.core.domain.Zoekopdracht.{OpgeslagenDoorRecruiter, VerwijderdDoorRecruiter}
import nl.dpes.core.domain.{<PERSON><PERSON><PERSON><PERSON>, <PERSON>kparameters, Zoektermen}
import spray.json.{DefaultJsonProtocol, DeserializationException, JsNull, JsString, JsValue, JsonFormat, RootJsonFormat}

object JsonProtocol extends DefaultJsonProtocol {

  implicit def nullableOptionFormat[T](implicit default: JsonFormat[Option[T]]): JsonFormat[Option[T]] =
    new JsonFormat[Option[T]] {
      final val nullMarker: JsValue = JsString("null")

      override def write(obj: Option[T]): JsValue = obj match {
        case null => nullMarker
        case _    => default.write(obj)
      }

      override def read(json: JsValue): Option[T] = json match {
        case `nullMarker` => null
        case _            => default.read(json)
      }
    }

  implicit def frequentieFmt: JsonFormat[Frequenties.Frequentie] = new EnumFormat(Frequenties)

  private implicit def sitesFmt: JsonFormat[nl.dpes.core.domain.Sites.Site] = new EnumFormat(nl.dpes.core.domain.Sites)

  implicit lazy val frequentietijdVerstrekenFmt: RootJsonFormat[FrequentietijdVerstreken] = jsonFormat2(FrequentietijdVerstreken.apply)
  implicit lazy val zoektermen: RootJsonFormat[Zoektermen]                                = jsonFormat7(Zoektermen.apply)
  implicit lazy val zoekparameters: RootJsonFormat[Zoekparameters]                        = jsonFormat14(Zoekparameters.apply)
  implicit lazy val verwijderd: RootJsonFormat[Recruiter.Verwijderd]                      = jsonFormat1(Recruiter.Verwijderd.apply)
  implicit lazy val EMailadresFmt: RootJsonFormat[EMailadres]                             = jsonFormat1(EMailadres.apply)
  implicit lazy val WachtwoordFmt: RootJsonFormat[Wachtwoord]                             = jsonFormat3(Wachtwoord.apply)

  implicit val FavorietGemaaktFmt: RootJsonFormat[Recruiter.FavorietGemaakt]       = jsonFormat2(Recruiter.FavorietGemaakt.apply)
  implicit val GeimporteerdFmt: RootJsonFormat[Recruiter.Geimporteerd]             = jsonFormat4(Recruiter.Geimporteerd.apply)
  implicit val FavorietVerwijderdFmt: RootJsonFormat[Recruiter.FavorietVerwijderd] = jsonFormat2(Recruiter.FavorietVerwijderd.apply)

  implicit val recruiterEMailadresGewijzigd: RootJsonFormat[Recruiter.EMailadresGewijzigd] = jsonFormat2(
    Recruiter.EMailadresGewijzigd.apply
  )

  implicit val EMailadresWijzigingVerzochtFmt: RootJsonFormat[Werkzoekende.EMailadresWijzigingVerzocht] = jsonFormat6(
    Werkzoekende.EMailadresWijzigingVerzocht.apply
  )
  implicit val OpzeggingVerzochtFmt: RootJsonFormat[Werkzoekende.OpzeggingVerzocht] = jsonFormat3(Werkzoekende.OpzeggingVerzocht.apply)
  implicit val AccountOpgezegdFmt: RootJsonFormat[Werkzoekende.AccountOpgezegd]     = jsonFormat2(Werkzoekende.AccountOpgezegd.apply)

  implicit val WachtwoordOpnieuwIngesteldFmt: RootJsonFormat[Werkzoekende.WachtwoordOpnieuwIngesteld] = jsonFormat2(
    Werkzoekende.WachtwoordOpnieuwIngesteld.apply
  )
  implicit val GeregistreerdFmt: RootJsonFormat[Werkzoekende.Geregistreerd] = jsonFormat5(Werkzoekende.Geregistreerd.apply)

  implicit val GeregistreerdExternalFmt: RootJsonFormat[Werkzoekende.GeregistreerdExternal] = jsonFormat3(
    Werkzoekende.GeregistreerdExternal.apply
  )

  implicit val GeverifieerdFmt: RootJsonFormat[Werkzoekende.Geverifieerd] = jsonFormat1(Werkzoekende.Geverifieerd.apply)

  implicit val EMailadresGewijzigdFmt: RootJsonFormat[Werkzoekende.EMailadresGewijzigd] = jsonFormat2(
    Werkzoekende.EMailadresGewijzigd.apply
  )
  implicit val WachtwoordVergetenFmt: RootJsonFormat[Werkzoekende.WachtwoordVergeten] = jsonFormat7(Werkzoekende.WachtwoordVergeten.apply)

  implicit val AccountGeimporteerdFmt: RootJsonFormat[Werkzoekende.AccountGeimporteerd] = jsonFormat5(
    Werkzoekende.AccountGeimporteerd.apply
  )

  implicit val WachtwoordIngesteldFmt: RootJsonFormat[Werkzoekende.WachtwoordIngesteld] = jsonFormat2(
    Werkzoekende.WachtwoordIngesteld.apply
  )

  implicit val wachtwoordIngesteldVoorExternalWerkzoekendeFmt: RootJsonFormat[Werkzoekende.WachtwoordIngesteldVoorExternalWerkzoekende] =
    jsonFormat2(Werkzoekende.WachtwoordIngesteldVoorExternalWerkzoekende.apply)

  implicit val AangemaaktDoorRecruiterFmt: RootJsonFormat[Zoekopdracht.AangemaaktDoorRecruiter] = jsonFormat2(
    Zoekopdracht.AangemaaktDoorRecruiter.apply
  )

  implicit val VerwijderdDoorRecruiterFmt: RootJsonFormat[Zoekopdracht.VerwijderdDoorRecruiter] = jsonFormat2(
    Zoekopdracht.VerwijderdDoorRecruiter.apply
  )
  implicit val VerwijderdFmt: RootJsonFormat[Zoekopdracht.Verwijderd]             = jsonFormat1(Zoekopdracht.Verwijderd.apply)
  implicit val GewijzigdFmt: RootJsonFormat[Zoekopdracht.Gewijzigd]               = jsonFormat4(Zoekopdracht.Gewijzigd.apply)
  implicit val GeregistreerdFormat: RootJsonFormat[Geregistreerd]                 = jsonFormat3(Geregistreerd.apply)
  implicit val WachtwoordGewijzigdFormat: RootJsonFormat[WachtwoordGewijzigd]     = jsonFormat2(WachtwoordGewijzigd.apply)
  implicit val IngeschrevenVoorEMailFormat: RootJsonFormat[IngeschrevenVoorEMail] = jsonFormat3(IngeschrevenVoorEMail.apply)

  implicit val OpgeslagenDoorRecruiterFmt: RootJsonFormat[Zoekopdracht.OpgeslagenDoorRecruiter] = jsonFormat5(
    Zoekopdracht.OpgeslagenDoorRecruiter.apply
  )

  private class EnumFormat[T <: scala.Enumeration](enu: T) extends RootJsonFormat[T#Value] {
    override def write(obj: T#Value): JsValue = JsString(obj.toString)

    override def read(json: JsValue): T#Value =
      json match {
        case JsString(txt) => enu.withName(txt)
        case somethingElse => throw DeserializationException(s"Expected a value from enum $enu instead of $somethingElse")
      }
  }
}
