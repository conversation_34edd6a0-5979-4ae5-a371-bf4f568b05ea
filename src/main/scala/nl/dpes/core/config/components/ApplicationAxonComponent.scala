package nl.dpes.core.config.components

import nl.dpes.b2b.salesforce.service.RecruiterService
import nl.dpes.core.config.{ApplicationHooks, Configuration}
import nl.dpes.core.domain._
import nl.dpes.core.domain.werkzoekende.VerificatieSaga
import nl.dpes.core.services.{GDPRService, MailService, SavedSearch, TrackingSimService}
import org.axonframework.commandhandling.SimpleCommandBus
import org.axonframework.common.transaction.NoTransactionManager
import org.axonframework.config.{AggregateConfigurer, Configurer, EventProcessingConfigurer}
import org.axonframework.eventhandling.{PropagatingErrorHand<PERSON>, SubscribingEventProcessor}
import org.axonframework.modelling.saga.repository.SagaStore
import org.axonframework.modelling.saga.repository.jdbc.{GenericSagaSqlSchema, JdbcSagaStore}

trait ApplicationAxonComponent extends AxonComponent {
  this: Configuration
    with LoggingComponent
    with ApplicationHooks
    with IdentifierComponent
    with CoreProjectionsComponent
    with IndexProjectionsComponent
    with MailServiceComponent
    with MySQLComponent
    with ProfileServiceComponent
    with SystemComponent
    with NotifierComponent
    with SecurityComponent
    with SimComponent
    with SavedSearchComponent
    with JobSeekerServiceComponent
    with EventPublisherComponent
    with SalesforceGatewayComponent =>

  override def axonConfigurer: Configurer = {
    def registerSaga[T](eventProcessingConfiguration: EventProcessingConfigurer, sagaClass: Class[T]): Unit = {
      eventProcessingConfiguration.registerEventProcessor(sagaClass.getSimpleName, createTrackingEventProcessor)
      eventProcessingConfiguration.assignHandlerTypesMatching(sagaClass.getSimpleName, (c: Class[_]) => c == sagaClass)
      eventProcessingConfiguration.registerSaga(sagaClass)
    }

    lazy val sagaStore = {
      val sagaStore = JdbcSagaStore
        .builder()
        .connectionProvider(connectionProvider)
        .sqlSchema(new GenericSagaSqlSchema())
        .serializer(serializer)
        .build()

      if (!connectionProvider.getConnection.getMetaData.getTables(null, null, "SagaEntry", null).next()) {
        sagaStore.createSchema()
      }

      sagaStore
    }

    val configurer: Configurer = super.axonConfigurer
      .configureCommandBus(_ =>
        SimpleCommandBus
          .builder()
          .messageMonitor(messageMonitor)
          .build()
      )
      .registerComponent(classOf[MailService], _ => mailService)
      .registerComponent(classOf[RecruiterService], _ => b2bRecruiterService)
      .registerComponent(classOf[SavedSearch], _ => savedSearchService)
      .registerComponent(classOf[SagaStore[_]], _ => sagaStore)

      // Sagas
      .eventProcessing(eventProcessingConfigurer => registerSaga(eventProcessingConfigurer, classOf[OpgeslagenZoekopdrachtSaga]))
      .eventProcessing(eventProcessingConfigurer => registerSaga(eventProcessingConfigurer, classOf[RecruiterFavorietSaga]))
      .eventProcessing(eventProcessingConfigurer => registerSaga(eventProcessingConfigurer, classOf[VerificatieSaga]))

      // Aggregates
      .configureAggregate(AggregateConfigurer.defaultConfiguration(classOf[Werkzoekende]))
      .configureAggregate(AggregateConfigurer.defaultConfiguration(classOf[Recruiter]))
      .configureAggregate(AggregateConfigurer.defaultConfiguration(classOf[Zoekopdracht]))

      // External handlers
      .eventProcessing { eventProcessingConfigurer =>
        val name = "subscribing"
        eventProcessingConfigurer.byDefaultAssignTo(name)
        eventProcessingConfigurer.registerEventHandler(_ => mailService)
        eventProcessingConfigurer.registerEventHandler(_ => simService)
        eventProcessingConfigurer.registerEventHandler(_ => jobSeekerService)
        eventProcessingConfigurer.registerEventHandler(_ => favoriteProfilesEventHandler)

        eventProcessingConfigurer.registerEventProcessor(
          name,
          (name, configuration, eventHandlerInvoker) =>
            SubscribingEventProcessor
              .builder()
              .name(name)
              .errorHandler(PropagatingErrorHandler.INSTANCE)
              .eventHandlerInvoker(eventHandlerInvoker)
//          .messageMonitor(messageMonitor)
              .messageSource(configuration.eventBus())
              .transactionManager(NoTransactionManager.INSTANCE)
              .build()
        )
      }

    // tracking SIM
    configurer.eventProcessing { eventProcessingConfigurer =>
      val name = classOf[TrackingSimService].getSimpleName

      eventProcessingConfigurer.assignHandlerInstancesMatching(name, (t: Any) => t == trackingSimService)
      eventProcessingConfigurer.registerEventHandler(_ => trackingSimService)

      eventProcessingConfigurer.registerEventProcessor(name, createTrackingEventProcessor)
    }

    // GDPR Service
    configurer.eventProcessing { eventProcessingConfigurer =>
      val name         = classOf[GDPRService].getSimpleName
      val eventHandler = new GDPRService(cryptoEngine)
      eventProcessingConfigurer.assignHandlerInstancesMatching(name, (t: Any) => t == eventHandler)
      eventProcessingConfigurer.registerEventHandler(_ => eventHandler)

      eventProcessingConfigurer.registerEventProcessor(name, createTrackingEventProcessor)
    }

    configurer
  }

  override def axonShutdown(): Unit =
    super.axonShutdown()
}
