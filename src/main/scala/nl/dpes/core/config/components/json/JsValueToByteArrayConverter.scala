package nl.dpes.core.config.components.json

import org.axonframework.serialization.ContentTypeConverter
import spray.json.JsValue

import java.nio.charset.Charset

class JsValueToByteArrayConverter(charset: Charset) extends ContentTypeConverter[JsValue, Array[Byte]] {

  override def convert(original: JsValue): Array[Byte] = original.compactPrint.getBytes(charset)

  override def expectedSourceType(): Class[JsValue] = classOf[JsValue]

  override def targetType(): Class[Array[Byte]] = classOf[Array[Byte]]
}
