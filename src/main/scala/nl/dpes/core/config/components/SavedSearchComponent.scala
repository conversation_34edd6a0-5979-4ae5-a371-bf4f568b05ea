package nl.dpes.core.config.components

import nl.dpes.core.services.SavedSearch

trait SavedSearchComponent {
  this: AxonComponent
    with CoreProjectionsComponent
    with IndexProjectionsComponent
    with IdentifierComponent
    with MailServiceComponent
    with JobSeekerServiceComponent
    with SalesforceGatewayComponent
    with LoggingComponent =>

  lazy val savedSearchService = new SavedSearch(
    zoekopdrachtProjections,
    indexOpgeslagenZoekopdrachtenProjections,
    recruiterAccountProjections,
    identifierService,
    commandGateway,
    mailService,
    jobSeekerService,
    b2bRecruiterService
  )
}
