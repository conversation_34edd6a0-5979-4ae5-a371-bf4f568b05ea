package nl.dpes.core.config.components

import java.io.FileInputStream
import java.util.Properties

import nl.dpes.core.config.{ApplicationHooks, Configuration}
import nl.dpes.core.services.StatusService
import nl.dpes.core.services.StatusService.License
import org.joda.time.DateTime

trait StatusComponent {
  this: ApplicationHooks with LoggingComponent with MySQLComponent with Configuration =>

  addBeforeStartHook {
    statusService.onApplicationStarting()
  }

  addStartHook {
    statusService.onApplicationStarting()
  }

  addAfterStartHook {
    statusService.onApplicationStarted()
  }

  addShutdownHook {
    statusService.onApplicationStopping()
  }

  addAfterShutdownHook {
    statusService.onApplicationStopped()
  }

  private lazy val gdprLicense: License = {
    val location = System.getProperty("axoniq.gdpr.license", "axoniq.license")
    val fis      = new FileInputStream(location)
    val props    = new Properties()
    props.load(fis)
    val license = License(props.getProperty("product"), DateTime.parse(props.getProperty("expiry_date")))
    fis.close()
    license
  }

  private lazy val licenses: Set[License] = Set(
    gdprLicense
  )

  lazy val statusService = new StatusService(licenses)
}
