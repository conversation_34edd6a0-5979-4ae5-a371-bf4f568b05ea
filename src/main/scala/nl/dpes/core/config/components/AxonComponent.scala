package nl.dpes.core.config.components

import com.thoughtworks.xstream.XStream
import com.typesafe.config.ConfigFactory
import io.axoniq.gdpr.api.{FieldEncrypter, FieldEncryptingSerializer}
import io.axoniq.gdpr.cryptoengine.{CryptoEngine, JdbcCryptoEngine, KeyType}
import nl.dpes.axon4s.serialization.xml.EnumConverter
import nl.dpes.common.conversions._
import nl.dpes.core.config.components.json.SpraySerializer
import nl.dpes.core.config.{ApplicationHooks, Configuration}
import nl.dpes.core.projections.core.{CoreRecruiterAccountProjections, CoreWerkzoekendeAccountProjections}
import nl.dpes.core.services.NotifyingMessageMonitor.Actions
import nl.dpes.core.services._
import nl.dpes.utils.date.SavedSearchClock
import nl.dpes.utils.events.DomainAwareReplacementValueProvider
import nl.dpes.utils.events.upcasting.converter.ListConverter
import nl.dpes.utils.events.upcasting.werkzoekende.{UrlUpcaster, WerkzoekendeEMailadresUpcaster}
import nl.dpes.utils.events.upcasting.zoekopdracht.SerializationHelper.ListSerializeEnd
import nl.dpes.utils.events.upcasting.zoekopdracht.{AangemaaktDoorRecruiterUpcaster, OpgeslagenDoorRecruiterUpcaster, SerializationHelper}
import nl.dpes.utils.scheduling.MySQLSchedulingSchema
import org.axonframework.commandhandling.gateway.CommandGateway
import org.axonframework.common.transaction.NoTransactionManager
import org.axonframework.config.{ConfigurationScopeAwareProvider, Configurer, DefaultConfigurer, Configuration => AxonConfig}
import org.axonframework.deadline.DeadlineManager
import org.axonframework.deadline.quartz.QuartzDeadlineManager
import org.axonframework.eventhandling.scheduling.EventScheduler
import org.axonframework.eventhandling.scheduling.quartz.QuartzEventScheduler
import org.axonframework.eventhandling.tokenstore.jdbc.{GenericTokenTableFactory, JdbcTokenStore}
import org.axonframework.eventhandling.tokenstore.{TokenStore, UnableToClaimTokenException}
import org.axonframework.eventhandling.{
  EventHandlerInvoker,
  PropagatingErrorHandler,
  TrackingEventProcessor,
  TrackingEventProcessorConfiguration
}
import org.axonframework.eventsourcing.eventstore.EventStorageEngine
import org.axonframework.eventsourcing.eventstore.jdbc.{
  EventSchema,
  JdbcEventStorageEngine,
  JdbcSQLErrorCodesResolver,
  MySqlEventTableFactory
}
import org.axonframework.serialization.upcasting.event.EventUpcasterChain
import org.axonframework.serialization.xml.{CompactDriver, XStreamSerializer}
import org.quartz.Scheduler
import org.quartz.impl.StdSchedulerFactory
import org.slf4j.Logger

import java.time.{Clock, Duration}
import scala.util.Random

trait AxonComponent {
  this: Configuration
    with LoggingComponent
    with ApplicationHooks
    with IdentifierComponent
    with CoreProjectionsComponent
    with IndexProjectionsComponent
    with MySQLComponent
    with ProfileServiceComponent
    with SystemComponent
    with NotifierComponent
    with EventPublisherComponent
    with SecurityComponent =>

  scala.util.Properties.envOrNone("AXONIQ_GDPR_LICENSE_FILE").map { licenseLocation =>
    scala.util.Properties.setProp("axoniq.gdpr.license", licenseLocation)
  }

  protected lazy val xStream: XStream = {
    val xStream = new XStream(new CompactDriver)
    xStream.alias("scala.collection.immutable.List$SerializationProxy", classOf[SerializationHelper[_]])
    xStream.alias("scala.collection.immutable.ListSerializeEnd$", ListSerializeEnd.getClass)
    xStream.allowTypesByWildcard(Array("nl.dpes.**", "scala.**", "cats.**", "ch.qos.logback.**"))
    xStream.registerConverter(new EnumConverter)
    xStream.registerConverter(new ListConverter(xStream.getMapper))
    xStream.ignoreUnknownElements()
    xStream
  }
  protected lazy val xmlSerializer: XStreamSerializer = XStreamSerializer.builder().xStream(xStream).build()

  protected def messageMonitor = new NotifyingMessageMonitor(
    eventNotifier,
    Application.nodeName,
    Map(classOf[UnableToClaimTokenException] -> Actions.Ignore)
  )

  protected lazy val cryptoEngine: CryptoEngine = {
    val cryptoEngine = new JdbcCryptoEngine(dataSource, "GDPRModuleKeys")
    cryptoEngine.setKeyType(KeyType.AES_256)

    if (!connectionProvider.getConnection.getMetaData.getTables(null, null, "GDPRModuleKeys", null).next()) {
      connectionProvider.getConnection.prepareStatement(cryptoEngine.getCreateTableStatement).execute()
    }

    cryptoEngine
  }

  protected lazy val scheduler: Scheduler = new StdSchedulerFactory(ConfigFactory.load().getConfig("scheduling")).getScheduler

  private lazy val fieldEncrypter =
    new FieldEncrypter(cryptoEngine, xmlSerializer, new DomainAwareReplacementValueProvider(identifierService))

  protected lazy val serializer = new FieldEncryptingSerializer(fieldEncrypter, xmlSerializer)

  def createTrackingEventProcessor(
    name: String,
    configuration: AxonConfig,
    eventHandlerInvoker: EventHandlerInvoker
  ): TrackingEventProcessor = TrackingEventProcessor
    .builder()
    .name(name)
    .eventHandlerInvoker(eventHandlerInvoker)
//    .messageMonitor(messageMonitor)
    .messageSource(configuration.eventStore())
    .errorHandler(PropagatingErrorHandler.INSTANCE)
    .tokenStore(configuration.getComponent(classOf[TokenStore]))
    .trackingEventProcessorConfiguration(TrackingEventProcessorConfiguration.forSingleThreadedProcessing())
    .transactionManager(NoTransactionManager.INSTANCE)
    .storingTokensAfterProcessing()
    .build()

  def axonConfigurer: Configurer = {
    new MySQLSchedulingSchema()
      .createSchema(connectionProvider.getConnection)

    lazy val eventStore: EventStorageEngine = {
      val tableFactory = new MySqlEventTableFactory()
      tableFactory
        .createDomainEventTable(connectionProvider.getConnection, new EventSchema())
        .execute()
      tableFactory
        .createSnapshotEventTable(connectionProvider.getConnection, new EventSchema())
        .execute()

      JdbcEventStorageEngine
        .builder()
        .eventSerializer(serializer)
        .snapshotSerializer(serializer)
        .upcasterChain(
          new EventUpcasterChain(
            new AangemaaktDoorRecruiterUpcaster(serializer),
            new OpgeslagenDoorRecruiterUpcaster(serializer),
            new WerkzoekendeEMailadresUpcaster,
            new UrlUpcaster
          )
        )
        .connectionProvider(connectionProvider)
        .transactionManager(NoTransactionManager.INSTANCE)
        .persistenceExceptionResolver(new JdbcSQLErrorCodesResolver)
        .build()
    }

    val tokenStore = JdbcTokenStore
      .builder()
      .claimTimeout(Duration.ofMinutes(1))
      .connectionProvider(connectionProvider)
      .serializer(xmlSerializer)
      .build()

    tokenStore.createSchema(GenericTokenTableFactory.INSTANCE)

    val configurer = DefaultConfigurer
      // Base framework
      .defaultConfiguration()
      .configureMessageSerializer(_ => xmlSerializer)
      .configureSerializer(_ => xmlSerializer)
      .configureEmbeddedEventStore(_ => eventStore)
      // Injectables
      .registerComponent(classOf[TokenStore], _ => tokenStore)
      .registerComponent(classOf[Logger], _ => logger)
      .registerComponent(classOf[Clock], _ => Clock.systemUTC())
      .registerComponent(classOf[SavedSearchClock], _ => new SavedSearchClock(Clock.systemUTC(), new Random()))
      .registerComponent(classOf[CoreWerkzoekendeAccountProjections], _ => werkzoekendeAccountProjections)
      .registerComponent(classOf[CoreRecruiterAccountProjections], _ => recruiterAccountProjections)
      .registerComponent(classOf[PasswordService], _ => passwordService)
      .registerComponent(
        classOf[DeadlineManager],
        config => {
          new MySQLSchedulingSchema().createSchema(connectionProvider.getConnection)

          scheduler.start()

          QuartzDeadlineManager
            .builder()
            .scheduler(scheduler)
            .serializer(xmlSerializer)
            .scopeAwareProvider(new ConfigurationScopeAwareProvider(config))
            .build()
        }
      )
      .registerComponent(
        classOf[EventScheduler],
        config =>
          QuartzEventScheduler
            .builder()
            .eventBus(config.eventBus())
            .scheduler(scheduler)
            .build()
      )

    configurer.eventProcessing { eventProcessingConfigurer =>
      eventProcessingConfigurer.assignHandlerInstancesMatching(
        ProjectionDetails.Core.tokenName,
        (t: Any) =>
          t == werkzoekendeAccountProjections || t == recruiterAccountProjections || t == zoekopdrachtProjections || t == sanDiegoEmployerProjections
      )
      eventProcessingConfigurer.registerEventHandler(_ => werkzoekendeAccountProjections)
      eventProcessingConfigurer.registerEventHandler(_ => recruiterAccountProjections)
      eventProcessingConfigurer.registerEventHandler(_ => zoekopdrachtProjections)
      eventProcessingConfigurer.registerEventHandler(_ => sanDiegoEmployerProjections)

      eventProcessingConfigurer.registerEventProcessor(ProjectionDetails.Core.tokenName, createTrackingEventProcessor)
    }

    configurer.eventProcessing { eventProcessingConfigurer =>
      eventProcessingConfigurer.assignHandlerInstancesMatching(
        ProjectionDetails.Index.tokenName,
        (t: Any) => t == indexOpgeslagenZoekopdrachtenProjections
      )
      eventProcessingConfigurer.registerEventHandler(_ => indexOpgeslagenZoekopdrachtenProjections)

      eventProcessingConfigurer.registerEventProcessor(ProjectionDetails.Index.tokenName, createTrackingEventProcessor)
    }

    configurer.eventProcessing { eventProcessingConfigurer =>
      eventProcessingConfigurer.assignHandlerInstancesMatching(
        ProjectionDetails.ElasticSearch.tokenName,
        (t: Any) => t == indexRecruiterFavorietProjections
      )
      eventProcessingConfigurer.registerEventHandler(_ => indexRecruiterFavorietProjections)

      eventProcessingConfigurer.registerEventProcessor(ProjectionDetails.ElasticSearch.tokenName, createTrackingEventProcessor)
    }

    configurer.eventProcessing { eventProcessingConfigurer =>
      eventProcessingConfigurer.assignHandlerInstancesMatching(
        ProfileServiceConfig.FavoriteProfilesProjection.token,
        (t: Any) => t == favoriteProfilesEventHandler
      )
      eventProcessingConfigurer.registerEventHandler(_ => favoriteProfilesEventHandler)

      eventProcessingConfigurer.registerEventProcessor(ProfileServiceConfig.FavoriteProfilesProjection.token, createTrackingEventProcessor)
    }

    configurer.eventProcessing { eventProcessingConfigurer =>
      eventProcessingConfigurer.assignHandlerInstancesMatching(
        ProfileServiceConfig.SavedSearchProjection.token,
        (t: Any) => t == savedSearchEventHandler
      )
      eventProcessingConfigurer.registerEventHandler(_ => savedSearchEventHandler)

      eventProcessingConfigurer.registerEventProcessor(ProfileServiceConfig.SavedSearchProjection.token, createTrackingEventProcessor)
    }

    eventPublisherService.foreach(eventPublisher =>
      configurer.eventProcessing { eventProcessingConfigurer =>
        val processingGroup = "nl.dpes.core.services.eventpublisher"

        eventProcessingConfigurer.assignHandlerInstancesMatching(processingGroup, (t: Any) => t == eventPublisher)
        eventProcessingConfigurer.registerEventHandler(_ => eventPublisher)

        eventProcessingConfigurer.registerEventProcessor(processingGroup, createTrackingEventProcessor)
      }
    )

    configurer
  }

  lazy val axonConfig: AxonConfig = axonConfigurer.buildConfiguration()

  def axonStart(): Unit =
    axonConfig.start()

  def axonShutdown(): Unit = {
    scheduler.shutdown( /* waitForJobsToComplete = */ true)
    axonConfig.shutdown()
  }

  lazy val commandGateway: CommandGateway = axonConfig.commandGateway()
}
