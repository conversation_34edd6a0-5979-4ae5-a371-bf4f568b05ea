package nl.dpes.core.config.components

import com.amazonaws.client.builder.AwsClientBuilder.EndpointConfiguration
import com.amazonaws.services.kinesis.{AmazonKinesis, AmazonKinesisClient}
import nl.dpes.core.config.Configuration
import nl.dpes.core.services.eventpublisher.{EventPublisherService, KinesisEventPublisherService}

trait EventPublisherComponent {
  val eventPublisherService: Option[EventPublisherService]
}

trait KinesisEventPublisherComponent extends EventPublisherComponent {
  this: LoggingComponent with AxonComponent with IdentifierComponent with Configuration =>

  lazy val kinesisClient: AmazonKinesis = {
    val client = AmazonKinesisClient.builder()

    if (Kinesis.url.nonEmpty) {
      client.withEndpointConfiguration(new EndpointConfiguration(Kinesis.url, Kinesis.region))
    }

    client.build()
  }

  lazy val eventPublisherService: Option[KinesisEventPublisherService] =
    KinesisEventPublisherService(kinesisClient, EventPublisher.DomainEvents.streamName)
}
