package nl.dpes.core.config.components

import nl.dpes.core.services.RecruiterIdToSalesforceIdMigrator

trait RecruiterIdToSalesforceIdMigratorComponent {
  this: LoggingComponent with CoreProjectionsComponent with IndexProjectionsComponent with SystemComponent with AxonComponent =>

  lazy val salesforceIdMigrator = new RecruiterIdToSalesforceIdMigrator(
    recruiterAccountProjections,
    indexRecruiterFavorietProjections,
    indexOpgeslagenZoekopdrachtenProjections,
    commandGateway
  )
}
