package nl.dpes.core.config

import akka.actor.{ActorSystem => AkkaActorSystem}
import org.apache.pekko.actor.ActorSystem
import kamon.Kamon
import nl.dpes.core.config.components._
import nl.dpes.core.repositories.TokenRepository
import nl.dpes.utils.events.EventStoreMaintenance
import nl.dpes.utils.events.EventStoreMaintenance.Task
import sttp.client3.{HttpClientFutureBackend, SttpBackend}

import scala.concurrent.{ExecutionContext, Future}

trait WebServerModule
    extends Configuration
    with ApplicationHooks
    with LoggingComponent
    with SystemComponent
    with ApplicationAxonComponent
    with ProfileServiceComponent
    with StatusComponent
    with JobSeekerServiceComponent
    with DynamoDBComponent
    with MySQLComponent
    with CoreProjectionsComponent
    with IndexProjectionsComponent
    with ElasticSearchComponent
    with MailServiceComponent
    with IdentifierComponent
    with SavedSearchComponent
    with SecurityComponent
    with TokenRepositoryComponent
    with NotifierComponent
    with HttpComponent
    with SimComponent
    with KinesisEventPublisherComponent
    with RecruiterIdToSalesforceIdMigratorComponent
    with SalesforceGatewayComponent {
  Kamon.init()

  override implicit val system: ActorSystem = ActorSystem("core-api")
  // TODO: remove akkaSystem when b2b salesforce stops using akka in grpc
  override implicit val akkaSystem: AkkaActorSystem = AkkaActorSystem("core-api")
  override implicit def executor: ExecutionContext  = system.dispatcher

  override implicit def sttpBackend: SttpBackend[Future, _] = HttpClientFutureBackend()

  private lazy val eventStoreMaintenance = new EventStoreMaintenance(
    connectionProvider,
    tokenRepository,
    TokenRepository.EventStoreMaintenanceToken
  )
  private lazy val maintenanceTasks: Seq[Task[_]] = Seq.empty[Task[_]]

  eventStoreMaintenance.execute(maintenanceTasks)

  addStartHook {
    logger.info("Starting Axon...")
    axonStart()
    logger.info("Axon started")
  }

  addShutdownHook {
    logger.info("Shutting down Axon...")
    axonShutdown()
    logger.info("Axon shutdown")
  }
}
