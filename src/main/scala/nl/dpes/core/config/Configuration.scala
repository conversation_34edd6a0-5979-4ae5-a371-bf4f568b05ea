package nl.dpes.core.config

import com.typesafe.config.ConfigFactory
import scala.jdk.CollectionConverters._

trait Configuration {
  private val config          = ConfigFactory.load()
  private final val Namespace = "nl.dpes.core"

  implicit lazy val ApplicationEnvironment: Environment = Application.environment

  object Application {
    lazy val environment: Environment = Environment(config.getString(s"$Namespace.env"))
    lazy val nodeName: String         = config.getString("node.name")
  }

  object Api {
    lazy val basePath: String = config.getString(s"$Namespace.http.api.basePath")
    lazy val host: String     = config.getString(s"$Namespace.http.api.host")
    lazy val port: Int        = config.getInt(s"$Namespace.http.api.port")
  }

  object Server {
    lazy val host: String = config.getString(s"$Namespace.http.bind.host")
    lazy val port: Int    = config.getInt(s"$Namespace.http.bind.port")
  }

  object MySQL {
    lazy val user: String             = config.getString(s"$Namespace.db.user")
    lazy val password: String         = config.getString(s"$Namespace.db.password")
    lazy val connectionString: String = config.getString(s"$Namespace.db.connectionString")
    lazy val connectionTimeout: Long  = config.getLong(s"$Namespace.db.connectionTimeout")

    object ConnectionPool {
      lazy val initialConnections: Int = config.getInt(s"$Namespace.db.connectionPool.initialConnections")
      lazy val maxConnections: Int     = config.getInt(s"$Namespace.db.connectionPool.maxConnections")
    }
  }

  object DynamoDB {
    lazy val url: String    = config.getString(s"$Namespace.dynamodb.url")
    lazy val region: String = config.getString(s"$Namespace.dynamodb.region")
  }

  object ElasticSearch {
    lazy val url: String = config.getString(s"$Namespace.elasticsearch.url")
    lazy val port: Int   = config.getInt(s"$Namespace.elasticsearch.port")
  }

  object Kinesis {
    lazy val url: String    = config.getString(s"$Namespace.kinesis.url")
    lazy val region: String = config.getString(s"$Namespace.kinesis.region")
  }

  object JobSeekerService {
    lazy val url: String    = config.getString(s"$Namespace.ndsm.service.jobseeker.baseUrl")
    lazy val apiKey: String = config.getString(s"$Namespace.ndsm.service.jobseeker.apiKey")
  }

  object MailService {

    lazy val url: String = "http://" + config.getString(s"$Namespace.ndsm.service.mail.host") + ":" +
      config.getInt(s"$Namespace.ndsm.service.mail.port")
  }

  object SalesforceGateway {
    lazy val host: String  = config.getString(s"$Namespace.salesforce_gateway.grpc.host")
    lazy val grpcPort: Int = config.getInt(s"$Namespace.salesforce_gateway.grpc.port")
  }

  object SIM {
    lazy val host: String     = config.getString(s"$Namespace.sim.host")
    lazy val port: Int        = config.getInt(s"$Namespace.sim.port")
    lazy val basePath: String = config.getString(s"$Namespace.sim.path")
  }

  object Security {
    lazy val minimumPasswordLength: Int = config.getInt(s"$Namespace.security.minimumPasswordLength")

    object JWT {
      lazy val issuer: String = config.getString(s"$Namespace.security.jwt.issuer")
    }

    object Rsa {
      lazy val id: String         = config.getString(s"$Namespace.security.rsa.id")
      lazy val privateKey: String = config.getString(s"$Namespace.security.rsa.key.private")
      lazy val publicKey: String  = config.getString(s"$Namespace.security.rsa.key.public")
    }

    object ApiKey {
      lazy val keys: List[String] = config.getStringList(s"$Namespace.security.apikey.keys").asScala.toList
    }
  }

  object PHP {
    lazy val executable: String = config.getString(s"$Namespace.php.executable")
  }

  object Notifier {

    object Events {
      lazy val channel: String  = config.getString(s"$Namespace.notifier.events.channel")
      lazy val username: String = config.getString(s"$Namespace.notifier.events.username")

      lazy val icon: Option[String] = config.getString(s"$Namespace.notifier.events.icon") match {
        case "" => None
        case s  => Some(s)
      }
      lazy val level: String      = config.getString(s"$Namespace.notifier.events.level")
      lazy val webhookUrl: String = config.getString(s"$Namespace.notifier.events.webhookUrl")
    }

    object Rebuild {
      lazy val channel: String  = config.getString(s"$Namespace.notifier.rebuild.channel")
      lazy val username: String = config.getString(s"$Namespace.notifier.rebuild.username")

      lazy val icon: Option[String] = config.getString(s"$Namespace.notifier.rebuild.icon") match {
        case "" => None
        case s  => Some(s)
      }
      lazy val level: String      = config.getString(s"$Namespace.notifier.rebuild.level")
      lazy val webhookUrl: String = config.getString(s"$Namespace.notifier.rebuild.webhookUrl")
    }
  }

  object EventPublisher {

    object DomainEvents {
      lazy val streamName: String = config.getString(s"$Namespace.eventPublisher.domain.streamName")
    }
  }

  object EventBus {

    object Kinesis {
      lazy val url: String    = config.getString(s"$Namespace.kinesis.url")
      lazy val region: String = config.getString(s"$Namespace.kinesis.region")
    }
    lazy val domainEventStream: String = config.getString(s"$Namespace.event_bus.domain_event_stream")
  }

  object ProjectionDetails {

    object Core {
      lazy val tokenName: String = config.getString(s"$Namespace.projections.core.tokenName")
      lazy val version: Int      = config.getInt(s"$Namespace.projections.core.version")
    }

    object Index {
      lazy val tokenName: String = config.getString(s"$Namespace.projections.index.tokenName")
      lazy val version: Int      = config.getInt(s"$Namespace.projections.index.version")
    }

    object ElasticSearch {
      lazy val tokenName: String = config.getString(s"$Namespace.projections.elasticsearch.tokenName")
      lazy val version: Int      = config.getInt(s"$Namespace.projections.elasticsearch.version")
    }
  }

  object Axon {
    lazy val distributedCommandGateway: Boolean = config.getBoolean(s"$Namespace.axon.distributedCommandGateway")
  }

  object ProfileServiceConfig {
    lazy val dbConnectionString: String   = config.getString(s"$Namespace.profileService.databaseUrl")
    lazy val dbUsername: String           = config.getString(s"$Namespace.profileService.databaseUsername")
    lazy val dbPassword: String           = config.getString(s"$Namespace.profileService.databasePassword")
    lazy val favoritesTableName: String   = config.getString(s"$Namespace.profileService.favoritesTableName")
    lazy val savedSearchTableName: String = config.getString(s"$Namespace.profileService.savedSearchTableName")
    lazy val threadCount: Int             = config.getInt(s"$Namespace.profileService.threadCount")

    object FavoriteProfilesProjection {
      lazy val tokenName: String = config.getString(s"$Namespace.profileService.favoriteProfilesProjection.tokenName")
      lazy val version: String   = config.getString(s"$Namespace.profileService.favoriteProfilesProjection.version")

      def token: String = tokenName + version
    }

    object SavedSearchProjection {
      lazy val tokenName: String = config.getString(s"$Namespace.profileService.savedSearchProjection.tokenName")
      lazy val version: String   = config.getString(s"$Namespace.profileService.savedSearchProjection.version")

      def token: String = tokenName + version
    }
  }
}
