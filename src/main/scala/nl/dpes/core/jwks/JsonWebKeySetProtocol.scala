package nl.dpes.core.jwks

import org.apache.pekko.http.scaladsl.marshallers.sprayjson.SprayJsonSupport
import nl.dpes.core.jwks.JsonWebKeySet.{PublicKey, PublicKeys}
import spray.json.{DefaultJsonProtocol, RootJsonFormat}

trait JsonWebKeySetProtocol extends SprayJsonSupport with DefaultJsonProtocol {
  implicit val publicKeyFormat: RootJsonFormat[PublicKey]   = jsonFormat4(PublicKey)
  implicit val publicKeysFormat: RootJsonFormat[PublicKeys] = jsonFormat1(PublicKeys)
}
