package nl.dpes.core.jwks

import org.apache.pekko.http.scaladsl.model.StatusCodes
import org.apache.pekko.http.scaladsl.server.{Directives, Route}
import io.circe.Codec
import io.circe.generic.semiauto.deriveCodec
import nl.dpes.core.config.Configuration
import nl.dpes.core.config.components.{LoggingComponent, SecurityComponent}
import org.apache.commons.codec.binary.Base64

object JsonWebKeySet {
  final case class PublicKey(e: String, kid: String, kty: String, n: String)
  final case class PublicKeys(keys: List[PublicKey])

  implicit val codecPublicKey: Codec[PublicKey]   = deriveCodec[PublicKey]
  implicit val codecPublicKeys: Codec[PublicKeys] = deriveCodec[PublicKeys]
}

trait JsonWebKeySet extends Directives with JsonWebKeySetProtocol with Configuration with SecurityComponent with LoggingComponent {

  import JsonWebKeySet._

  val jsonWebKeySetRoute: Route = pathPrefix(".well-known") {
    path("jwks.json") {
      get {
        maybeRsaKeys match {
          case Right(keys) =>
            complete(
              StatusCodes.OK,
              PublicKeys(
                List(
                  PublicKey(
                    Base64.encodeBase64URLSafeString(keys.publicKey.getPublicExponent.toByteArray),
                    Security.Rsa.id,
                    keys.publicKey.getAlgorithm,
                    Base64.encodeBase64URLSafeString(keys.publicKey.getModulus.toByteArray)
                  )
                )
              )
            )
          case Left(throwable) =>
            logger.error(s"JSON Web Key Set is not available: $throwable.")
            complete(StatusCodes.InternalServerError)
        }
      }
    }
  }
}
