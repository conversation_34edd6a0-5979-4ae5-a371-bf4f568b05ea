package nl.dpes.core.jwks

import cats.effect.IO
import nl.dpes.core.api.v1.ApiError
import nl.dpes.core.jwks.JsonWebKeySet.{PublicKey, PublicKeys}
import org.apache.commons.codec.binary.Base64

class J<PERSON><PERSON>ebKeySetLogic extends JsonWebKeySet {

  def getPublicKeys: IO[Either[ApiError, PublicKeys]] =
    IO {
      maybeRsaKeys match {
        case Right(keys) =>
          Right(
            PublicKeys(
              List(
                PublicKey(
                  Base64.encodeBase64URLSafeString(keys.publicKey.getPublicExponent.toByteArray),
                  Security.Rsa.id,
                  keys.publicKey.getAlgorithm,
                  Base64.encodeBase64URLSafeString(keys.publicKey.getModulus.toByteArray)
                )
              )
            )
          )
        case Left(throwable) =>
          val message = s"JSON Web Key Set is not available: $throwable."
          logger.error(message)
          Left(ApiError.Unknown(message))
      }
    }
}
