package nl.dpes.core.api

import nl.dpes.core.api.v1.customerservice.CustomerServiceLogic
import nl.dpes.core.api.v1.jobseekers.JobSeekerLogic
import nl.dpes.core.api.v1.recruiters.RecruitersLogic
import nl.dpes.core.api.v1.searches.SavedSearchLogic
import nl.dpes.core.api.v1.status.StatusLogic
import nl.dpes.core.jwks.JsonWebKeySetLogic

case class Logic(
  statusLogic: StatusLogic,
  savedSearchLogic: SavedSearchLogic,
  recruitersLogic: <PERSON>cruit<PERSON>Log<PERSON>,
  customerServiceLogic: CustomerServiceLogic,
  jobSeekerLogic: Job<PERSON>eekerLog<PERSON>,
  jsonWebKeySetLogic: JsonWebKeySetLogic
)
