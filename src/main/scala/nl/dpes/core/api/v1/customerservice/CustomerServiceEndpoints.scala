package nl.dpes.core.api.v1.customerservice

import io.circe.{Codec => CCodec}
import nl.dpes.core.api.v1.ApiError
import nl.dpes.core.api.v1.customerservice.CustomerServiceEndpoints._
import sttp.model.StatusCode
import sttp.tapir._
import sttp.tapir.generic.auto._
import sttp.tapir.json.circe._

class CustomerServiceEndpoints(prefix: String, version: String) {

  private val tag = "Customer Service"

  val createToken: PublicEndpoint[ServiceWorker, Unit, Token, Any] =
    endpoint
      .in(prefix / version / "medewerkers" / "token")
      .in(jsonBody[ServiceWorker].description("Customer Service Worker Id"))
      .out(jsonBody[Token].description("Token Created"))
      .out(statusCode(StatusCode.Created))
      .tag(tag)
      .summary("Create an admin token")

  val getCurrentWorker: Endpoint[String, Unit, ApiError.UnprocessableEntity, ServiceWorker, Any] =
    endpoint.get
      .in(prefix / version / "medewerkers" / "me")
      .securityIn(auth.bearer[String]())
      .out(jsonBody[ServiceWorker].description("Customer Service Worker Id"))
      .errorOut(statusCode(StatusCode.UnprocessableEntity).and(jsonBody[ApiError.UnprocessableEntity]))
      .description("Wrong token")
      .tag(tag)
      .summary("Return the currently logged in customer service worker")

  val createLoginAsToken: Endpoint[String, LoginAs, ApiError.UnprocessableEntity, Token, Any] =
    endpoint.post
      .in(prefix / version / "medewerkers" / "login-als-token")
      .securityIn(auth.bearer[String]())
      .in(jsonBody[LoginAs].description("Login Als"))
      .out(jsonBody[Token].description("Token"))
      .out(statusCode(StatusCode.Ok))
      .errorOut(statusCode(StatusCode.UnprocessableEntity).and(jsonBody[ApiError.UnprocessableEntity]))
      .description("Wrong token")
      .tag(tag)
      .summary("Get a login as token")

}

object CustomerServiceEndpoints {

  final case class Token(token: String)
  final case class ServiceWorker(userId: String)
  final case class LoginAs(userId: String, role: String)

  implicit val tokenCodec: CCodec.AsObject[Token]                 = io.circe.generic.semiauto.deriveCodec[Token]
  implicit val loginAsCodec: CCodec.AsObject[LoginAs]             = io.circe.generic.semiauto.deriveCodec[LoginAs]
  implicit val serviceWorkerCodec: CCodec.AsObject[ServiceWorker] = io.circe.generic.semiauto.deriveCodec[ServiceWorker]

}
