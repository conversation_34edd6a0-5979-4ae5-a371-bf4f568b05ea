package nl.dpes.core.api.v1.searches

import nl.dpes.core.api.v1.ApiError
import nl.dpes.core.domain.{Fre<PERSON><PERSON>, Zoekparameters, Zoektermen}
import nl.dpes.core.domain.exceptions.AccountNietGevonden
import nl.dpes.core.projections.index.IndexOpgeslagenZoekopdrachtProjections
import nl.dpes.core.projections.index.IndexOpgeslagenZoekopdrachtProjections.IndexOpgeslagenZoekopdracht
import nl.dpes.core.services.{SavedSearch => SavedSearchService}
import org.axonframework.modelling.command.AggregateNotFoundException
import org.slf4j.Logger

import scala.concurrent.{ExecutionContext, Future}

class SavedSearchLogic(
  projections: IndexOpgeslagenZoekopdrachtProjections,
  savedSearchService: SavedSearchService,
  executor: ExecutionContext,
  logger: Logger,
  prefix: String,
  version: String
) {
  private implicit val ec = executor

  import SavedSearchEndpoints._

  def getSearch(input: SearchIdInput): Future[Either[ApiError.NotFound, SavedSearch]] = {
    val result = projections.findBySavedSearchId(input.recruiterId, input.searchId) match {
      case Some(v) =>
        Right(indexOpgeslagenZoekopdrachtToSavedSearch(v))
      case None =>
        Left(ApiError.NotFound(s"Search not found for recruiter with id ${input.recruiterId}", "Saved Search", Some(input.searchId)))
    }
    Future.successful(result)
  }

  def editSearch(input: (SearchIdInput, Search)): Future[Either[ApiError, Unit]] =
    savedSearchService
      .editByRecruiter(input._1.recruiterId, input._1.searchId, input._2.naam, convertFromFrequency(input._2.frequentie))
      .map(_ => Right(()))
      .recover {
        case e: AccountNietGevonden =>
          Left(ApiError.NotFound("", e.accountType, Some(e.accountId)))
        case e =>
          logger.error(e.getMessage, e)
          Left(ApiError.Unknown(e.getMessage))
      }

  def deleteSearch(input: SearchIdInput): Future[Either[ApiError.Unknown, Unit]] =
    savedSearchService
      .deleteByRecruiter(input.searchId, input.recruiterId)
      .map(_ => Right(()))
      .recover {
        case e: AggregateNotFoundException =>
          logger.warn(e.getMessage, e)
          Right(())
        case e =>
          logger.error(e.getMessage, e)
          Left(ApiError.Unknown(e.getMessage))
      }

  def listSearches(recruiterId: String): Future[Either[Unit, SavedSearches]] = {
    val searches = projections.findByRecruiterId(recruiterId).map(indexOpgeslagenZoekopdrachtToSavedSearch)
    Future.successful(Right(SavedSearches(searches)))
  }

  def saveSearch(input: (String, Search)): Future[Either[ApiError, (SavedSearchId, String)]] = {
    val search      = input._2
    val recruiterId = input._1

    savedSearchService
      .saveByRecruiter(
        recruiterId,
        convertSearchParamsToZoekparameters(search.zoekparameters),
        search.naam,
        convertFromFrequency(search.frequentie)
      )
      .map { id =>
        val idObject = SavedSearchId(id)
        val location = s"/$prefix/$version/recruiters/$recruiterId/zoekopdrachten/$id"
        Right((idObject, location))
      }
      .recover {
        case e: AccountNietGevonden =>
          Left(ApiError.NotFound("", e.accountType, Some(e.accountId)))
        case e =>
          logger.error(e.getMessage, e)
          Left(ApiError.Unknown(e.getMessage))
      }
  }

  private def indexOpgeslagenZoekopdrachtToSavedSearch(input: IndexOpgeslagenZoekopdracht): SavedSearch =
    SavedSearch(
      input.opgeslagenZoekopdrachtId,
      input.accountId,
      input.zoekopdrachtId,
      input.naam,
      convertFrequency(input.frequentie),
      convertParams(input.zoekparameters)
    )

  private def convertFrequency(input: Frequenties.Frequentie): Frequency = input match {
    case Frequenties.Wekelijks => Wekelijks
    case Frequenties.Dagelijks => Dagelijks
    case Frequenties.Nooit     => Nooit
  }

  private def convertFromFrequency(input: Frequency): Frequenties.Frequentie = input match {
    case Wekelijks => Frequenties.Wekelijks
    case Dagelijks => Frequenties.Dagelijks
    case Nooit     => Frequenties.Nooit
  }

  private def convertParams(input: Zoekparameters): SearchParams =
    SearchParams(
      input.zoektermen.map(zoektermenToSearchConditions),
      input.locatie,
      input.wijzigingsdatum,
      input.opleidingsniveaus,
      input.aantallenUren,
      input.soortenWerk,
      input.beschikbaarheden,
      input.rijbewijzen,
      input.talen,
      input.afstandTotWerklocatie,
      input.carriereniveau,
      input.functiegroep,
      input.gewenstSalaris,
      input.provincies
    )

  private def convertSearchParamsToZoekparameters(input: SearchParams): Zoekparameters =
    Zoekparameters(
      input.zoektermen.map(searchConditionsToZoktermen),
      input.locatie,
      input.wijzigingsdatum,
      input.opleidingsniveaus,
      input.aantallenUren,
      input.soortenWerk,
      input.beschikbaarheden,
      input.rijbewijzen,
      input.talen,
      input.afstandTotWerklocatie,
      input.carriereniveau,
      input.functiegroep,
      input.gewenstSalaris,
      input.provincies
    )

  private def zoektermenToSearchConditions(zoektermen: Zoektermen): SearchConditions = SearchConditions(
    zoektermen.alles,
    zoektermen.opleidingNaam,
    zoektermen.opleidingBeschrijving,
    zoektermen.gewensteBaan,
    zoektermen.functieTitel,
    zoektermen.functieBeschrijving,
    zoektermen.cursussen
  )

  private def searchConditionsToZoktermen(conditions: SearchConditions): Zoektermen = Zoektermen(
    conditions.alles,
    conditions.opleidingNaam,
    conditions.opleidingBeschrijving,
    conditions.gewensteBaan,
    conditions.functieTitel,
    conditions.functieBeschrijving,
    conditions.cursussen
  )

}
