package nl.dpes.core.api.v1.customerservice

import cats.syntax.either._
import nl.dpes.core.api.v1.ApiError
import nl.dpes.core.api.v1.customerservice.CustomerServiceEndpoints._
import nl.dpes.core.domain.Rollen
import nl.dpes.core.services.TokenService
import nl.dpes.core.services.security.tokens.{AccessToken, LoginAsToken, TokenTypes}
import org.slf4j.Logger

import scala.concurrent.{ExecutionContext, Future}

class CustomerServiceLogic(tokenService: TokenService, logger: Logger, executor: ExecutionContext) {

  implicit val ec = executor

  def createToken(serviceWorker: ServiceWorker): Future[Either[Unit, Token]] = {
    val accessToken = AccessToken(serviceWorker.userId, None, Rollen.Medewerker)
    Future {
      val token = tokenService.generate(accessToken)
      Right(Token(token))
    }
  }

  def getCurrentWorker(token: String)(u: Unit): Future[Either[ApiError.UnprocessableEntity, ServiceWorker]] =
    Future {
      tokenService.extract[AccessToken](token, TokenTypes.AccessToken) match {
        case Right(accessToken: AccessToken) =>
          ServiceWorker(accessToken.userId).asRight
        case Left(err) =>
          logger.error("Cannot extract data from access token", err)
          ApiError.UnprocessableEntity("Token", "Token is wrong").asLeft
      }

    }

  def createLoginAsToken(token: String)(loginAs: LoginAs): Future[Either[ApiError.UnprocessableEntity, Token]] = Future {
    tokenService.extract[AccessToken](token, TokenTypes.AccessToken) match {
      case Right(t) if Rollen.stringToRol(t.role) == Rollen.Medewerker =>
        Token(tokenService.generate(LoginAsToken(loginAs.userId, loginAs.role))).asRight
      case _ =>
        logger.error("Cannot read token, or wrong role")
        ApiError.UnprocessableEntity("Token", "Invalid Token or Role").asLeft
    }
  }

}
