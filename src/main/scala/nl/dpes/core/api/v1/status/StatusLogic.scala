package nl.dpes.core.api.v1.status

import nl.dpes.core.api.v1.status.StatusEndpoints.{ErrorResponse, GreenResponse, OrangeResponse, RedResponse}
import nl.dpes.core.services.StatusService
import nl.dpes.core.services.StatusService.ApplicationStatus

import scala.concurrent.Future

class StatusLogic(statusService: StatusService) {

  def getStatus(u: Unit): Future[Either[ErrorResponse, GreenResponse]] = {
    lazy val licenseInformation = statusService.licenses.map(StatusEndpoints.licenseToLicenseInformation)

    val r = statusService.getApplicationState match {
      case ApplicationStatus.Started                               => Right[ErrorResponse, GreenResponse](GreenResponse(licenses = licenseInformation))
      case ApplicationStatus.Warning                               => Left(OrangeResponse(licenses = licenseInformation): ErrorResponse)
      case ApplicationStatus.Starting | ApplicationStatus.Stopping => Left(OrangeResponse(licenses = licenseInformation): ErrorResponse)
      case ApplicationStatus.Error                                 => Left(RedResponse(licenses = licenseInformation): ErrorResponse)
      case _                                                       => Left(RedResponse(licenses = licenseInformation): ErrorResponse)
    }

    Future.successful(r)
  }

}
