package nl.dpes.core.api.v1.jobseekers

import io.circe.{<PERSON><PERSON>, Decoder, Encoder}
import nl.dpes.core.api.v1.{ApiError, ApiIndex}
import nl.dpes.core.services.sim.SubscriptionStatus
import sttp.model.{<PERSON><PERSON>, <PERSON>erNames, StatusCode}
import sttp.tapir._
import sttp.tapir.generic.auto._
import sttp.tapir.json.circe._

import java.util.UUID

class JobSeekerEndpoints(prefix: String, version: String) {
  import JobSeekerEndpoints._

  private val tagName = "Job Seekers"

  private val jobSeekerId  = path[UUID]("jobSeekerId").description("Job Seeker Id")
  private val subscription = path[String]("subscription").example("personal")
  private val booleanPath  = path[Boolean]("value").example(true)
  private val tokenPath    = path[String]("token").example("some_random_token")
  private val xApiKey      = header[Option[String]]("x-api-key").description("Optional api key")

  private val filterEmail = query[Option[String]]("filter.e-mailadres").description("Filter by email adres").example(Some("<EMAIL>"))

  private val forbiddenError  = statusCode(StatusCode.Forbidden).and(jsonBody[ApiError.Forbidden].description("Not Authorized"))
  private val notFoundError   = statusCode(StatusCode.NotFound).and(jsonBody[ApiError.NotFound].description("JobSeeker not found"))
  private val emailInUseError = statusCode(StatusCode.Conflict).and(jsonBody[ApiError.Conflict].description("Email already in use"))
  private val jssError        = statusCode(StatusCode.InternalServerError).and(jsonBody[ApiError.Unknown].description("JSS Failed"))
  private val gatewayError    = statusCode(StatusCode.InternalServerError).and(jsonBody[ApiError.Unknown].description("Error sending event"))
  private val badRequest      = statusCode(StatusCode.BadRequest).and(jsonBody[ApiError.Unknown].description("Wrong input data"))

  private val unauthorizedError =
    statusCode(StatusCode.Unauthorized).and(jsonBody[ApiError.Unauthorized].description("Invalid or no credentials"))

  private val entityValidationError =
    statusCode(StatusCode.UnprocessableEntity).and(jsonBody[ApiError.UnprocessableEntity].description("Entity validation error"))

  val findJobSeeker: PublicEndpoint[UUID, ApiError.NotFound, JobSeekerInfo, Any] =
    endpoint.get
      .in(prefix / version / "werkzoekenden" / jobSeekerId)
      .out(jsonBody[JobSeekerInfo].description("JobSeeker account"))
      .errorOut(notFoundError)
      .tag(tagName)
      .summary("Check if a JobSeeker's account exists")

  val deleteJobSeeker: Endpoint[Option[String], (UUID, Option[String], Reason), ApiError, Unit, Any] =
    endpoint.delete
      .securityIn(auth.bearer[Option[String]]().description("Bearer token"))
      .in(prefix / version / "werkzoekenden" / jobSeekerId)
      .in(xApiKey)
      .in(jsonBody[Reason])
      .out(statusCode(StatusCode.Accepted))
      .errorOut(oneOf[ApiError](oneOfVariant(notFoundError), oneOfVariant(unauthorizedError), oneOfVariant(gatewayError)))
      .tag(tagName)
      .summary("Terminate a JobSeeker's account")

  val getSubscriptions: Endpoint[String, Unit, ApiError.Unknown, SubscriptionStatus, Any] =
    endpoint.get
      .securityIn(auth.bearer[String]().description("Bearer token"))
      .in(prefix / version / "werkzoekenden" / "e-mailinschrijvingen")
      .out(jsonBody[SubscriptionStatus].description("Subscriptions status"))
      .errorOut(badRequest)
      .tag(tagName)
      .summary("Get a list of a subscription status of a job seeker")

  val subscribeToEmail: Endpoint[String, (String, Boolean), ApiError, Unit, Any] =
    endpoint.put
      .securityIn(auth.bearer[String]())
      .in(prefix / version / "werkzoekenden" / "e-mailinschrijvingen" / subscription / booleanPath)
      .out(statusCode(StatusCode.Created).description("Email subscriptions successfully added."))
      .errorOut(
        oneOf[ApiError](
          oneOfVariant(forbiddenError),
          oneOfVariant(notFoundError)
        )
      )
      .tag(tagName)
      .summary("Subscribe to the one of the subscriptions type")

  val subscribeToAllEmail: Endpoint[String, Unit, ApiError, Unit, Any] =
    endpoint.put
      .securityIn(auth.bearer[String]())
      .in(prefix / version / "werkzoekenden" / "e-mailinschrijvingen" / "alle")
      .out(statusCode(StatusCode.Created).description("Email subscriptions successfully added."))
      .errorOut(oneOf[ApiError](oneOfVariant(forbiddenError), oneOfVariant(notFoundError)))
      .tag(tagName)
      .summary("Subscribe to all available subscriptions")

  val requestChangeEmailAddress: PublicEndpoint[(UUID, ChangeEmailAddress), ApiError, Unit, Any] =
    endpoint.post
      .in(prefix / version / "werkzoekenden" / jobSeekerId / "e-mailadres")
      .in(jsonBody[ChangeEmailAddress].description("Request to change email"))
      .out(statusCode(StatusCode.Created).description("Email address change request successfully sent"))
      .errorOut(
        oneOf[ApiError](
          oneOfVariant(notFoundError),
          oneOfVariant(emailInUseError),
          oneOfVariant(entityValidationError)
        )
      )
      .tag(tagName)
      .summary("request to change email address for a job seeker's account")

  val loginState: Endpoint[String, Unit, ApiError, Logged, Any] =
    endpoint.get
      .securityIn(auth.bearer[String]())
      .in(prefix / version / "werkzoekenden" / "login")
      .out(jsonBody[Logged].description("JobSeeker that logged in"))
      .errorOut(oneOf[ApiError](oneOfVariant(unauthorizedError), oneOfVariant(forbiddenError)))
      .tag(tagName)
      .summary("Receive a refreshed access token")

  val login: PublicEndpoint[LoginRequest, ApiError, Logged, Any] =
    endpoint.post
      .in(prefix / version / "werkzoekenden" / "login")
      .in(jsonBody[LoginRequest].description("Log In data"))
      .out(jsonBody[Logged].description("JobSeeker was successfully logged in"))
      .out(statusCode(StatusCode.Created))
      .errorOut(oneOf[ApiError](oneOfVariant(unauthorizedError), oneOfVariant(forbiddenError)))
      .tag(tagName)
      .summary("Log in as a job seeker")

  val loginAs: PublicEndpoint[String, ApiError, LoggedAs, Any] =
    endpoint.post
      .in(prefix / version / "werkzoekenden" / "login-als" / tokenPath)
      .out(jsonBody[LoggedAs].description("JobSeeker was successfully logged in"))
      .out(statusCode(StatusCode.Created))
      .errorOut(oneOf[ApiError](oneOfVariant(unauthorizedError), oneOfVariant(forbiddenError), oneOfVariant(notFoundError)))
      .tag(tagName)
      .summary("Log in as a job seeker with token")

  val resetPassword: PublicEndpoint[PasswordReset, ApiError, Unit, Any] =
    endpoint.post
      .in(prefix / version / "werkzoekenden" / "wachtwoord" / "herstel")
      .in(jsonBody[PasswordReset].description("Password reset request"))
      .out(statusCode(StatusCode.Created).description("Password request successfully sent"))
      .errorOut(oneOf[ApiError](oneOfVariant(notFoundError), oneOfVariant(gatewayError)))
      .tag(tagName)
      .summary("request a password reset")

  val replacePassword: Endpoint[String, (UUID, PasswordReplace), ApiError, Unit, Any] =
    endpoint.put
      .securityIn(auth.bearer[String]())
      .in(prefix / version / "werkzoekenden" / jobSeekerId / "wachtwoord")
      .in(jsonBody[PasswordReplace].description("Password replace request"))
      .out(statusCode(StatusCode.NoContent).description("Password successfully replaced"))
      .errorOut(oneOf[ApiError](oneOfVariant(entityValidationError), oneOfVariant(entityValidationError)))
      .tag(tagName)
      .summary("Replace the password")

  val setPasswordExternal: PublicEndpoint[ExternalPassword, ApiError, Unit, Any] =
    endpoint.post
      .in(prefix / version / "werkzoekenden" / "wachtwoord-external")
      .in(jsonBody[ExternalPassword])
      .out(statusCode(StatusCode.Created).description("Password of JobSeeker successfully set"))
      .errorOut(oneOf[ApiError](oneOfVariant(entityValidationError), oneOfVariant(forbiddenError), oneOfVariant(notFoundError)))
      .tag(tagName)
      .summary("Set the password of an external JobSeeker during the activation")

  val setPassword: PublicEndpoint[Password, ApiError, Logged, Any] =
    endpoint.post
      .in(prefix / version / "werkzoekenden" / "wachtwoord")
      .in(jsonBody[Password])
      .out(jsonBody[Logged])
      .out(statusCode(StatusCode.Created).description("Password of JobSeeker successfully set"))
      .errorOut(
        oneOf[ApiError](
          oneOfVariant(entityValidationError),
          oneOfVariant(notFoundError),
          oneOfVariant(unauthorizedError)
        )
      )
      .tag(tagName)
      .summary("Set the password of a JobSeeker account")

  val verify: PublicEndpoint[Verification, ApiError, Unit, Any] =
    endpoint.put
      .in(prefix / version / "werkzoekenden" / "verificatie")
      .in(jsonBody[Verification].description("Token to verify"))
      .out(statusCode(StatusCode.NoContent).description("JobSeeker successfully verified"))
      .errorOut(
        oneOf[ApiError](
          oneOfVariant(unauthorizedError),
          oneOfVariant(notFoundError),
          oneOfVariant(emailInUseError),
          oneOfVariant(badRequest)
        )
      )
      .tag(tagName)
      .summary("Verify a JobSeeker account")

  val registerExternalJobSeeker: PublicEndpoint[ExternalRegistration, ApiError, (Registered, String), Any] =
    endpoint.post
      .in(prefix / version / "werkzoekenden" / "external")
      .in(jsonBody[ExternalRegistration].description("Registration Data"))
      .out(jsonBody[Registered].description("JobSeeker's account was successfully registered"))
      .out(statusCode(StatusCode.Created))
      .out(header[String](HeaderNames.ContentLocation).description("The location of a content"))
      .errorOut(
        oneOf[ApiError](
          oneOfVariant(emailInUseError),
          oneOfVariant(entityValidationError),
          oneOfVariant(jssError)
        )
      )
      .tag(tagName)
      .summary("Register a 3rd party JobSeeker account")

  val listJobSeekers: PublicEndpoint[Option[String], Unit, JobSeekers, Any] =
    endpoint.get
      .in(prefix / version / "werkzoekenden")
      .in(filterEmail)
      .out(jsonBody[JobSeekers])
      .tag(tagName)
      .summary("Retrieve a list of job seekers")

  val registerJobSeeker: PublicEndpoint[Registration, ApiError, (Registered, String), Any] =
    endpoint.post
      .in(prefix / version / "werkzoekenden")
      .in(jsonBody[Registration].description("Registration Data"))
      .out(jsonBody[Registered].description("JobSeeker's account was successfully registered"))
      .out(statusCode(StatusCode.Created))
      .out(header[String](HeaderNames.ContentLocation).description("The location of a content"))
      .errorOut(
        oneOf[ApiError](
          oneOfVariant(emailInUseError),
          oneOfVariant(entityValidationError),
          oneOfVariant(jssError)
        )
      )
      .tag(tagName)
      .summary("Register a JobSeeker account")

}

object JobSeekerEndpoints {

  final case class Registration(eMailadres: String, site: String, verificatieUrl: String)
  implicit val registrationCodec: Codec[Registration] = io.circe.generic.semiauto.deriveCodec[Registration]

  final case class ExternalRegistration(id: String, eMailadres: String, site: String)
  implicit val externalRegistrationCodec: Codec[ExternalRegistration] = io.circe.generic.semiauto.deriveCodec[ExternalRegistration]

  final case class PasswordReplace(huidigWachtwoord: String, nieuwWachtwoord: String)
  implicit val passwordreplaceCodec: Codec[PasswordReplace] = io.circe.generic.semiauto.deriveCodec[PasswordReplace]

  final case class Password(token: String, wachtwoord: String)
  implicit val passwordCodec: Codec[Password] = io.circe.generic.semiauto.deriveCodec[Password]

  final case class ExternalPassword(werkzoekendeId: String, wachtwoord: String)
  implicit val externalPasswordCodec: Codec[ExternalPassword] = io.circe.generic.semiauto.deriveCodec[ExternalPassword]

  final case class JobSeekers(items: Seq[JobSeekerInfo]) extends ApiIndex[JobSeekerInfo](items)
  implicit val jobSeekersCodec: Codec[JobSeekers] = io.circe.generic.semiauto.deriveCodec[JobSeekers]

  final case class PasswordReset(eMailadres: String, herstelUrl: String, site: String, template: Option[String] = None)
  implicit val passwordResetCodec: Codec[PasswordReset] = io.circe.generic.semiauto.deriveCodec[PasswordReset]

  final case class Registered(werkzoekendeId: String, token: String)
  implicit val registeredCodec: Codec[Registered] = io.circe.generic.semiauto.deriveCodec[Registered]

  final case class Verification(token: String)
  implicit val verificationCodec: Codec[Verification] = io.circe.generic.semiauto.deriveCodec[Verification]

  final case class LoginRequest(eMailadres: String, wachtwoord: String, site: String)
  implicit val loginRequestCodec: Codec[LoginRequest] = io.circe.generic.semiauto.deriveCodec[LoginRequest]

  final case class Logged(werkzoekendeId: String, sanDiegoId: Option[Int], token: String)
  implicit val loggedEncoder: Encoder[Logged] = io.circe.generic.semiauto.deriveEncoder[Logged].mapJson(_.dropNullValues)
  implicit val loggedDecoder: Decoder[Logged] = io.circe.generic.semiauto.deriveDecoder[Logged]

  final case class LoggedAs(werkzoekendeId: String, eMailadres: String, sanDiegoId: Option[Int], token: String)
  implicit val loggedAsEncoder: Encoder[LoggedAs] = io.circe.generic.semiauto.deriveEncoder[LoggedAs].mapJson(_.deepDropNullValues)
  implicit val loggedAsDecoder: Decoder[LoggedAs] = io.circe.generic.semiauto.deriveDecoder[LoggedAs]

  final case class JobSeekerInfo(
    werkzoekendeId: String,
    site: String,
    sanDiegoId: Option[Int],
    eMailadres: String,
    naam: String,
    isSavedSearchOnly: Boolean = false
  )

  implicit val jobSeekerInfoEnc: Encoder[JobSeekerInfo] =
    io.circe.generic.semiauto.deriveEncoder[JobSeekerInfo].mapJson(_.deepDropNullValues)
  implicit val jobSeekerInfoDec: Decoder[JobSeekerInfo] = io.circe.generic.semiauto.deriveDecoder[JobSeekerInfo]

  final case class Reason(reden: String)
  implicit val rCodec: Codec[Reason] = io.circe.generic.semiauto.deriveCodec[Reason]

  final case class ChangeEmailAddress(nieuwEMailadres: String, verificatieUrl: String)
  implicit val cearCodec: Codec[ChangeEmailAddress] = io.circe.generic.semiauto.deriveCodec[ChangeEmailAddress]

}
