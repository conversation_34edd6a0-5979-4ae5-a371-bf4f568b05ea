package nl.dpes.core.api.v1.jobseekers

import cats.syntax.either._
import com.auth0.jwt.exceptions.{JWTDecodeException, TokenExpiredException}
import nl.dpes.core.api.v1.ApiError
import nl.dpes.core.api.v1.ApiError._
import nl.dpes.core.api.v1.jobseekers.JobSeekerEndpoints._
import nl.dpes.core.domain.Werkzoekende._
import nl.dpes.core.domain.exceptions._
import nl.dpes.core.domain.werkzoekende.RegisterWerkzoekendeHandler
import nl.dpes.core.domain.{<PERSON><PERSON>, <PERSON>, Wachtwoord}
import nl.dpes.core.projections.core.CoreWerkzoekendeAccountProjections
import nl.dpes.core.projections.core.CoreWerkzoekendeAccountProjections.CoreWerkzoekendeAccount
import nl.dpes.core.services.security.PasswordHasher.HashedPassword
import nl.dpes.core.services.security.tokens._
import nl.dpes.core.services.sim.{HttpSimClient, SubscriptionStatus}
import nl.dpes.core.services.{IdentifierService, PasswordService, TokenService}
import org.axonframework.commandhandling.gateway.CommandGateway
import org.axonframework.modelling.command.AggregateNotFoundException
import org.slf4j.Logger

import java.time.Instant
import java.util.UUID
import scala.concurrent.{ExecutionContext, Future}
import scala.util.Try

class JobSeekerLogic(
  jobSeekerProjections: CoreWerkzoekendeAccountProjections,
  commandGateway: CommandGateway,
  tokenService: TokenService,
  passwordService: PasswordService,
  sanDiegoPasswordService: PasswordService,
  apiKeys: List[String],
  simClient: HttpSimClient,
  logger: Logger,
  executor: ExecutionContext
) {

  implicit val ec             = executor
  private val registerHandler = new RegisterWerkzoekendeHandler(commandGateway, new IdentifierService, tokenService, jobSeekerProjections)

  def findJobSeeker(jobSeekerId: UUID): Future[Either[NotFound, JobSeekerInfo]] = Future {
    jobSeekerProjections.findByWerkzoekendeId(jobSeekerId.toString) match {
      case Some(found) =>
        Right(
          JobSeekerInfo(
            werkzoekendeId = found.werkzoekendeId,
            site = found.site,
            sanDiegoId = found.sanDiegoId,
            eMailadres = found.eMailadres,
            naam = "",
            isSavedSearchOnly = false
          )
        )
      case None => Left(NotFound("Cannot find JobSeeker", "JobSeeker", Some(jobSeekerId.toString)))
    }
  }

  def deleteJobSeeker(tokenMaybe: Option[String])(input: (UUID, Option[String], Reason)): Future[Either[ApiError, Unit]] = {
    val jobSeekerId = input._1.toString
    val apiKeyMaybe = input._2
    val reason      = input._3

    Future {
      (apiKeyMaybe, tokenMaybe) match {
        case (Some(apiKey), _) if validApiKey(apiKey) => terminate(jobSeekerId, reason)
        case (_, Some(token)) =>
          tokenService.extract[AccessToken](token, TokenTypes.AccessToken) match {
            case Right(accessToken) if accessToken.userId == jobSeekerId                         => terminate(jobSeekerId, reason)
            case Right(accessToken) if Rollen.stringToRol(accessToken.role) == Rollen.Medewerker => terminate(jobSeekerId, reason)
            case _                                                                               => Left(Unauthorized("Caller", "You are not authorized to delete JobSeeker"))
          }
        case _ => Left(Unauthorized("Caller", "Incorrect api key"))
      }
    }

  }

  def getSubscriptions(token: String)(u: Unit): Future[Either[Unknown, SubscriptionStatus]] =
    tokenService.extract[AccessToken](token, TokenTypes.AccessToken) match {
      case Right(accessToken) if Rollen.stringToRol(accessToken.role) == Rollen.Werkzoekende && accessToken.site.nonEmpty =>
        logger.info(s"getting subscriptions for ${accessToken.userId} and site ${accessToken.site.get}")
        simClient.getSubscriptions(accessToken.userId, Sites.stringToSite(accessToken.site.get)).map(Right(_))
      case t =>
        logger.error(s"issue in getting subscriptions for token, $t")
        Future.successful(Left(Unknown("Wrong role or site not supported")))
    }

  def subscribeToEmail(token: String)(input: (String, Boolean)): Future[Either[ApiError, Unit]] = {
    val subscriptionName   = input._1
    val subscriptionStatus = input._2

    tokenService.extract[AccessToken](token, TokenTypes.AccessToken) match {
      case Right(accessToken) if Rollen.stringToRol(accessToken.role) == Rollen.Werkzoekende =>
        val f = if (subscriptionStatus) {
          Future(commandGateway.sendAndWait[Unit](SchrijfInVoorEMail(accessToken.userId, subscriptionName)))
        } else {
          Future(commandGateway.sendAndWait[Unit](UitschrijfVoorEmail(accessToken.userId, subscriptionName)))
        }

        f.map(Right(_)).recover { err =>
          logger.error("Error calling command gateway to change subscription for a user", err)
          Left(Unknown(err.getMessage))
        }
      case Right(_) =>
        Future.successful(Left(Unauthorized("Caller", "You're not eligible for this operation")))
      case Left(err) =>
        logger.error("Error extracting access token", err)
        Future.successful(Left(Unknown(err.getMessage)))
    }
  }

  def subscribeToAllEmails(token: String)(u: Unit): Future[Either[ApiError, Unit]] =
    tokenService.extract[AccessToken](token, TokenTypes.AccessToken) match {
      case Right(accessToken) if Rollen.stringToRol(accessToken.role) == Rollen.Werkzoekende =>
        Future(commandGateway.sendAndWait[Unit](SchrijfInVoorAlleEMails(accessToken.userId)))
          .map(Right(_))
          .recover { err =>
            logger.error("Error calling command gateway to change subscription for a user", err)
            Left(Unknown(err.getMessage))
          }
      case Right(_) =>
        Future.successful(Left(Unauthorized("Caller", "You're not eligible for this operation")))
      case Left(err) =>
        logger.error("Error extracting access token", err)
        Future.successful(Left(Unknown(err.getMessage)))
    }

  def requestEmailChange(input: (UUID, ChangeEmailAddress)): Future[Either[ApiError, Unit]] = {
    val jobSeekerId = input._1.toString
    val request     = input._2

    val emailWijzigTokenId = UUID.randomUUID().toString
    val token              = tokenService.generate(ChangeEmailToken(jobSeekerId, Rollen.Werkzoekende, emailWijzigTokenId))

    Future {
      commandGateway.sendAndWait[Unit](
        VerzoekEMailadresWijziging(jobSeekerId, request.nieuwEMailadres, request.verificatieUrl, token, emailWijzigTokenId)
      )
    }.map(Right(_))
      .recover {
        case e: EMailadresIsAlInGebruik =>
          logger.warn(s"Email is already in use $e")
          Left(Conflict(s"Email is already in use $e"))
        case e =>
          Left(Unknown(e.getMessage))
      }

  }

  def loginState(token: String)(u: Unit): Future[Either[ApiError, Logged]] =
    tokenService.extract[AccessToken](token, TokenTypes.AccessToken) match {
      case Right(accessToken) if Rollen.stringToRol(accessToken.role) == Rollen.Werkzoekende =>
        Future {
          jobSeekerProjections.findByWerkzoekendeId(accessToken.userId) match {
            case Some(found) =>
              logger.info("Login state called successfully")
              Right(Logged(accessToken.userId, accessToken.sanDiegoId, tokenService.generate(accessToken)))
            case None =>
              Left(Unauthorized("Caller", "You're not eligible for this operation"))
          }
        }
      case Right(_) =>
        Future.successful(Left(Unauthorized("Caller", "You're not eligible for this operation")))
      case Left(err) =>
        logger.error("Error extracting access token", err)
        Future.successful(Left(Unknown(err.getMessage)))
    }

  def login(input: LoginRequest): Future[Either[ApiError, Logged]] = Future {
    jobSeekerProjections.findByEMailadres(input.eMailadres, input.site) match {
      case Some(CoreWerkzoekendeAccount(jobSeekerId, maybeSanDiegoId, emailAddress, site, Some(hash), Some(salt), Some(true), _))
          if sanDiegoPasswordService.verifyHashedPassword(hash, salt, input.wachtwoord) =>
        //old san diego pass
        logger.info("Migrated San Diego job seeker legacy password")
        // Set Password with new hash
        val hashedPassword = passwordService.hashPassword(input.wachtwoord)

        commandGateway.send(StelWachtwoordIn(jobSeekerId, Wachtwoord(hashedPassword.hash, hashedPassword.salt)))

        val token = tokenService.generate(AccessToken(jobSeekerId, maybeSanDiegoId, Rollen.Werkzoekende, Some(emailAddress), Some(site)))
        jobSeekerProjections.updateLastActivity(jobSeekerId, Instant.now().toEpochMilli)

        Right(Logged(jobSeekerId, maybeSanDiegoId, token))
      case Some(CoreWerkzoekendeAccount(jobSeekerId, maybeSanDiegoId, emailAddress, site, Some(hash), Some(salt), _, _))
          if passwordService.verifyHashedPassword(hash, salt, input.wachtwoord) =>
        //regular pass
        val token = tokenService.generate(AccessToken(jobSeekerId, maybeSanDiegoId, Rollen.Werkzoekende, Some(emailAddress), Some(site)))

        jobSeekerProjections.updateLastActivity(jobSeekerId, Instant.now().toEpochMilli)

        Right(Logged(jobSeekerId, maybeSanDiegoId, token))
      case accountOpt =>
        logger.info(s"Error logging in for the input (emailAddress: ${input.eMailadres}, site: ${input.site}) and the account $accountOpt")
        Left(Unauthorized("JobSeeker", "Wrong credentials for"))
    }
  }

  def loginAs(token: String): Future[Either[ApiError, LoggedAs]] = Future {
    tokenService.extract[LoginAsToken](token, TokenTypes.LoginAsToken) match {
      case Right(loggedAsToken) if Rollen.Werkzoekende.equals(Rollen.stringToRol(loggedAsToken.role)) =>
        jobSeekerProjections.findByWerkzoekendeId(loggedAsToken.userId) match {
          case Some(found) =>
            val token = tokenService.generate(
              AccessToken(found.werkzoekendeId, found.sanDiegoId, Rollen.Werkzoekende, Some(found.eMailadres), Some(found.site))
            )
            Right(LoggedAs(found.werkzoekendeId, found.eMailadres, found.sanDiegoId, token))
          case None =>
            logger.warn(s"Cannot find user with id ${loggedAsToken.userId}")
            Left(NotFound("Cannot find JobSeeker", "JobSeeker", Some(loggedAsToken.userId)))
        }
      case Right(_) =>
        logger.error(s"Logging in as a JobSeeker failed cause wrong role been provided in token")
        Left(Unauthorized("Login As Agent", "Wrong role being used"))
      case Left(err) =>
        logger.error(s"Logging in as a jobseeker failed with error: ${err.getMessage}")
        Left(Unauthorized("Login As Agent", "Cannot read token"))
    }
  }

  def resetPassword(pwdReset: PasswordReset): Future[Either[ApiError, Unit]] = Future {
    jobSeekerProjections.findByEMailadres(pwdReset.eMailadres, pwdReset.site) match {
      case Some(found) =>
        val herstelTokenId = UUID.randomUUID().toString
        val herstelToken   = tokenService.generate(ResetPasswordToken(found.werkzoekendeId, Rollen.Werkzoekende, herstelTokenId))
        Try {
          commandGateway.sendAndWait[Unit](
            VergeetWachtwoord(
              found.werkzoekendeId,
              found.eMailadres,
              pwdReset.site,
              pwdReset.herstelUrl,
              herstelToken,
              herstelTokenId,
              pwdReset.template
            )
          )
        }.toEither.leftMap { err =>
          logger.error(s"Error changing password for request $pwdReset", err)
          Unknown(err.getMessage)
        }
      case None =>
        logger.warn(s"JobSeeker with email ${pwdReset.eMailadres} and site ${pwdReset.site} wasn't found")
        Left(NotFound("Job Seeker with such email not found", "JobSeeker", Some(pwdReset.eMailadres)))
    }
  }

  def replacePassword(token: String)(input: (UUID, PasswordReplace)): Future[Either[ApiError, Unit]] = {
    val jobSeekerId = input._1.toString
    val request     = input._2

    Future {
      tokenService
        .extract[AccessToken](token, TokenTypes.AccessToken)
        .leftMap { err =>
          logger.error(s"Error replacing password for jobSeekerId $jobSeekerId", err)
          Unauthorized("JobSeeker", "Token cannot be read.")
        }
        .flatMap { accessToken =>
          commandGateway
            .sendAndWait[Either[WachtwoordKomtNietOvereen, Unit]](
              WijzigWachtwoord(
                accessToken.userId,
                request.huidigWachtwoord,
                request.nieuwWachtwoord
              )
            )
            .leftMap(_ => UnprocessableEntity("PasswordReplace", s"Password doesn't match for JobSeekerId $jobSeekerId"))
        }
    }
  }

  def setPasswordExternal(externalPassword: ExternalPassword): Future[Either[ApiError, Unit]] = {
    val jobSeekerId = externalPassword.werkzoekendeId
    val password    = externalPassword.wachtwoord

    Future {
      for {
        _ <- jobSeekerProjections.findByWerkzoekendeId(externalPassword.werkzoekendeId) match {
          case Some(_) => Right(())
          case None    => Left(NotFound("JobSeeker Not Found", "JobSeeker", Some(externalPassword.werkzoekendeId)))
        }

        hashedPass <- passwordService.validateAndHashPassword(password) match {
          case Left(err)   => Left(UnprocessableEntity("ExternalPassword", s"Password does not respect policy: $err"))
          case Right(pass) => Right(pass)
        }

        result <- Try(
          commandGateway
            .sendAndWait[Unit](StelWachtwoordInVoorExternalWerkzoekende(jobSeekerId, hashedPass))
        ).toEither
          .leftMap { err =>
            logger.error("Error sending command to gateway", err)
            Unknown(err.getMessage)
          }
      } yield result
    }
  }

  def setPassword(pwd: Password): Future[Either[ApiError, Logged]] = getAccessTokenFromWachtwoordRequest(pwd).map(_.map { at =>
    Logged(at.userId, at.sanDiegoId, tokenService.generate(at))
  })

  def verify(verification: Verification): Future[Either[ApiError, Unit]] =
    Future {
      val result = for {
        token <- tokenService.extract[Token](verification.token, TokenTypes.VerificationToken, TokenTypes.ChangeEmailToken)
        _ <- token match {
          case token: VerificationToken =>
            Try(commandGateway.sendAndWait[Unit](Verifieer(token.userId))).toEither
          case token: ChangeEmailToken =>
            Try(commandGateway.sendAndWait[Unit](BevestigEMailadresWijziging(token.userId, token.tokenId))).toEither
          case token =>
            Left(InvalidTokenType(token.tokenType))
        }
      } yield ()

      result.leftMap {
        case _: EMailadresIsAlInGebruik    => Conflict("Email is already in use")
        case _: AggregateNotFoundException => NotFound("JobSeeker not found", "JobSeeker", None)
        case e @ (WijzigEMailadresTokenOngeldig(_) | WachtwoordTokenOngeldig(_) | InvalidTokenType(_)) =>
          Unauthorized("Caller", e.getMessage)
        case e: TokenExpiredException                                         => Unauthorized("Caller", e.getMessage)
        case e @ (EMailadresIsNietGeverifieerd(_) | WachtwoordAlIngesteld(_)) => BadRequest(e.getMessage)
        case e: JWTDecodeException                                            => BadRequest(e.getMessage)
        case e                                                                => Unknown(e.getMessage)
      }
    }

  def registerExternalJobSeeker(externalRegistration: ExternalRegistration): Future[Either[ApiError, (Registered, String)]] =
    registerHandler.registerExternalWerkzoekende(externalRegistration.id, externalRegistration.eMailadres, externalRegistration.site) map {
      case Left(_)  => Left(Conflict("Email is already in use"))
      case Right(r) => Right(Registered(r.werkzoekendeId, r.token) -> s"/api/v1/werkzoekenden/${r.werkzoekendeId}")
    }

  def listJobSeekers(filter: Option[String]): Future[Either[Unit, JobSeekers]] = filter match {
    case None => Future.successful(Right(JobSeekers(List.empty)))
    case Some(email) =>
      val list = Sites.Alle
        .flatMap { site =>
          jobSeekerProjections.findByEMailadres(email, site)
        }
        .map { found =>
          JobSeekerInfo(
            werkzoekendeId = found.werkzoekendeId,
            site = found.site,
            sanDiegoId = found.sanDiegoId,
            eMailadres = found.eMailadres,
            naam = "",
            isSavedSearchOnly = false
          )
        }
      Future.successful(Right(JobSeekers(list)))
  }

  def registerJobSeeker(registration: Registration): Future[Either[ApiError, (Registered, String)]] =
    registerHandler.registerWerkzoekende(registration.eMailadres, registration.site, registration.verificatieUrl).map {
      case Right(r) =>
        Right(Registered(r.werkzoekendeId, r.token) -> s"/api/v1/werkzoekenden/${r.werkzoekendeId}")
      case Left(EMailadresIsAlInGebruik(eMailadres)) =>
        Left(Conflict(s"Email is already in user $eMailadres"))
      case Left(err) =>
        logger.error(s"Error registering job sekeer.", err)
        Left(Unknown(err.getMessage))
    }

  private def getAccessTokenFromWachtwoordRequest(pwd: Password): Future[Either[ApiError, AccessToken]] = {
    val tokenAndHash: Either[ApiError, (Token, HashedPassword, Option[CoreWerkzoekendeAccount])] = for {
      token <- tokenService
        .extract[PasswordToken](pwd.token, TokenTypes.VerificationToken, TokenTypes.ResetPasswordToken)
        .leftMap { err =>
          logger.error(s"Error extracting token from set password request.", err)
          Unauthorized("Caller", err.getMessage)
        }
      // Encrypt the password immediately because it gets added to the event log
      hashed <- passwordService
        .validateAndHashPassword(pwd.wachtwoord)
        .leftMap { err =>
          UnprocessableEntity("ExternalPassword", s"Password does not respect policy: $err")
        }
    } yield (token, hashed, jobSeekerProjections.findByWerkzoekendeId(token.userId))

    Future {
      tokenAndHash.flatMap {
        case (token: VerificationToken, hashed, Some(werkzoekendeAccount)) =>
          Try {
            commandGateway.sendAndWait[Unit](StelWachtwoordIn(token.userId, hashed))
          }.toEither
            .map { _ =>
              AccessToken(
                token.userId,
                werkzoekendeAccount.sanDiegoId,
                Rollen.Werkzoekende,
                Some(werkzoekendeAccount.eMailadres),
                Some(werkzoekendeAccount.site)
              )
            }
            .leftMap(err => Unauthorized("Caller", err.getMessage))
        case (token: ResetPasswordToken, hashed, maybeWerkzoekendeAccount) =>
          maybeWerkzoekendeAccount match {
            case Some(werkzoekende) =>
              Try {
                commandGateway.sendAndWait[Unit](StelWachtwoordOpnieuwIn(token.userId, hashed, token.tokenId))
              }.toEither
                .map { _ =>
                  AccessToken(
                    token.userId,
                    werkzoekende.sanDiegoId,
                    Rollen.Werkzoekende,
                    Some(werkzoekende.eMailadres),
                    Some(werkzoekende.site)
                  )
                }
                .leftMap(err => Unauthorized("Caller", err.getMessage))
            case None => Left(NotFound("Job Seeker Not Found", "JobSeeker", Some(token.userId)))
          }
        case (token: Token, _, _) =>
          Left(NotFound("Job Seeker Not Found", "JobSeeker", Some(token.userId)))
      }
    }

  }

  private def terminate(id: String, reason: Reason): Either[Unknown, Unit] =
    jobSeekerProjections.findByWerkzoekendeId(id) match {
      case Some(_) =>
        Try(commandGateway.sendAndWait(ZegAccountOp(id, reason.reden))).toEither.leftMap { err =>
          logger.error("Error sending Delete Account event", err)
          Unknown("Error sending Delete Account event")
        }
      case None => Right(())
    }

  private def validApiKey(key: String): Boolean = apiKeys.contains(key)

}
