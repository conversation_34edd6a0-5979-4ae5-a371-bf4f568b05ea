package nl.dpes.core.api.v1.recruiters

import io.circe.Codec
import io.circe.generic.semiauto._
import nl.dpes.core.api.v1.{ApiError, ApiIndex}
import sttp.model.StatusCode
import sttp.tapir.CodecFormat.TextPlain
import sttp.tapir._
import sttp.tapir.generic.auto._
import sttp.tapir.json.circe._

class RecruitersEndpoints(prefix: String, version: String) {
  import RecruitersEndpoints._

  private val tagName = "Recruiters"

  private val recruiterId = path[String]("recruiterId").description("Recruiter Id path param")
  private val jobSeekerId = path[String]("jobSeekerId").description("Job Seeker Id path param")
  private val page        = query[Option[Int]]("page").default(Some(1)).description("Page number")
  private val limit       = query[Option[Int]]("limit").default(Some(50)).description("Limit per page")

  private val forbidden           = statusCode(StatusCode.Forbidden).and(jsonBody[ApiError.Forbidden].description("Operation not allowed"))
  private val notFound            = statusCode(StatusCode.NotFound).and(jsonBody[ApiError.NotFound].description("Recruiter Not Found"))
  private val internalServerError = statusCode(StatusCode.InternalServerError).and(jsonBody[ApiError.Unknown])

  private val ids =
    query[Option[JobSeekerIds]]("werkzoekendeIds").default(Some(JobSeekerIds.empty)).description("Comma separated list of JobSeeker's Ids")

  val recruitersIndex: PublicEndpoint[Option[Int], ApiError.Forbidden, Recruiters, Any] =
    endpoint.get
      .in(prefix / version / "recruiters")
      .in(query[Option[Int]]("filter.sanDiegoId").description("Filter by SanDiegoId"))
      .out(jsonBody[Recruiters].description("Recruiters"))
      .errorOut(forbidden)
      .tag(tagName)
      .summary("Retrieve list of recruiters")

  val importRecruiter: PublicEndpoint[Import, Unit, RecruiterId, Any] =
    endpoint.post
      .in(prefix / version / "recruiters")
      .in(jsonBody[Import].description("Import"))
      .out(jsonBody[RecruiterId].description("Recruiter import accepted"))
      .out(statusCode(StatusCode.Accepted))
      .tag(tagName)
      .summary("Import a recruiter")

  val migrateToSalesforce: PublicEndpoint[MigrateToSalesforceId, ApiError.NotFound, RecruiterId, Any] =
    endpoint.post
      .in(prefix / version / "recruiters" / "migrate")
      .in(jsonBody[MigrateToSalesforceId])
      .out(jsonBody[RecruiterId])
      .out(statusCode(StatusCode.Accepted).description("Recruiter(s) migrate request accepted"))
      .errorOut(jsonBody[ApiError.NotFound])
      .errorOut(statusCode(StatusCode.NotFound).description("Recruiter(s) Not Found"))
      .tag(tagName)
      .summary("Migrate one or more recruiters to salesforceId")

  val saveFavourite: PublicEndpoint[(String, String), ApiError.NotFound, Unit, Any] =
    endpoint.post
      .in(prefix / version / "recruiters" / recruiterId / "favorieten" / jobSeekerId)
      .errorOut(notFound)
      .out(statusCode(StatusCode.Accepted).description("Favourite Save Request Accepted"))
      .tag(tagName)
      .summary("Save a jobSeeker")

  val deleteFavourite: PublicEndpoint[(String, String), ApiError.NotFound, Unit, Any] =
    endpoint.delete
      .in(prefix / version / "recruiters" / recruiterId / "favorieten" / jobSeekerId)
      .errorOut(notFound)
      .out(statusCode(StatusCode.NoContent).description("Favourite Delete Request Accepted"))
      .tag(tagName)
      .summary("Delete a jobSeeker")

  val favouritesIndexPaging: PublicEndpoint[(String, Option[Int], Option[Int], Option[JobSeekerIds]), ApiError, FavouritesResponse, Any] =
    endpoint.get
      .in(prefix / version / "recruiters" / recruiterId / "favorieten-paging")
      .in(page)
      .in(limit)
      .in(ids)
      .out(jsonBody[FavouritesResponse].description("Favourites Response"))
      .errorOut(
        oneOf[ApiError](
          oneOfVariant(notFound),
          oneOfVariant(internalServerError)
        )
      )
      .tag(tagName)
      .summary("List favourites")

  val retrieveSingleRecruiter: PublicEndpoint[String, ApiError.NotFound, Recruiter, Any] =
    endpoint.get
      .in(prefix / version / "recruiters" / recruiterId)
      .errorOut(notFound)
      .out(jsonBody[Recruiter])
      .tag(tagName)
      .summary("Retrieve a recruiter, when present")

}

object RecruitersEndpoints {

  final case class Recruiter(recruiterId: String, eMailadres: String, site: String)
  implicit val rEnc: Codec[Recruiter] = deriveCodec[Recruiter]

  final case class Recruiters(items: Seq[Recruiter]) extends ApiIndex[Recruiter](items)
  implicit val rsCodec: Codec[Recruiters] = deriveCodec[Recruiters]

  final case class RecruiterId(id: String)
  implicit val rIdCodec: Codec[RecruiterId] = deriveCodec[RecruiterId]

  final case class Favourites(items: Seq[Favourite])
  implicit val fsCodec: Codec[Favourites] = deriveCodec[Favourites]

  final case class Favourite(werkzoekendeId: String)
  implicit val fCodec: Codec[Favourite] = deriveCodec[Favourite]

  final case class FavouritesResponse(items: Seq[Favourite], totalCount: Long)
  implicit val fResponseCodec: Codec[FavouritesResponse] = deriveCodec[FavouritesResponse]

  final case class Import(salesForceId: String, eMailadres: String, site: String)
  implicit val iCodec: Codec[Import] = deriveCodec[Import]

  final case class MigrateToSalesforceId(sanDiegoIds: Seq[Int], salesForceId: String)
  implicit val mCodec: Codec[MigrateToSalesforceId] = deriveCodec[MigrateToSalesforceId]

  final case class JobSeekerIds(ids: List[String])

  object JobSeekerIds {
    val empty = JobSeekerIds(List.empty)
  }

  implicit val codec: sttp.tapir.Codec[String, JobSeekerIds, TextPlain] = {
    def decode(s: String): DecodeResult[JobSeekerIds] = DecodeResult.Value(JobSeekerIds(s.split(",").toList))
    def encode(ids: JobSeekerIds): String             = ids.ids.mkString(",")
    sttp.tapir.Codec.string.mapDecode(decode)(encode)
  }
}
