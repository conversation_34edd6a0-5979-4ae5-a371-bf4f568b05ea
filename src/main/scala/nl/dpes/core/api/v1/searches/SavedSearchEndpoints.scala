package nl.dpes.core.api.v1.searches

import io.circe.generic.semiauto._
import io.circe.{Code<PERSON>, Decoder, Encoder}
import nl.dpes.core.api.v1.{ApiError, ApiIndex}
import sttp.model.{HeaderNames, StatusCode}
import sttp.tapir._
import sttp.tapir.generic.auto._
import sttp.tapir.json.circe._

class SavedSearchEndpoints(prefix: String, version: String) {
  import SavedSearchEndpoints._

  private val tagName = "Saved Searches"

  private val recruiterId                = path[String]("recruiterId").description("Recruiter Id")
  private val savedSearchId              = path[String]("savedSearchId").description("Saved Search Id")
  private def notFoundError(msg: String) = statusCode(StatusCode.NotFound).and(jsonBody[ApiError.NotFound].description(msg))
  private val unknown                    = statusCode(StatusCode.InternalServerError).and(jsonBody[ApiError.Unknown].description("Internal Server Error"))

  private val search = SavedSearch(
    "saved-search-id",
    "accotunt-id",
    "search-id",
    "some name",
    Dagelijks,
    SearchParams()
  )

  val getSearch: PublicEndpoint[SearchIdInput, ApiError.NotFound, SavedSearch, Any] =
    endpoint.get
      .in(prefix / version / "recruiters" / recruiterId / "zoekopdrachten" / savedSearchId)
      .out(jsonBody[SavedSearch].example(search))
      .mapInTo[SearchIdInput]
      .errorOut(notFoundError("Saved Search Not Found"))
      .tag(tagName)
      .summary("Get Saved Search by Saved Search Id")

  val editSearch: PublicEndpoint[(SearchIdInput, Search), ApiError, Unit, Any] =
    endpoint.put
      .in(prefix / version / "recruiters" / recruiterId / "zoekopdrachten" / savedSearchId)
      .mapInTo[SearchIdInput]
      .in(jsonBody[Search])
      .errorOut(oneOf[ApiError](oneOfVariant(notFoundError("Account Not Found")), oneOfVariant(unknown)))
      .out(statusCode(StatusCode.NoContent).description("Search was successfully edited."))
      .tag(tagName)
      .summary("Edit Search for a Recruiter")

  val deleteSearch: PublicEndpoint[SearchIdInput, ApiError.Unknown, Unit, Any] =
    endpoint.delete
      .in(prefix / version / "recruiters" / recruiterId / "zoekopdrachten" / savedSearchId)
      .mapInTo[SearchIdInput]
      .errorOut(unknown)
      .out(statusCode(StatusCode.NoContent).description("Search was successfully deleted."))
      .tag(tagName)
      .summary("Delete Search for a Recruiter")

  val listSearches: PublicEndpoint[String, Unit, SavedSearches, Any] =
    endpoint.get
      .in(prefix / version / "recruiters" / recruiterId / "zoekopdrachten")
      .out(jsonBody[SavedSearches])
      .tag(tagName)
      .summary("List Saved Search for a Recruiter")

  val saveSearch: PublicEndpoint[(String, Search), ApiError, (SavedSearchId, String), Any] =
    endpoint.post
      .in(prefix / version / "recruiters" / recruiterId / "zoekopdrachten")
      .in(jsonBody[Search])
      .errorOut(oneOf[ApiError](oneOfVariant(notFoundError("Account Not Found")), oneOfVariant(unknown)))
      .out(statusCode(StatusCode.Created).and(jsonBody[SavedSearchId]))
      .description("Search was successfully saved.")
      .out(header[String](HeaderNames.ContentLocation).description("The location of a content"))
      .tag(tagName)
      .summary("Save Search for a Recruiter")

}

object SavedSearchEndpoints {

  sealed trait Frequency
  final case object Dagelijks extends Frequency
  final case object Wekelijks extends Frequency
  final case object Nooit     extends Frequency
  implicit val frequencyCode: Codec[Frequency] = io.circe.generic.extras.semiauto.deriveEnumerationCodec[Frequency]

  final case class SavedSearch(
    opgeslagenZoekopdrachtId: String,
    accountId: String,
    zoekopdrachtId: String,
    naam: String,
    frequentie: Frequency,
    zoekparameters: SearchParams
  )
  implicit val savedSearchEnc: Encoder[SavedSearch] = deriveEncoder[SavedSearch].mapJson(_.deepDropNullValues)
  implicit val savedSearchDec: Decoder[SavedSearch] = deriveDecoder[SavedSearch]

  final case class SavedSearches(items: Seq[SavedSearch]) extends ApiIndex[SavedSearch](items)
  implicit val savedSearchesEnc: Encoder[SavedSearches] = deriveEncoder[SavedSearches].mapJson(_.deepDropNullValues)
  implicit val savedSearchesDec: Decoder[SavedSearches] = deriveDecoder[SavedSearches]

  final case class SearchParams(
    zoektermen: Option[SearchConditions] = None,
    locatie: Option[String] = None,
    wijzigingsdatum: Option[String] = None,
    opleidingsniveaus: Option[Seq[String]] = None,
    aantallenUren: Option[Seq[String]] = None,
    soortenWerk: Option[Seq[String]] = None,
    beschikbaarheden: Option[Seq[String]] = None,
    rijbewijzen: Option[Seq[String]] = None,
    talen: Option[Seq[String]] = None,
    afstandTotWerklocatie: Option[String] = None,
    carriereniveau: Option[Seq[String]] = None,
    functiegroep: Option[Seq[String]] = None,
    gewenstSalaris: Option[Seq[String]] = None,
    provincies: Option[Seq[String]] = None
  )

  implicit val searchParamsEnc: Encoder[SearchParams] = deriveEncoder[SearchParams].mapJson(_.deepDropNullValues)
  implicit val searchParamsDec: Decoder[SearchParams] = deriveDecoder[SearchParams]

  final case class SearchConditions(
    alles: Option[Seq[String]] = None,
    opleidingNaam: Option[Seq[String]] = None,
    opleidingBeschrijving: Option[Seq[String]] = None,
    gewensteBaan: Option[Seq[String]] = None,
    functieTitel: Option[Seq[String]] = None,
    functieBeschrijving: Option[Seq[String]] = None,
    cursussen: Option[Seq[String]] = None
  )
  implicit val searchConditionsEnc: Encoder[SearchConditions] = deriveEncoder[SearchConditions].mapJson(_.deepDropNullValues)
  implicit val searchConditionsDec: Decoder[SearchConditions] = deriveDecoder[SearchConditions]

  final case class Search(
    naam: String,
    frequentie: Frequency,
    zoekparameters: SearchParams
  )
  implicit val searchEnc: Encoder[Search] = deriveEncoder[Search].mapJson(_.deepDropNullValues)
  implicit val searchDec: Decoder[Search] = deriveDecoder[Search]

  final case class SearchIdInput(recruiterId: String, searchId: String)

  final case class SavedSearchId(id: String)
  implicit val savedSearchIdCodec: Codec[SavedSearchId] = deriveCodec[SavedSearchId]

}
