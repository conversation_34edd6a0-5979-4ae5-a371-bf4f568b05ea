package nl.dpes.core.api.v1

import nl.dpes.core.api.v1.customerservice.{CustomerServiceEndpoints, CustomerServiceLogic}
import nl.dpes.core.api.v1.jobseekers.{JobSeekerEndpoints, JobSeekerLogic}
import nl.dpes.core.api.v1.recruiters.{RecruitersEndpoints, RecruitersLogic}
import nl.dpes.core.api.v1.searches.{SavedSearchEndpoints, SavedSearchLogic}
import nl.dpes.core.api.v1.status.{StatusEndpoints, StatusLogic}
import sttp.tapir.server.pekkohttp.PekkoHttpServerInterpreter
import sttp.tapir.swagger.SwaggerUIOptions
import sttp.tapir.swagger.bundle.SwaggerInterpreter

import scala.concurrent.ExecutionContext.Implicits.global
import scala.concurrent.Future

class Routes(
  prefix: String,
  version: String,
  statusLogic: StatusLogic,
  savedSearchLogic: SavedSearchLogic,
  recruitersLogic: <PERSON>cruitersLogic,
  customerServiceLogic: CustomerServiceLogic,
  jobSeekerLogic: JobSeekerLogic
) {

  private val status          = new StatusEndpoints(prefix, version)
  private val customerService = new CustomerServiceEndpoints(prefix, version)
  private val recruiters      = new RecruitersEndpoints(prefix, version)
  private val jobSeekers      = new JobSeekerEndpoints(prefix, version)
  private val savedSearches   = new SavedSearchEndpoints(prefix, version)

  private val swaggerUIOptions = SwaggerUIOptions.default.copy(pathPrefix = List(prefix, "docs"))

  private val swaggerEndpoints = SwaggerInterpreter(swaggerUIOptions = swaggerUIOptions)
    .fromEndpoints[Future](
      List(
        customerService.createLoginAsToken,
        customerService.getCurrentWorker,
        customerService.createToken,
        recruiters.retrieveSingleRecruiter,
        recruiters.favouritesIndexPaging,
        recruiters.saveFavourite,
        recruiters.deleteFavourite,
        recruiters.migrateToSalesforce,
        recruiters.recruitersIndex,
        recruiters.importRecruiter,
        status.getStatus,
        jobSeekers.findJobSeeker,
        jobSeekers.deleteJobSeeker,
        jobSeekers.getSubscriptions,
        jobSeekers.subscribeToEmail,
        jobSeekers.subscribeToAllEmail,
        jobSeekers.requestChangeEmailAddress,
        jobSeekers.loginState,
        jobSeekers.login,
        jobSeekers.loginAs,
        jobSeekers.resetPassword,
        jobSeekers.replacePassword,
        jobSeekers.setPasswordExternal,
        jobSeekers.setPassword,
        jobSeekers.verify,
        jobSeekers.registerExternalJobSeeker,
        jobSeekers.listJobSeekers,
        jobSeekers.registerJobSeeker,
        savedSearches.getSearch,
        savedSearches.editSearch,
        savedSearches.deleteSearch,
        savedSearches.listSearches,
        savedSearches.saveSearch
      ),
      "Core API",
      "1.0"
    )

  val statusRoute = PekkoHttpServerInterpreter().toRoute(status.getStatus.serverLogic(statusLogic.getStatus))

  val savedSearchRoutes = PekkoHttpServerInterpreter().toRoute(
    List(
      savedSearches.getSearch.serverLogic(savedSearchLogic.getSearch),
      savedSearches.saveSearch.serverLogic(savedSearchLogic.saveSearch),
      savedSearches.editSearch.serverLogic(savedSearchLogic.editSearch),
      savedSearches.deleteSearch.serverLogic(savedSearchLogic.deleteSearch),
      savedSearches.listSearches.serverLogic(savedSearchLogic.listSearches)
    )
  )

  val recruitersRoutes = PekkoHttpServerInterpreter().toRoute(
    List(
      recruiters.saveFavourite.serverLogic(recruitersLogic.saveFavourite),
      recruiters.importRecruiter.serverLogic(recruitersLogic.importRecruiter),
      recruiters.retrieveSingleRecruiter.serverLogic(recruitersLogic.singleRecruiter),
      recruiters.recruitersIndex.serverLogic(recruitersLogic.recruitersIndex),
      recruiters.migrateToSalesforce.serverLogic(recruitersLogic.migrateToSalesforce),
      recruiters.favouritesIndexPaging.serverLogic(recruitersLogic.favouriteList),
      recruiters.deleteFavourite.serverLogic(recruitersLogic.deleteFavourite)
    )
  )

  val customerServiceRoutes = PekkoHttpServerInterpreter().toRoute(
    List(
      customerService.createToken.serverLogic(customerServiceLogic.createToken),
      customerService.getCurrentWorker.serverSecurityLogicPure(Right(_)).serverLogic(customerServiceLogic.getCurrentWorker),
      customerService.createLoginAsToken.serverSecurityLogicPure(Right(_)).serverLogic(customerServiceLogic.createLoginAsToken)
    )
  )

  val jobSeekerRoutes = PekkoHttpServerInterpreter().toRoute(
    List(
      jobSeekers.getSubscriptions.serverSecurityLogicPure(Right(_)).serverLogic(jobSeekerLogic.getSubscriptions),
      jobSeekers.subscribeToEmail.serverSecurityLogicPure(Right(_)).serverLogic(jobSeekerLogic.subscribeToEmail),
      jobSeekers.subscribeToAllEmail.serverSecurityLogicPure(Right(_)).serverLogic(jobSeekerLogic.subscribeToAllEmails),
      jobSeekers.findJobSeeker.serverLogic(jobSeekerLogic.findJobSeeker),
      jobSeekers.deleteJobSeeker.serverSecurityLogicPure(Right(_)).serverLogic(jobSeekerLogic.deleteJobSeeker),
      jobSeekers.requestChangeEmailAddress.serverLogic(jobSeekerLogic.requestEmailChange),
      jobSeekers.loginState.serverSecurityLogicPure(Right(_)).serverLogic(jobSeekerLogic.loginState),
      jobSeekers.login.serverLogic(jobSeekerLogic.login),
      jobSeekers.loginAs.serverLogic(jobSeekerLogic.loginAs),
      jobSeekers.resetPassword.serverLogic(jobSeekerLogic.resetPassword),
      jobSeekers.replacePassword.serverSecurityLogicPure(Right(_)).serverLogic(jobSeekerLogic.replacePassword),
      jobSeekers.setPasswordExternal.serverLogic(jobSeekerLogic.setPasswordExternal),
      jobSeekers.setPassword.serverLogic(jobSeekerLogic.setPassword),
      jobSeekers.verify.serverLogic(jobSeekerLogic.verify),
      jobSeekers.registerExternalJobSeeker.serverLogic(jobSeekerLogic.registerExternalJobSeeker),
      jobSeekers.listJobSeekers.serverLogic(jobSeekerLogic.listJobSeekers),
      jobSeekers.registerJobSeeker.serverLogic(jobSeekerLogic.registerJobSeeker)
    )
  )

  val swagger = PekkoHttpServerInterpreter().toRoute(swaggerEndpoints)
}
