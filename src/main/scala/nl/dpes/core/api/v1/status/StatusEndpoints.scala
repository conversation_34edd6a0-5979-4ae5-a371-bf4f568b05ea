package nl.dpes.core.api.v1.status

import io.circe.Codec
import nl.dpes.core.api.v1.status.StatusEndpoints._
import nl.dpes.core.services.StatusService.License
import sttp.model.StatusCode
import sttp.tapir.Codec.PlainCodec
import sttp.tapir._
import sttp.tapir.generic.auto._
import sttp.tapir.json.circe._
import org.joda.time.DateTime

import scala.concurrent.duration._

class StatusEndpoints(prefix: String, version: String) {

  private val licenses = Set(LicenseInformation("product", "expires", 0))

  private val greenExample  = GreenResponse(licenses = licenses)
  private val orangeExample = OrangeResponse(licenses = licenses)
  private val redExample    = RedResponse(licenses = licenses)

  private val errors = oneOf[ErrorResponse](
    oneOfVariant(
      statusCode(StatusCode(503))
        .and(jsonBody[OrangeResponse].description("Service is unavailable at the moment (Orange)").example(orangeExample))
    ),
    oneOfDefaultVariant(
      statusCode(StatusCode(500)).and(jsonBody[RedResponse].description("Service is in an error state (Red)").example(redExample))
    )
  )

  val getStatus: PublicEndpoint[Unit, ErrorResponse, GreenResponse, Any] =
    endpoint.get
      .in(prefix / version / "status")
      .errorOut(errors)
      .out(jsonBody[GreenResponse].example(greenExample).description("Service Running is OK (Green)"))
      .tag("Status")
      .summary("Retrieve status information")

}

object StatusEndpoints {

  sealed trait Status
  final case object Green  extends Status
  final case object Orange extends Status
  final case object Red    extends Status

  final case class LicenseInformation(product: String, expires: String, daysLeft: Long)

  def licenseToLicenseInformation(license: License): LicenseInformation =
    LicenseInformation(
      license.name,
      license.expires.toString,
      Duration(license.expires.minus(DateTime.now().getMillis).getMillis, MILLISECONDS).toDays
    )

  final case class GreenResponse(status: Status = Green, licenses: Set[LicenseInformation] = Set.empty)

  sealed trait ErrorResponse
  final case class OrangeResponse(status: Status = Orange, licenses: Set[LicenseInformation] = Set.empty) extends ErrorResponse
  final case class RedResponse(status: Status = Red, licenses: Set[LicenseInformation] = Set.empty)       extends ErrorResponse

  implicit val featureCodec: PlainCodec[Status]           = sttp.tapir.Codec.derivedEnumeration[String, Status].defaultStringBased
  implicit val greenResponseCodec: Codec[GreenResponse]   = io.circe.generic.semiauto.deriveCodec[GreenResponse]
  implicit val orangeResponseCodec: Codec[OrangeResponse] = io.circe.generic.semiauto.deriveCodec[OrangeResponse]
  implicit val redResponseCodec: Codec[RedResponse]       = io.circe.generic.semiauto.deriveCodec[RedResponse]
  implicit val lCodec: Codec[LicenseInformation]          = io.circe.generic.semiauto.deriveCodec[LicenseInformation]
  implicit val statusCodec: Codec[Status]                 = io.circe.generic.extras.semiauto.deriveEnumerationCodec[Status]

}
