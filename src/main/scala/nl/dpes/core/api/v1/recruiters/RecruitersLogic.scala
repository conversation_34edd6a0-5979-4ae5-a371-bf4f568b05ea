package nl.dpes.core.api.v1.recruiters

import nl.dpes.core.api.v1.ApiError
import nl.dpes.core.api.v1.recruiters.RecruitersEndpoints._
import nl.dpes.core.api.v1.recruiters.RecruitersLogic._
import nl.dpes.core.domain.Recruiter.{ImporteerNaarSalesforce, MaakFavoriet, VerwijderFavoriet}
import nl.dpes.core.projections.core.CoreRecruiterAccountProjections.CoreRecruiterAccount
import nl.dpes.core.projections.core.{CoreRecruiterAccountProjections, CoreSanDiegoEmployerProjections}
import nl.dpes.core.projections.index.IndexRecruiterFavorietProjections
import nl.dpes.core.projections.index.IndexRecruiterFavorietProjections.RecruiterFavorietDocument
import nl.dpes.core.services.RecruiterIdToSalesforceIdMigrator
import org.axonframework.commandhandling.gateway.CommandGateway
import org.slf4j.Logger

import scala.concurrent.{ExecutionContext, Future}

class RecruitersLogic(
  sanDiegoEmployerProjections: CoreSanDiegoEmployerProjections,
  recruiterAccountProjections: CoreRecruiterAccountProjections,
  commandGateway: CommandGateway,
  logger: Logger,
  salesforceIdMigrator: RecruiterIdToSalesforceIdMigrator,
  indexRecruiterFavorietProjections: IndexRecruiterFavorietProjections,
  executor: ExecutionContext
) {

  private implicit val ec = executor

  def recruitersIndex(sanDiegoId: Option[Int]): Future[Either[ApiError.Forbidden, Recruiters]] = sanDiegoId match {
    case None =>
      Future.successful(
        Left(ApiError.Forbidden("The request was a legal request, but the server is refusing to respond to it.", "SanDiegoId is missing"))
      )
    case Some(id) =>
      val recruiters = sanDiegoEmployerProjections
        .findBySanDiegoId(id)
        .flatMap(found => recruiterAccountProjections.findByRecruiterId(found.recruiterId))
        .toSeq
      Future.successful(Right(Recruiters(recruiters.map(recruiterAccountToRecruiter))))
  }

  def importRecruiter(importData: Import): Future[Either[Unit, RecruiterId]] = {
    val result = recruiterAccountProjections.findByRecruiterId(importData.salesForceId) match {
      case None =>
        logger.info(s"Recruiter ${importData.salesForceId} will be imported")
        commandGateway.send(
          ImporteerNaarSalesforce(
            importData.salesForceId,
            importData.eMailadres,
            importData.site
          )
        )
        Right(RecruiterId(importData.salesForceId))
      case Some(found) => Right(RecruiterId(found.recruiterId))
    }

    Future.successful(result)
  }

  def migrateToSalesforce(migrate: MigrateToSalesforceId): Future[Either[ApiError.NotFound, RecruiterId]] = {
    val result = recruiterAccountProjections.findByRecruiterId(migrate.salesForceId) match {
      case Some(found) => Right(RecruiterId(found.recruiterId))
      case None =>
        migrate.sanDiegoIds.flatMap(sanDiegoEmployerProjections.findBySanDiegoId) match {
          case Nil =>
            Left(notFoundRecruiter(migrate.sanDiegoIds.toString))
          case ids =>
            salesforceIdMigrator.migrate(ids.map(_.recruiterId), migrate.salesForceId)
            Right(RecruiterId(migrate.salesForceId))
        }
    }
    Future.successful(result)
  }

  def saveFavourite(input: (String, String)): Future[Either[ApiError.NotFound, Unit]] = {
    val result = recruiterAccountProjections.findByRecruiterId(input._1) match {
      case None =>
        Left(notFoundRecruiter(input._1))
      case Some(_) =>
        commandGateway.sendAndWait(MaakFavoriet(input._1, input._2))
        Right(())
    }

    Future.successful(result)
  }

  def deleteFavourite(input: (String, String)): Future[Either[ApiError.NotFound, Unit]] = {
    val result = recruiterAccountProjections.findByRecruiterId(input._1) match {
      case None =>
        Left(notFoundRecruiter(input._1))
      case Some(_) =>
        commandGateway.sendAndWait(VerwijderFavoriet(input._1, input._2))
        Right(())
    }

    Future.successful(result)
  }

  def favouriteList(input: (String, Option[Int], Option[Int], Option[JobSeekerIds])): Future[Either[ApiError, FavouritesResponse]] = {
    val page        = input._2.getOrElse(1)
    val limit       = input._3.getOrElse(50)
    val recruiterId = input._1
    val maybeIds    = input._4

    recruiterAccountProjections.findByRecruiterId(input._1) match {
      case None =>
        Future.successful(Left(notFoundRecruiter(recruiterId)))
      case Some(_) =>
        val futureList = maybeIds
          .fold(indexRecruiterFavorietProjections.searchFavoriet(recruiterId, page, limit))(ids =>
            indexRecruiterFavorietProjections.searchFavorietByWerkzoekendeIds(recruiterId, ids.ids)
          )

        futureList
          .map { l =>
            Right(
              FavouritesResponse(
                l.items.map(recruiterFavouriteDocumentToFavourite),
                l.totalCount
              )
            )
          }
          .recover { case e =>
            Left(ApiError.Unknown(e.getMessage))
          }

    }
  }

  def singleRecruiter(recruiterId: String): Future[Either[ApiError.NotFound, Recruiter]] =
    recruiterAccountProjections.findByRecruiterId(recruiterId) match {
      case None =>
        Future.successful(Left(notFoundRecruiter(recruiterId)))
      case Some(found) =>
        Future.successful(Right(Recruiter(found.recruiterId, found.eMailadres, found.site)))
    }

}

object RecruitersLogic {

  def recruiterAccountToRecruiter(account: CoreRecruiterAccount): Recruiter = Recruiter(
    account.recruiterId,
    account.eMailadres,
    account.site
  )

  def recruiterFavouriteDocumentToFavourite(indexRecruiterFavoriet: RecruiterFavorietDocument): Favourite =
    Favourite(indexRecruiterFavoriet.werkzoekendeId)

  def notFoundRecruiter(recruiterId: String): ApiError.NotFound =
    ApiError.NotFound("Recruiter not found.", recruiterId, Some("Recruiter"))
}
