package nl.dpes.core.api.v1

import io.circe.Codec
import io.circe.generic.semiauto._

sealed trait ApiError

object ApiError {

  final case class Unauthorized(who: String, reason: String)                     extends ApiError
  final case class NotFound(message: String, entity: String, id: Option[String]) extends ApiError
  final case class Forbidden(message: String, reason: String)                    extends ApiError
  final case class Conflict(reason: String)                                      extends ApiError
  final case class UnprocessableEntity(entity: String, reason: String)           extends ApiError
  final case class Unknown(message: String)                                      extends ApiError
  final case class BadRequest(details: String)                                   extends ApiError

  implicit val codecNotFound: Codec[NotFound]                 = deriveCodec[NotFound]
  implicit val codecUnauthorized: Codec[Unauthorized]         = deriveCodec[Unauthorized]
  implicit val codecForbidden: Codec[Forbidden]               = deriveCodec[Forbidden]
  implicit val codecUnknown: Codec[Unknown]                   = deriveCodec[Unknown]
  implicit val codecConflict: Codec[Conflict]                 = deriveCodec[Conflict]
  implicit val codecUnprocessable: Codec[UnprocessableEntity] = deriveCodec[UnprocessableEntity]
  implicit val apiErrorCodec: Codec[ApiError]                 = deriveCodec[ApiError]

}
