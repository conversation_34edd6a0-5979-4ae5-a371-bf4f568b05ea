package nl.dpes.core.domain

import akka.http.scaladsl.model.Uri.Query
import akka.http.scaladsl.model.{IllegalUriException, Uri}
import com.fasterxml.jackson.annotation.JsonValue
import nl.dpes.core.domain.exceptions.UrlIsOngeldig

case class Url private (value: Uri) {
  require(!value.scheme.isEmpty)
  require(!value.authority.isEmpty)

  def +(kvp: (String, String)): Url =
    Url(value withQuery (kvp +: value.query()))

  def setQuery(kvp: (String, String)): Url =
    if (value.query().toMap.contains(kvp._1)) {
      Url(value withQuery Query(value.query().toMap.map {
        case (kvp._1, _) => kvp._1 -> kvp._2
        case t           => t
      }))
    } else {
      Url(value withQuery (kvp +: value.query()))
    }

  @JsonValue
  override def toString: String = value.toString
}

object Url {

  def apply(value: String): Url = try new Url(value)
  catch {
    case _: IllegalUriException      => throw UrlIsOngeldig(value)
    case _: IllegalArgumentException => throw UrlIsOngeldig(value)
  }

  implicit def urlToString(url: Url): String   = url.toString
  implicit def stringToUrl(value: String): Url = Url(value)
}
