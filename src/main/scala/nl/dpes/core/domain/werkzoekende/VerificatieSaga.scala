package nl.dpes.core.domain.werkzoekende

import java.time.temporal.ChronoUnit
import java.time.{Clock, Instant}
import nl.dpes.common.conversions._
import nl.dpes.core.domain.Werkzoekende._
import nl.dpes.core.services.MailService
import nl.dpes.core.services.MailService.VerificatieHerinneringsMail
import org.axonframework.commandhandling.gateway.CommandGateway
import org.axonframework.deadline.DeadlineManager
import org.axonframework.deadline.annotation.DeadlineHandler
import org.axonframework.eventhandling.Timestamp
import org.axonframework.modelling.saga.{EndSaga, SagaEventHandler, StartSaga}
import spray.json.JsonFormat

import scala.concurrent.Await
import scala.concurrent.duration._

object VerificatieSaga {
  val verificatieHerinneringGewenst = "verificatie-herinnering-gewenst"
  val verificatiePeriodeVerstreken  = "verificatie-periode-verstreken"

  final case class VerificatieHerinneringGewenst(werkzoekendeId: String)

  final case class VerificatiePeriodeVerstreken(werkzoekendeId: String)

  val reminderFrequency: FiniteDuration          = 48 hours
  val verificationPeriod: FiniteDuration         = 14 days
  val doNotSendReminderInTheLast: FiniteDuration = 24 hours

  import spray.json.RootJsonFormat
  import spray.json.DefaultJsonProtocol._
  import nl.dpes.utils.sprayjson._

  implicit val format: RootJsonFormat[VerificatieSaga] = {
    case class Representation(
      mailScheduleToken: Option[String] = None,
      closeAccountScheduleToken: Option[String] = None,
      remindersSent: Int = 0,
      verificationEndDate: Instant
    )
    object Representation {
      def from(repr: Representation): VerificatieSaga = {
        val saga = new VerificatieSaga()
        saga.mailScheduleToken = repr.mailScheduleToken
        saga.closeAccountScheduleToken = repr.closeAccountScheduleToken
        saga.remindersSent = repr.remindersSent
        saga.verificationEndDate = repr.verificationEndDate
        saga
      }

      def to(saga: VerificatieSaga): Representation = Representation(
        mailScheduleToken = saga.mailScheduleToken,
        closeAccountScheduleToken = saga.closeAccountScheduleToken,
        remindersSent = saga.remindersSent,
        verificationEndDate = saga.verificationEndDate
      )

      implicit val instantFormat                 = implicitly[JsonFormat[Long]].imap(Instant.ofEpochMilli, (_: Instant).toEpochMilli)
      val format: RootJsonFormat[Representation] = jsonFormat4(Representation.apply)
    }

    Representation.format.imap(Representation.from, Representation.to)
  }
}

class VerificatieSaga {

  import VerificatieSaga._

  var mailScheduleToken: Option[String]         = None
  var closeAccountScheduleToken: Option[String] = None
  var remindersSent: Int                        = 0
  var verificationEndDate: Instant              = _

  var eMailadres: String       = _
  var site: String             = _
  var verificatieUrl: String   = _
  var verificatieToken: String = _

  @StartSaga
  @SagaEventHandler(associationProperty = "werkzoekendeId")
  def onGeregistreerd(event: Geregistreerd, deadlineManager: DeadlineManager, clock: Clock, @Timestamp timestamp: Instant): Unit = {
    verificationEndDate = timestamp.plusSeconds(verificationPeriod.toSeconds)

    if (verificationEndDate.isAfter(clock.instant())) {
      mailScheduleToken = Some(
        deadlineManager.schedule(reminderFrequency, verificatieHerinneringGewenst, VerificatieHerinneringGewenst(event.werkzoekendeId))
      )
      closeAccountScheduleToken = Some(
        deadlineManager.schedule(verificationEndDate, verificatiePeriodeVerstreken, VerificatiePeriodeVerstreken(event.werkzoekendeId))
      )
    }

    eMailadres = event.eMailadres
    site = event.site
    verificatieUrl = event.verificatieUrl
    verificatieToken = event.verificatieToken
  }

  @DeadlineHandler(deadlineName = "verificatie-herinnering-gewenst")
  def onVerificatieHerinneringGewenst(
    event: VerificatieHerinneringGewenst,
    deadlineManager: DeadlineManager,
    mailService: MailService,
    clock: Clock
  ): Unit = {
    val now = clock.instant()

    if (now.until(verificationEndDate, ChronoUnit.SECONDS) > doNotSendReminderInTheLast.toSeconds) {
      Await.result(
        mailService.send(
          VerificatieHerinneringsMail(
            eMailadres,
            site,
            verificatieUrl,
            verificatieToken,
            s"${now.until(verificationEndDate, ChronoUnit.DAYS)} dagen"
          )
        ),
        10 seconds
      )

      mailScheduleToken = Some(deadlineManager.schedule(reminderFrequency, verificatieHerinneringGewenst, event))
    }
  }

  @DeadlineHandler(deadlineName = "verificatie-periode-verstreken")
  def onVerificatiePeriodeVerstreken(event: VerificatiePeriodeVerstreken, commandGateway: CommandGateway): Unit =
    commandGateway.sendAndWait[Unit](ZegAccountOp(event.werkzoekendeId, Opzegredenen.VerificatieperiodeVerstreken))

  @EndSaga
  @SagaEventHandler(associationProperty = "werkzoekendeId")
  def onGeverifieerd(event: Geverifieerd, deadlineManager: DeadlineManager): Unit = {
    mailScheduleToken.foreach(token => deadlineManager.cancelSchedule("verificatie-herinnering-gewenst", token))
    closeAccountScheduleToken.foreach(token => deadlineManager.cancelSchedule("verificatie-periode-verstreken", token))
  }

  @EndSaga
  @SagaEventHandler(associationProperty = "werkzoekendeId")
  def onAccountOpgezegd(event: AccountOpgezegd, deadlineManager: DeadlineManager): Unit = {
    mailScheduleToken.foreach(token => deadlineManager.cancelSchedule("verificatie-herinnering-gewenst", token))
    closeAccountScheduleToken.foreach(token => deadlineManager.cancelSchedule("verificatie-periode-verstreken", token))
  }
}
