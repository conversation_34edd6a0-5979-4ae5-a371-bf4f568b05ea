package nl.dpes.core.domain.werkzoekende

import nl.dpes.core.domain.exceptions.EMailinschrijvingWordtNietOndersteund

import scala.util.{Failure, Success, Try}

object EMailinschrijvingen extends Enumeration {
  type EMailinschrijving = Value
  val Persoonlijk: EMailinschrijving = Value("personal")
  val Nieuwsbrief: EMailinschrijving = Value("newsletter")
  val Partner: EMailinschrijving     = Value("partner")

  lazy val Alle: List[EMailinschrijving] = List(Persoonlijk, Nieuwsbrief, Partner)

  implicit def stringToEMailinschrijving(string: String): EMailinschrijving = Try(withName(string)) match {
    case Success(eMailinschrijving) => eMailinschrijving
    case Failure(_)                 => throw EMailinschrijvingWordtNietOndersteund(string)
  }

  implicit def eMailinschrijvingToString(eMailinschrijving: EMailinschrijving): String = eMailinschrijving.toString
}
