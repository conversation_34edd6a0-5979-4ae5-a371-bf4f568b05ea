package nl.dpes.core.domain.werkzoekende

import nl.dpes.core.api.v1.jobseekers.JobSeekerEndpoints.Registered
import nl.dpes.core.domain.Rollen
import nl.dpes.core.domain.Werkzoekende.{RegisterExternal, Registreer}
import nl.dpes.core.domain.exceptions.{DomainException, EMailadresIsAlInGebruik}
import nl.dpes.core.projections.core.CoreWerkzoekendeAccountProjections
import nl.dpes.core.services.security.tokens.{AccessToken, VerificationToken}
import nl.dpes.core.services.{IdentifierService, TokenService}
import org.axonframework.commandhandling.gateway.CommandGateway

import scala.concurrent.{ExecutionContext, Future}

class RegisterWerkzoekendeHandler(
  commandGateway: CommandGateway,
  identifierService: IdentifierService,
  tokenService: TokenService,
  werkzoekendeAccountProjections: CoreWerkzoekendeAccountProjections
)(implicit
  ec: ExecutionContext
) {

  def registerWerkzoekende(
    eMailadres: String,
    site: String,
    verificationUrl: String
  ): Future[Either[DomainException, Registered]] =
    for {
      emailExists <- Future { // the Future wrapper also catches exceptions, resulting in a Failure
        werkzoekendeAccountProjections.findByEMailadres(eMailadres, site)
      }
      registration <- emailExists match {
        case Some(value) =>
          Future.successful(Left(EMailadresIsAlInGebruik(eMailadres)))
        case None =>
          getIdFromJssOrGenerate() map ((werkzoekendeId: String) => {
            val verificationToken = tokenService.generate(VerificationToken(werkzoekendeId, Rollen.Werkzoekende))

            commandGateway.sendAndWait[Unit](
              Registreer(werkzoekendeId, eMailadres, site, verificationUrl, verificationToken)
            )
            val accessToken = tokenService.generate(
              AccessToken(werkzoekendeId, None, Rollen.Werkzoekende, Some(eMailadres), Some(site))
            )
            Right(Registered(werkzoekendeId, accessToken))
          })
      }
    } yield registration

  def registerExternalWerkzoekende(
    id: String,
    eMailadres: String,
    site: String
  ): Future[Either[DomainException, Registered]] =
    for {
      emailExists <- Future { // the Future wrapper also catches exceptions, resulting in a Failure
        werkzoekendeAccountProjections.findByEMailadres(eMailadres, site)
      }
      registration <- emailExists match {
        case Some(value) =>
          Future.successful(Left(EMailadresIsAlInGebruik(eMailadres)))
        case None =>
          Future {
            commandGateway.sendAndWait[Unit](
              RegisterExternal(id, eMailadres, site)
            )
            val accessToken = tokenService.generate(
              AccessToken(id, None, Rollen.Werkzoekende, Some(eMailadres), Some(site))
            )
            Right(Registered(id, accessToken))
          }
      }
    } yield registration

  private def getIdFromJssOrGenerate(): Future[String] = Future.successful(identifierService.generateUUID())
}
