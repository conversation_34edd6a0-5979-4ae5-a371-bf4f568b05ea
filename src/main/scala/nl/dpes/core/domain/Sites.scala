package nl.dpes.core.domain

import nl.dpes.core.domain.exceptions.SiteWordtNietOndersteund

import scala.util.{Failure, Success, Try}

object Sites extends Enumeration {
  type Site = Value
  val Iol: Site = Value("intermediair.nl")
  val Itb: Site = Value("itbanen.nl")
  val Nvb: Site = Value("nationalevacaturebank.nl")
  val Ndp: Site = Value("persgroepemploymentsolutions.nl")

  lazy val Alle: List[Site] = List(Iol, Itb, Nvb, Ndp)

  implicit def stringToSite(siteName: String): Site = Try(withName(siteName)) match {
    case Success(site) => site
    case Failure(_)    => throw SiteWordtNietOndersteund(siteName)
  }

  implicit def siteToString(site: Site): String = site.toString
}
