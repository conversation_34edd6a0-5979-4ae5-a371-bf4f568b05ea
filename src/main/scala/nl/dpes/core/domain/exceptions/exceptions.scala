package nl.dpes.core.domain

import nl.dpes.core.services.security.PasswordPolicy.PasswordValidationError

package object exceptions {
  sealed abstract class DomainException(message: String) extends RuntimeException(message)

  case class EMailadresIsOngeldig(eMailadres: String) extends DomainException(s"The email address is not valid: $eMailadres")
  case class SiteWordtNietOndersteund(site: String)   extends DomainException(s"Site is not supported: $site")
  case class RolWordtNietOndersteund(rol: String)     extends DomainException(s"Role is not supported: $rol")

  case class EMailinschrijvingWordtNietOndersteund(subscription: String)
      extends DomainException(s"Subscription is not supported: $subscription")
  case class UrlIsOngeldig(url: String) extends DomainException(s"URL is not a valid url: $url")

  case class OngeldigeFrequentieVoorZoekopdracht(frequentie: String)
      extends DomainException(s"Frequency is invalid for search: $frequentie")

  case class WachtwoordVoldoetNietAanEisen(fout: PasswordValidationError)
      extends DomainException(s"Password does not respect policy: $fout")
  case class CredentialsOngeldig() extends DomainException(s"Provided credentials are invalid")

  case class EMailadresIsAlInGebruik(eMailadres: String) extends DomainException(s"The email address is already in use: $eMailadres")
  case class RecruiterIdIsInGebruik(recruiterId: String) extends DomainException(s"The recruiter is already in use: $recruiterId")

  case class AccountNietGevonden(accountId: String, accountType: String)
      extends DomainException(s"Account not found: $accountType, $accountId")
  case class EMailadresIsNietGeverifieerd(eMailadres: String) extends DomainException(s"The email address is not yet verified: $eMailadres")

  case class WachtwoordTokenOngeldig(werkzoekendeId: String)
      extends DomainException(s"Password token is not valid for account: $werkzoekendeId")
  case class WachtwoordAlIngesteld(werkzoekendeId: String)     extends DomainException(s"Password is already set for account: $werkzoekendeId")
  case class WachtwoordKomtNietOvereen(werkzoekendeId: String) extends DomainException(s"Password does not match: $werkzoekendeId")
  case class EMailadresNietGevonden(eMailadres: String)        extends DomainException(s"The email address was not found: $eMailadres")

  case class WijzigEMailadresTokenOngeldig(werkzoekendeId: String)
      extends DomainException(s"Email token is not valid for account: $werkzoekendeId")
}
