package nl.dpes.core.domain

import nl.dpes.axon4s.annotations.targetAggregateIdentifier
import nl.dpes.core.config.components.LoggingComponent
import nl.dpes.core.domain.Sites.Site
import nl.dpes.core.domain.Werkzoekende._
import nl.dpes.core.domain.exceptions._
import nl.dpes.core.domain.werkzoekende.EMailinschrijvingen
import nl.dpes.core.domain.werkzoekende.EMailinschrijvingen.EMailinschrijving
import nl.dpes.core.projections.core.CoreWerkzoekendeAccountProjections
import nl.dpes.core.services.PasswordService
import io.axoniq.gdpr.api.{dataSubjectId, personalData}
import org.axonframework.commandhandling.CommandHandler
import org.axonframework.modelling.command.{AggregateIdentifier, AggregateRoot}
import org.axonframework.modelling.command.AggregateLifecycle.apply
import org.axonframework.eventsourcing.EventSourcingHandler
import org.axonframework.serialization.Revision

object Werkzoekende {

  sealed trait WerkzoekendeEvent extends Event {
    val werkzoekendeId: String

    def getWerkzoekendeId: String = werkzoekendeId
  }

  object Opzegredenen extends Enumeration {
    type Opzegreden = Value
    val VerificatieperiodeVerstreken: Opzegreden = Value("verificatieperiode verstreken")
    val Overig: Opzegreden                       = Value("overig")

    implicit def opzegredenToString(v: Opzegreden): String = v.toString
  }

  final case class ImporteerAccount(
    @targetAggregateIdentifier werkzoekendeId: String,
    sanDiegoId: Int,
    eMailadres: EMailadres,
    site: Site,
    wachtwoord: Wachtwoord
  )

  @Revision("1.1")
  final case class AccountGeimporteerd(
    @dataSubjectId werkzoekendeId: String,
    sanDiegoId: Int,
    @personalData(replacement = "*randomEmailAddress*") eMailadres: String,
    site: Site,
    wachtwoord: Wachtwoord
  ) extends WerkzoekendeEvent

  final case class Registreer(
    @targetAggregateIdentifier werkzoekendeId: String,
    eMailadres: EMailadres,
    site: Site,
    verificatieUrl: Url,
    verificatieToken: String
  )

  @Revision("1.2")
  final case class Geregistreerd(
    @dataSubjectId werkzoekendeId: String,
    @personalData(replacement = "*randomEmailAddress*") eMailadres: String,
    site: Site,
    verificatieUrl: String,
    verificatieToken: String
  ) extends WerkzoekendeEvent

  final case class RegisterExternal(
    @targetAggregateIdentifier werkzoekendeId: String,
    eMailadres: EMailadres,
    site: Site
  )

  final case class GeregistreerdExternal(
    @dataSubjectId werkzoekendeId: String,
    @personalData(replacement = "*randomEmailAddress*") eMailadres: String,
    site: Site
  ) extends WerkzoekendeEvent

  final case class Verifieer(@targetAggregateIdentifier werkzoekendeId: String)

  final case class Geverifieerd(@dataSubjectId werkzoekendeId: String) extends WerkzoekendeEvent

  final case class StelWachtwoordIn(@targetAggregateIdentifier werkzoekendeId: String, wachtwoord: Wachtwoord)

  final case class WachtwoordIngesteld(@dataSubjectId werkzoekendeId: String, wachtwoord: Wachtwoord) extends WerkzoekendeEvent

  final case class StelWachtwoordInVoorExternalWerkzoekende(@targetAggregateIdentifier werkzoekendeId: String, wachtwoord: Wachtwoord)

  final case class WachtwoordIngesteldVoorExternalWerkzoekende(@targetAggregateIdentifier werkzoekendeId: String, wachtwoord: Wachtwoord)
      extends WerkzoekendeEvent

  final case class WijzigWachtwoord(@targetAggregateIdentifier werkzoekendeId: String, huidigWachtwoord: String, nieuwWachtwoord: String)

  final case class WachtwoordGewijzigd(@dataSubjectId werkzoekendeId: String, nieuwWachtwoord: Wachtwoord) extends WerkzoekendeEvent

  final case class VergeetWachtwoord(
    @targetAggregateIdentifier werkzoekendeId: String,
    eMailadres: EMailadres,
    site: Site,
    herstelUrl: Url,
    herstelToken: String,
    herstelTokenId: String,
    customTemplate: Option[String]
  )

  @Revision("1.2")
  final case class WachtwoordVergeten(
    @dataSubjectId werkzoekendeId: String,
    @personalData(replacement = "*randomEmailAddress*") eMailadres: String,
    site: Site,
    herstelUrl: String,
    herstelToken: String,
    herstelTokenId: String,
    customTemplate: Option[String] = None
  ) extends WerkzoekendeEvent

  final case class StelWachtwoordOpnieuwIn(
    @targetAggregateIdentifier werkzoekendeId: String,
    wachtwoord: Wachtwoord,
    herstelTokenId: String
  )

  final case class WachtwoordOpnieuwIngesteld(@dataSubjectId werkzoekendeId: String, wachtwoord: Wachtwoord) extends WerkzoekendeEvent

  final case class VerzoekEMailadresWijziging(
    @targetAggregateIdentifier werkzoekendeId: String,
    nieuwEMailadres: EMailadres,
    verificatieUrl: Url,
    verificatieToken: String,
    verificatieTokenId: String
  )

  @Revision("1.1")
  final case class EMailadresWijzigingVerzocht(
    @dataSubjectId werkzoekendeId: String,
    site: Site,
    @personalData(replacement = "*randomEmailAddress*") nieuwEMailadres: String,
    verificatieUrl: String,
    verificatieToken: String,
    verificatieTokenId: String
  ) extends WerkzoekendeEvent

  final case class BevestigEMailadresWijziging(@targetAggregateIdentifier werkzoekendeId: String, verificatieTokenId: String)

  @Revision("1.1")
  final case class EMailadresGewijzigd(
    @dataSubjectId werkzoekendeId: String,
    @personalData(replacement = "*randomEmailAddress*") nieuwEMailadres: String
  ) extends WerkzoekendeEvent

  final case class SchrijfInVoorAlleEMails(@targetAggregateIdentifier werkzoekendeId: String)

  final case class SchrijfInVoorEMail(@targetAggregateIdentifier werkzoekendeId: String, eMailinschrijving: String)

  final case class IngeschrevenVoorEMail(@dataSubjectId werkzoekendeId: String, eMailinschrijving: String, site: String)
      extends WerkzoekendeEvent

  final case class UitschrijfVoorEmail(@targetAggregateIdentifier werkzoekendeId: String, eMailinschrijving: String)

  final case class UitgeschrevenVoorEMail(@dataSubjectId werkzoekendeId: String, eMailinschrijving: String, site: String)
      extends WerkzoekendeEvent

  final case class OpzeggingVerzocht(@dataSubjectId werkzoekendeId: String, sanDiegoId: Int, reden: String) extends WerkzoekendeEvent

  final case class ZegAccountOp(@targetAggregateIdentifier werkzoekendeId: String, reden: String)

  final case class AccountOpgezegd(@dataSubjectId werkzoekendeId: String, reden: String) extends WerkzoekendeEvent

  final case class EMailadresWijziging(nieuwEMailadres: String, verificatieTokenId: String)

}

@AggregateRoot
class Werkzoekende extends LoggingComponent {

  @AggregateIdentifier
  private var id: String                                                     = _
  private var geverifieerd                                                   = false
  private var opgezegd: Boolean                                              = false
  private var wachtwoordIngesteld: Boolean                                   = false
  private var maybeHerstelWachtwoordTokenId: Option[String]                  = None
  private var maybeVerzochteEMailadresWijziging: Option[EMailadresWijziging] = None
  private var site: Site                                                     = _
  private var eMailadres: String                                             = _
  private var maybeWachtwoord: Option[Wachtwoord]                            = None
  private var eMailinschrijvingen: Seq[EMailinschrijving]                    = Seq()

  @CommandHandler
  def this(command: ImporteerAccount) = {
    this

    apply(
      Werkzoekende.AccountGeimporteerd(command.werkzoekendeId, command.sanDiegoId, command.eMailadres, command.site, command.wachtwoord)
    )
  }

  @EventSourcingHandler
  def onAccountGeimporteerd(event: AccountGeimporteerd): Unit = {
    id = event.werkzoekendeId
    wachtwoordIngesteld = !event.wachtwoord.isLegacy
    geverifieerd = true
    site = event.site
    eMailadres = event.eMailadres
    maybeWachtwoord = Some(event.wachtwoord)
  }

  @CommandHandler
  def this(command: Registreer) = {
    this

    apply(
      Werkzoekende.Geregistreerd(
        command.werkzoekendeId,
        command.eMailadres,
        command.site,
        command.verificatieUrl,
        command.verificatieToken
      )
    )
  }

  @EventSourcingHandler
  def onGeregistreerd(event: Geregistreerd): Unit = {
    id = event.werkzoekendeId
    site = event.site
    eMailadres = event.eMailadres
  }

  @CommandHandler
  def this(command: RegisterExternal) = {
    this

    apply(
      Werkzoekende.GeregistreerdExternal(
        command.werkzoekendeId,
        command.eMailadres,
        command.site
      )
    )
  }

  @EventSourcingHandler
  def onGeregistreerdExternal(event: GeregistreerdExternal): Unit = {
    id = event.werkzoekendeId
    site = event.site
    eMailadres = event.eMailadres
  }

  @CommandHandler
  def verifieer(command: Verifieer): Unit =
    if (!geverifieerd) {
      apply(Werkzoekende.Geverifieerd(command.werkzoekendeId))
    }

  @EventSourcingHandler
  def onGeverifieerd(event: Geverifieerd): Unit =
    geverifieerd = true

  @CommandHandler
  def stelWachtwoordIn(command: StelWachtwoordIn): Unit = {
    if (!geverifieerd) {
      throw EMailadresIsNietGeverifieerd(command.werkzoekendeId)
    } else if (wachtwoordIngesteld) {
      throw WachtwoordAlIngesteld(command.werkzoekendeId)
    }

    apply(Werkzoekende.WachtwoordIngesteld(command.werkzoekendeId, command.wachtwoord))
  }

  @EventSourcingHandler
  def onWachtwoordIngesteld(event: WachtwoordIngesteld): Unit = {
    wachtwoordIngesteld = true
    maybeWachtwoord = Some(event.wachtwoord)
  }

  @CommandHandler
  def stelWachtwoordInVoorExternalWerkzoekende(command: StelWachtwoordInVoorExternalWerkzoekende): Unit = {
    if (wachtwoordIngesteld) {
      throw WachtwoordAlIngesteld(command.werkzoekendeId)
    }

    apply(Werkzoekende.WachtwoordIngesteldVoorExternalWerkzoekende(command.werkzoekendeId, command.wachtwoord))
  }

  @EventSourcingHandler
  def onWachtwoordIngesteldVoorExternalWerkzoekende(event: WachtwoordIngesteldVoorExternalWerkzoekende): Unit = {
    wachtwoordIngesteld = true
    maybeWachtwoord = Some(event.wachtwoord)
  }

  @CommandHandler
  def wijzigWachtwoord(command: WijzigWachtwoord, passwordService: PasswordService): Either[WachtwoordKomtNietOvereen, Unit] = {
    val appliedCommand = for {
      password <- maybeWachtwoord
      if passwordService.verifyHashedPassword(password.hash, password.salt, command.huidigWachtwoord)
    } yield apply(WachtwoordGewijzigd(command.werkzoekendeId, passwordService.hashPassword(command.nieuwWachtwoord)))

    appliedCommand.map(_ => Right(())).getOrElse(Left(WachtwoordKomtNietOvereen(command.werkzoekendeId)))
  }

  @EventSourcingHandler
  def onWachtwoordGewijzigd(event: WachtwoordGewijzigd): Unit =
    maybeWachtwoord = Some(event.nieuwWachtwoord)

  @CommandHandler
  def vergeetWachtwoord(command: VergeetWachtwoord): Unit =
    apply(
      Werkzoekende.WachtwoordVergeten(
        command.werkzoekendeId,
        command.eMailadres,
        command.site,
        command.herstelUrl,
        command.herstelToken,
        command.herstelTokenId,
        command.customTemplate
      )
    )

  @EventSourcingHandler
  def onWachtwoordVergeten(event: WachtwoordVergeten): Unit =
    maybeHerstelWachtwoordTokenId = Some(event.herstelTokenId)

  @CommandHandler
  def stelWachtwoordOpnieuwIn(command: StelWachtwoordOpnieuwIn): Unit = {
    verifieer(Verifieer(command.werkzoekendeId))

    if (!wachtwoordIngesteld) {
      apply(Werkzoekende.WachtwoordIngesteld(command.werkzoekendeId, command.wachtwoord))
    } else {
      maybeHerstelWachtwoordTokenId match {
        case Some(tokenId) if command.herstelTokenId == tokenId =>
          apply(Werkzoekende.WachtwoordOpnieuwIngesteld(command.werkzoekendeId, command.wachtwoord))
        case _ =>
          throw WachtwoordTokenOngeldig(command.werkzoekendeId)
      }
    }
  }

  @EventSourcingHandler
  def onWachtwoordOpnieuwIngesteld(event: WachtwoordOpnieuwIngesteld): Unit = {
    wachtwoordIngesteld = true
    maybeHerstelWachtwoordTokenId = None
    maybeWachtwoord = Some(event.wachtwoord)
  }

  @CommandHandler
  def verzoekEMailadresWijziging(
    command: VerzoekEMailadresWijziging,
    werkzoekendeAccountProjections: CoreWerkzoekendeAccountProjections
  ): Unit =
    if (werkzoekendeAccountProjections.eMailadresAvailable(command.nieuwEMailadres, site)) {
      apply(
        EMailadresWijzigingVerzocht(
          command.werkzoekendeId,
          site,
          command.nieuwEMailadres,
          command.verificatieUrl,
          command.verificatieToken,
          command.verificatieTokenId
        )
      )
    } else {
      throw EMailadresIsAlInGebruik(command.nieuwEMailadres)
    }

  @EventSourcingHandler
  def onEMailadresWijzigingVerzocht(event: EMailadresWijzigingVerzocht): Unit =
    maybeVerzochteEMailadresWijziging = Some(EMailadresWijziging(event.nieuwEMailadres, event.verificatieTokenId))

  @CommandHandler
  def bevestigEMailadresWijziging(
    command: BevestigEMailadresWijziging,
    werkzoekendeAccountProjections: CoreWerkzoekendeAccountProjections
  ): Unit =
    maybeVerzochteEMailadresWijziging match {
      case Some(EMailadresWijziging(nieuwEMailadres, token)) if token == command.verificatieTokenId =>
        if (!werkzoekendeAccountProjections.eMailadresAvailable(nieuwEMailadres, site)) {
          throw EMailadresIsAlInGebruik(nieuwEMailadres)
        }
        apply(Werkzoekende.EMailadresGewijzigd(command.werkzoekendeId, nieuwEMailadres))
      case _ =>
        throw WijzigEMailadresTokenOngeldig(command.werkzoekendeId)
    }

  @EventSourcingHandler
  def onEMailadresGewijzigd(event: EMailadresGewijzigd): Unit = {
    eMailadres = event.nieuwEMailadres
    maybeVerzochteEMailadresWijziging = None
  }

  @CommandHandler
  def schrijfInVoorAlleEMails(command: SchrijfInVoorAlleEMails): Unit =
    EMailinschrijvingen.Alle
      .filter(eMailinschrijving => !eMailinschrijvingen.contains(eMailinschrijving))
      .map(eMailinschrijving => IngeschrevenVoorEMail(id, eMailinschrijving, site))
      .foreach(apply(_))

  @CommandHandler
  def schrijfInVoorEMail(command: SchrijfInVoorEMail): Unit =
    if (!eMailinschrijvingen.exists(inschrijving => inschrijving.toString.equals(command.eMailinschrijving)))
      apply(IngeschrevenVoorEMail(id, command.eMailinschrijving, site))

  @EventSourcingHandler
  def onIngeschrevenVoorEMail(event: IngeschrevenVoorEMail): Unit =
    eMailinschrijvingen = eMailinschrijvingen :+ EMailinschrijvingen.stringToEMailinschrijving(event.eMailinschrijving)

  @CommandHandler
  def schrijfUitVoorEMail(command: UitschrijfVoorEmail): Unit =
    if (eMailinschrijvingen.exists(inschrijving => inschrijving.toString.equals(command.eMailinschrijving)))
      apply(UitgeschrevenVoorEMail(id, command.eMailinschrijving, site))

  @EventSourcingHandler
  def onUitgeschrevenVoorEMail(event: UitgeschrevenVoorEMail): Unit =
    eMailinschrijvingen = eMailinschrijvingen.filter(inschrijving =>
      !inschrijving.equals(EMailinschrijvingen.stringToEMailinschrijving(event.eMailinschrijving))
    )

  @EventSourcingHandler
  def onOpzeggingVerzocht(event: OpzeggingVerzocht): Unit =
    id = event.werkzoekendeId

  @CommandHandler
  def zegAccountOp(command: ZegAccountOp): Unit = {
    if (opgezegd) {
      logger.warn(s"Account ${command.werkzoekendeId} was already terminated")
    }
    apply(Werkzoekende.AccountOpgezegd(command.werkzoekendeId, command.reden))
  }

  @EventSourcingHandler
  def onAccountOpgezegd(event: AccountOpgezegd): Unit = {
    logger.info(s"Terminating account $id (aggregate)")
    opgezegd = true
  }
}
