package nl.dpes.core.domain

import io.axoniq.gdpr.api.{dataSubjectId, deepPersonalData}
import nl.dpes.axon4s.annotations.targetAggregateIdentifier
import nl.dpes.core.domain.Recruiter._
import nl.dpes.core.domain.Sites.Site
import nl.dpes.core.domain.exceptions.{EMailadresIsAlInGebruik, RecruiterIdIsInGebruik}
import nl.dpes.core.projections.core.CoreRecruiterAccountProjections
import org.axonframework.commandhandling.CommandHandler
import org.axonframework.modelling.command.AggregateLifecycle.apply
import org.axonframework.modelling.command.{AggregateIdentifier, AggregateLifecycle, AggregateRoot}
import org.axonframework.eventsourcing.EventSourcingHandler

object Recruiter {

  sealed trait RecruiterEvent extends Event {
    val recruiterId: String
  }

  protected[domain] trait RecruiterFavorietEvent extends Event {
    val recruiterId: String
    val werkzoekendeId: String

    def getFavorietId: String = s"${recruiterId}_$werkzoekendeId"
  }

  final case class Importeer(
    @targetAggregateIdentifier recruiterId: String,
    sanDiegoId: Int,
    eMailadres: EMailadres,
    site: Site
  )

  final case class Registreer(@targetAggregateIdentifier recruiterId: String, eMailadres: EMailadres, site: Site)

  final case class ImporteerNaarSalesforce(
    @targetAggregateIdentifier recruiterId: String,
    eMailadres: EMailadres,
    site: Site
  )

  final case class Geimporteerd(
    @dataSubjectId recruiterId: String,
    sanDiegoId: Int,
    @deepPersonalData eMailadres: EMailadres,
    site: Site
  ) extends RecruiterEvent

  final case class Geregistreerd(
    @dataSubjectId recruiterId: String,
    @deepPersonalData eMailadres: EMailadres,
    site: Site
  ) extends RecruiterEvent

  final case class WijzigEMailadres(
    @targetAggregateIdentifier recruiterId: String,
    eMailadres: EMailadres
  )

  final case class EMailadresGewijzigd(
    @dataSubjectId recruiterId: String,
    @deepPersonalData eMailadres: EMailadres
  ) extends RecruiterEvent

  final case class MaakFavoriet(@targetAggregateIdentifier recruiterId: String, werkzoekendeId: String)

  final case class FavorietGemaakt(recruiterId: String, werkzoekendeId: String) extends RecruiterFavorietEvent with RecruiterEvent

  final case class VerwijderFavoriet(@targetAggregateIdentifier recruiterId: String, werkzoekendeId: String)

  final case class FavorietVerwijderd(recruiterId: String, werkzoekendeId: String) extends RecruiterFavorietEvent with RecruiterEvent

  final case class Verwijder(@targetAggregateIdentifier recruiterId: String)

  final case class Verwijderd(recruiterId: String) extends RecruiterEvent

  final case class Favoriet(werkzoekendeId: String, recruiterId: String, tags: Seq[String])
}

@AggregateRoot
class Recruiter {

  @AggregateIdentifier
  private var id: String                        = _
  private var eMailadres: EMailadres            = _
  private var site: Sites.Site                  = _
  private var verwijderd: Boolean               = false
  private var favorieten: Map[String, Favoriet] = Map.empty

  @CommandHandler
  def this(command: Recruiter.Importeer, recruiterAccountProjections: CoreRecruiterAccountProjections) = {
    this

    if (recruiterAccountProjections.isEMailadresAvailable(command.eMailadres, command.site)) {
      apply(Recruiter.Geimporteerd(command.recruiterId, command.sanDiegoId, command.eMailadres, command.site))
    } else {
      throw EMailadresIsAlInGebruik(command.eMailadres)
    }
  }

  @CommandHandler
  def this(command: Recruiter.ImporteerNaarSalesforce, recruiterAccountProjections: CoreRecruiterAccountProjections) = {
    this

    recruiterAccountProjections.findByRecruiterId(command.recruiterId) match {
      case None    => apply(Recruiter.Geregistreerd(command.recruiterId, command.eMailadres, command.site))
      case Some(_) => throw RecruiterIdIsInGebruik(command.recruiterId)
    }
  }

  @CommandHandler
  def this(command: Recruiter.Registreer, recruiterAccountProjections: CoreRecruiterAccountProjections) = {
    this

    if (recruiterAccountProjections.findByRecruiterId(command.recruiterId).isEmpty)
      apply(Recruiter.Geregistreerd(command.recruiterId, command.eMailadres, command.site))
  }

  @EventSourcingHandler
  def onGeimporteerd(event: Recruiter.Geimporteerd): Unit = {
    this.id = event.recruiterId
    this.eMailadres = event.eMailadres
    this.site = event.site
  }

  @EventSourcingHandler
  def onGeregistreerd(event: Recruiter.Geregistreerd): Unit = {
    this.id = event.recruiterId
    this.eMailadres = event.eMailadres
    this.site = event.site
  }

  @CommandHandler
  def wijzigEMailadres(command: WijzigEMailadres, recruiterAccountProjections: CoreRecruiterAccountProjections): Unit =
    if (this.eMailadres != command.eMailadres) {
      if (recruiterAccountProjections.isEMailadresAvailable(command.eMailadres, this.site)) {
        apply(Recruiter.EMailadresGewijzigd(command.recruiterId, command.eMailadres))
      } else {
        throw EMailadresIsAlInGebruik(command.eMailadres)
      }
    }

  @EventSourcingHandler
  def onEMailadresGewijzigd(event: EMailadresGewijzigd): Unit =
    this.eMailadres = event.eMailadres

  @CommandHandler
  def maakFavoriet(command: MaakFavoriet): Unit =
    if (!favorieten.contains(command.werkzoekendeId)) {
      apply(FavorietGemaakt(command.recruiterId, command.werkzoekendeId))
    }

  @EventSourcingHandler
  def onFavorietGemaakt(event: FavorietGemaakt): Unit =
    favorieten = favorieten + (event.werkzoekendeId -> Favoriet(event.werkzoekendeId, event.recruiterId, Seq.empty))

  @CommandHandler
  def verwijderFavoriet(command: VerwijderFavoriet): Unit =
    if (favorieten.contains(command.werkzoekendeId)) {
      apply(FavorietVerwijderd(command.recruiterId, command.werkzoekendeId))
    }

  @EventSourcingHandler
  def onFavorietVerwijderd(event: FavorietVerwijderd): Unit =
    favorieten = favorieten - event.werkzoekendeId

  @CommandHandler
  def verwijder(command: Verwijder): Unit =
    if (!verwijderd) {
      apply(Verwijderd(id))
    }

  @EventSourcingHandler
  def onVerwijderd(event: Verwijderd): Unit = {
    verwijderd = true
    AggregateLifecycle.markDeleted()
  }
}
