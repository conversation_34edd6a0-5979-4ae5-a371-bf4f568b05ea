package nl.dpes.core.domain

import nl.dpes.core.domain.Recruiter.{FavorietGemaakt, FavorietVerwijderd, VerwijderFavoriet}
import nl.dpes.core.domain.Werkzoekende.AccountOpgezegd
import org.axonframework.commandhandling.gateway.CommandGateway
import org.axonframework.modelling.saga.{EndSaga, SagaEventHandler, SagaLifecycle, StartSaga}

class RecruiterFavorietSaga {
  private var recruiterId: String    = _
  private var werkzoekendeId: String = _

  @StartSaga
  @SagaEventHandler(associationProperty = "favorietId")
  def onFavorietGemaakt(event: FavorietGemaakt): Unit = {
    SagaLifecycle.associateWith("recruiterId", event.recruiterId)
    SagaLifecycle.associateWith("werkzoekendeId", event.werkzoekendeId)

    recruiterId = event.recruiterId
    werkzoekendeId = event.werkzoekendeId
  }

  @EndSaga
  @SagaEventHandler(associationProperty = "favorietId")
  def onFavorietVerwijderd(event: FavorietVerwijderd): Unit = ()

  @SagaEventHandler(associationProperty = "werkzoekendeId")
  def onAccountOpgezegd(event: AccountOpgezegd, commandGateway: CommandGateway): Unit =
    commandGateway.sendAndWait(VerwijderFavoriet(recruiterId, werkzoekendeId))

  @EndSaga
  @SagaEventHandler(associationProperty = "recruiterId")
  def onRecruiterVerwijderd(event: Recruiter.Verwijderd): Unit = {}
}

object RecruiterFavorietSaga {

  import spray.json.RootJsonFormat
  import spray.json.DefaultJsonProtocol._
  import nl.dpes.utils.sprayjson._

  implicit val format: RootJsonFormat[RecruiterFavorietSaga] = {
    case class Representation(recruiterId: String, werkzoekendeId: String)
    object Representation {
      def from(repr: Representation): RecruiterFavorietSaga = {
        val saga = new RecruiterFavorietSaga()
        saga.recruiterId = repr.recruiterId
        saga.werkzoekendeId = repr.werkzoekendeId
        saga
      }

      def to(saga: RecruiterFavorietSaga): Representation =
        Representation(recruiterId = saga.recruiterId, werkzoekendeId = saga.werkzoekendeId)

      val format: RootJsonFormat[Representation] = jsonFormat2(Representation.apply)
    }

    Representation.format.imap(Representation.from, Representation.to)
  }
}
