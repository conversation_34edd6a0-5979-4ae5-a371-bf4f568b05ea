package nl.dpes.core.domain

import com.fasterxml.jackson.annotation.JsonValue
import io.axoniq.gdpr.api.{personalData, personalDataType}
import nl.dpes.core.domain.exceptions.EMailadresIsOngeldig

import scala.util.{Failure, Success, Try}
import scala.util.matching.Regex

@personalDataType
case class EMailadres private (@personalData(replacement = "*randomEmailAddress*") value: String) {
  import EMailadres._
  Try(require(validate(value))) match {
    case Success(email) => email
    case Failure(_)     => throw EMailadresIsOngeldig(value)
  }

  @JsonValue
  override def toString: String = value
}

object EMailadres {
  private val ValidEmailAddressPattern: Regex = raw"""^[^\s@]+@[^\s@]+\.[^\s@]+$$""".r
  private val MaxLength: Int                  = 255

  private def validate(emailAddress: String): Boolean =
    emailAddress match {
      case ea if ea.length > MaxLength  => false
      case ValidEmailAddressPattern(_*) => true
      case _                            => false
    }

  def apply(value: String): EMailadres =
    new EMailadres(value.trim.toLowerCase)

  implicit def eMailadresToString(eMailadres: EMailadres): String = eMailadres.value
  implicit def stringToEMailadres(value: String): EMailadres      = EMailadres(value)
}
