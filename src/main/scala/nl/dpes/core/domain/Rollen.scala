package nl.dpes.core.domain

import nl.dpes.core.domain.exceptions.RolWordtNietOndersteund

import scala.util.{Failure, Success, Try}

object Rollen extends Enumeration {
  type Rol = Value
  val Medewerker, Werkzoekende, Recruiter = Value

  private val Index = Map(
    "medewerker"   -> Medewerker,
    "werkzoekende" -> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    "recruiter"    -> Recruiter
  )

  implicit def stringToRol(rolnaam: String): Rol = Try(Index(rolnaam)) match {
    case Success(rol) => rol
    case Failure(_)   => throw RolWordtNietOndersteund(rolnaam)
  }

  implicit def rolToString(rol: Rol): String = (for {
    (key, value) <- Index if value == rol
  } yield key).head
}
