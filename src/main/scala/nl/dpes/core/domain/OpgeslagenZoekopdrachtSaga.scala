package nl.dpes.core.domain

import java.time.Instant

import nl.dpes.core.domain.Zoekopdracht.{<PERSON><PERSON><PERSON><PERSON><PERSON>d, OpgeslagenZoekopdrachtEvent, VerwijderdDoorRecruiter}
import nl.dpes.core.services.SavedSearch
import nl.dpes.utils.date.SavedSearchClock
import org.axonframework.deadline.DeadlineManager
import org.axonframework.deadline.annotation.DeadlineHandler
import org.axonframework.eventhandling.Timestamp
import org.axonframework.modelling.saga.{EndSaga, SagaEventHandler, SagaLifecycle, StartSaga}
import org.slf4j.Logger

import scala.concurrent.Await
import scala.concurrent.ExecutionContext.Implicits.global
import scala.concurrent.duration._
import scala.util.{Failure, Success, Try}

object OpgeslagenZoekopdrachtSaga {

  import spray.json.RootJsonFormat
  import spray.json.DefaultJsonProtocol._
  import nl.dpes.utils.sprayjson._
  import nl.dpes.core.config.components.json.JsonProtocol.frequentieFmt

  implicit val format: RootJsonFormat[OpgeslagenZoekopdrachtSaga] = {
    case class Representation(maybeToken: Option[String], frequentie: Frequenties.Frequentie)
    object Representation {
      def from(repr: Representation): OpgeslagenZoekopdrachtSaga = {
        val saga = new OpgeslagenZoekopdrachtSaga()
        saga.maybeToken = repr.maybeToken
        saga.frequentie = repr.frequentie
        saga
      }

      def to(saga: OpgeslagenZoekopdrachtSaga): Representation = Representation(maybeToken = saga.maybeToken, frequentie = saga.frequentie)

      val format: RootJsonFormat[Representation] = jsonFormat2(Representation.apply)
    }

    Representation.format.imap(Representation.from, Representation.to)
  }

  val frequentietijdVerstreken = "frequentietijd-verstreken"

  final case class FrequentietijdVerstreken(zoekopdrachtId: String, accountId: String) extends OpgeslagenZoekopdrachtEvent
}

class OpgeslagenZoekopdrachtSaga {

  import OpgeslagenZoekopdrachtSaga._

  var maybeToken: Option[String]         = None
  var frequentie: Frequenties.Frequentie = Frequenties.Nooit

  @StartSaga
  @SagaEventHandler(associationProperty = "opgeslagenZoekopdrachtId")
  def onOpgeslagenDoorRecruiter(
    event: Zoekopdracht.OpgeslagenDoorRecruiter,
    @Timestamp timestamp: Instant,
    deadlineManager: DeadlineManager,
    savedSearchClock: SavedSearchClock,
    logger: Logger
  ): Unit = {
    frequentie = event.frequentie
    maybeToken = schedule(event.zoekopdrachtId, event.recruiterId, frequentie, deadlineManager, savedSearchClock, logger, Some(timestamp))

    SagaLifecycle.associateWith("recruiterId", event.recruiterId)
    SagaLifecycle.associateWith("zoekopdrachtId", event.zoekopdrachtId)
  }

  @DeadlineHandler(deadlineName = "frequentietijd-verstreken")
  def onFrequentietijdVerstreken(
    deadline: FrequentietijdVerstreken,
    deadlineManager: DeadlineManager,
    savedSearch: SavedSearch,
    savedSearchClock: SavedSearchClock,
    logger: Logger
  ): Unit = {

    logger.info(s"SAVED_SEARCH: FrequentietijdVerstreken for account: ${deadline.accountId} and zoekopdracht: ${deadline.zoekopdrachtId}")

    val future = savedSearch.searchAndSend(deadline.zoekopdrachtId, deadline.accountId)

    Await.ready(future, 30 seconds)

    future.value.get match {
      case Success(_) =>
        maybeToken = schedule(deadline.zoekopdrachtId, deadline.accountId, frequentie, deadlineManager, savedSearchClock, logger)
        logger.info(s"SAVED_SEARCH: SearchAndSend completed for : ${deadline.accountId} and zoekopdracht: ${deadline.zoekopdrachtId}")
      case Failure(e) if e.getMessage.contains("saved search not found") || e.getMessage.contains("recruiter not found") =>
        //zombie sagas killer
        cancel(deadlineManager)
        SagaLifecycle.end()
        logger.error(
          s"SAVED_SEARCH: Saga canceled for account: ${deadline.accountId} and zoekopdracht: ${deadline.zoekopdrachtId} with error ${e.getMessage}",
          e
        )
      case Failure(e) =>
        maybeToken = schedule(deadline.zoekopdrachtId, deadline.accountId, frequentie, deadlineManager, savedSearchClock, logger)
        logger.error(
          s"SAVED_SEARCH: SearchAndSend failed for account: ${deadline.accountId} and zoekopdracht: ${deadline.zoekopdrachtId} with error ${e.getMessage}",
          e
        )
    }
  }

  @SagaEventHandler(associationProperty = "opgeslagenZoekopdrachtId")
  def onGewijzigd(
    event: Gewijzigd,
    @Timestamp timestamp: Instant,
    deadlineManager: DeadlineManager,
    savedSearchClock: SavedSearchClock,
    logger: Logger
  ): Unit =
    if (frequentie != event.frequentie) {
      frequentie = event.frequentie
      maybeToken = schedule(event.zoekopdrachtId, event.accountId, frequentie, deadlineManager, savedSearchClock, logger, Some(timestamp))
    }

  @EndSaga
  @SagaEventHandler(associationProperty = "opgeslagenZoekopdrachtId")
  def onVerwijderdDoorRecruiter(event: VerwijderdDoorRecruiter, deadlineManager: DeadlineManager): Unit =
    cancel(deadlineManager)

  private def schedule(
    zoekopdrachtId: String,
    recruiterId: String,
    frequentie: Frequenties.Frequentie,
    deadlineManager: DeadlineManager,
    savedSearchClock: SavedSearchClock,
    logger: Logger,
    basedOnDate: Option[Instant] = None
  ): Option[String] = {
    cancel(deadlineManager)

    basedOnDate
      .flatMap(basedOn => savedSearchClock.getFirstScheduleTimeForRecruiter(recruiterId, frequentie, basedOn))
      .orElse(savedSearchClock.getNextScheduleTimeForRecruiter(recruiterId, frequentie))
      .map { instant =>
        Try(deadlineManager.schedule(instant, frequentietijdVerstreken, FrequentietijdVerstreken(zoekopdrachtId, recruiterId))) match {
          case Success(scheduleToken) => Some(scheduleToken)
          case Failure(exception) =>
            logger.error(
              s"SAVED_SEARCH: Scheduling FrequentietijdVerstreken event failed " +
              s"for account: $recruiterId and zoekopdracht: $zoekopdrachtId " +
              s"with error ${exception.getMessage}",
              exception
            )
            None
        }
      }
      .getOrElse {
        cancel(deadlineManager)
        None
      }
  }

  private def cancel(deadlineManager: DeadlineManager): Unit = {
    Try(maybeToken.foreach(token => deadlineManager.cancelSchedule(frequentietijdVerstreken, token)))
    maybeToken = None
  }
}
