package nl.dpes.core.services

import nl.dpes.core.domain.Recruiter.{ImporteerNaarSalesforce, MaakFavoriet, Verwijder}
import nl.dpes.core.domain.Sites
import nl.dpes.core.domain.Zoekopdracht.{Sla<PERSON>p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, VerwijderDoorRecruiter}
import nl.dpes.core.projections.core.CoreRecruiterAccountProjections
import nl.dpes.core.projections.core.CoreRecruiterAccountProjections.CoreRecruiterAccount
import nl.dpes.core.projections.index.IndexOpgeslagenZoekopdrachtProjections.IndexOpgeslagenZoekopdracht
import nl.dpes.core.projections.index.IndexRecruiterFavorietProjections.RecruiterFavorietDocument
import nl.dpes.core.projections.index.{IndexOpgeslagenZoekopdrachtProjections, IndexRecruiterFavorietProjections}
import org.axonframework.commandhandling.gateway.CommandGateway
import org.slf4j.Logger

import scala.concurrent.{Await, ExecutionContext, Future}
import scala.concurrent.duration._

class RecruiterIdToSalesforceIdMigrator(
  accountProjection: CoreRecruiterAccountProjections,
  favorietProjection: IndexRecruiterFavorietProjections,
  savedSearchProjection: IndexOpgeslagenZoekopdrachtProjections,
  commandGateway: CommandGateway
)(implicit logger: Logger, ec: ExecutionContext) {

  def migrate(recruiterIds: Seq[String], salesforceId: String): Unit =
    recruiterIds.flatMap(accountProjection.findByRecruiterId) match {
      case Nil => logger.info(s"Could not find recruiter ($recruiterIds, $salesforceId). Skipping")
      case recruiters =>
        logger.info(s"Migrating recruiter ($recruiterIds, $salesforceId)")
        runMigration(recruiters, salesforceId)
    }

  def runMigration(recruiters: Seq[CoreRecruiterAccount], salesforceId: String): Unit = {
    val (_, savedSearches) = migrateRecruiters(recruiters, salesforceId)
    recruiters.foreach(recruiter => commandGateway.sendAndWait(Verwijder(recruiter.recruiterId)))
    savedSearches.foreach(zoekopdracht =>
      commandGateway.sendAndWait(VerwijderDoorRecruiter(zoekopdracht.zoekopdrachtId, zoekopdracht.accountId))
    )
  }

  def migrateRecruiters(
    recruiters: Seq[CoreRecruiterAccount],
    salesforceId: String
  ): (Seq[CoreRecruiterAccount], Seq[IndexOpgeslagenZoekopdracht]) = {
    val favorites: Seq[RecruiterFavorietDocument] = Await
      .result(
        Future.sequence(
          recruiters.map(recruiter => favorietProjection.searchFavoriet(recruiter.recruiterId, 1, 100).map(_.items))
        ),
        10 seconds
      )
      .flatten

    val savedSearches: Seq[IndexOpgeslagenZoekopdracht] =
      recruiters.flatMap(recruiter => savedSearchProjection.findByRecruiterId(recruiter.recruiterId))

    migrateCommands(recruiters, salesforceId, favorites, savedSearches).map(commandGateway.sendAndWait)
    (recruiters, savedSearches)
  }

  def migrateCommands(
    recruiters: Seq[CoreRecruiterAccount],
    salesforceId: String,
    favorites: Seq[RecruiterFavorietDocument],
    savedSearches: Seq[IndexOpgeslagenZoekopdracht]
  ): List[Any] =
    List(ImporteerNaarSalesforce(salesforceId, recruiters.head.eMailadres, Sites.Ndp)) ++
    savedSearches.flatMap { search =>
      List(
        SlaOpDoorRecruiter(
          search.zoekopdrachtId,
          salesforceId,
          search.naam,
          search.frequentie
        )
      )
    } ++
    favorites
      .map(favorite => MaakFavoriet(salesforceId, favorite.werkzoekendeId))
      .sortBy(com => com.werkzoekendeId)
}
