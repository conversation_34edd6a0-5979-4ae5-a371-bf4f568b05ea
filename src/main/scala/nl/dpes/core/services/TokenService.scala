package nl.dpes.core.services

import nl.dpes.core.domain.Rollen
import nl.dpes.core.services.security.JWTProvider
import nl.dpes.core.services.security.tokens.TokenTypes.TokenType
import nl.dpes.core.services.security.tokens._

import scala.concurrent.duration._
import scala.util.Try

object TokenService {
  type TokenDescription            = (TokenType, String)
  type TokenExtraction[T <: Token] = PartialFunction[TokenDescription, Either[Throwable, T]]

  final val VerificationTokenValidity = 14 days
  final val PasswordTokenValidity     = 24 hours
  final val AccessTokenValidity       = 14 days
  final val ChangeEmailTokenValidity  = 7 days
  final val LoginAsTokenValidity      = 15 seconds

  private final val PasswordTokenIdClaim = "passwordTokenId"
  private final val EmailTokenIdClaim    = "emailTokenId"
  private final val SanDiegoIdClaim      = "sanDiegoId"
  private final val TokenTypeClaim       = "type"
  private final val EmailAddressClaim    = "emailAddress"
  private final val SiteClaim            = "site"
}

class TokenService(jwt: JWTProvider) {
  import TokenService._

  private lazy val tokenExtractionChain = extractAccessToken orElse
    extractVerificationToken orElse
    extractResetPasswordToken orElse
    extractChangeEmailToken orElse
    extractLoginAsToken

  def extract[T <: Token](token: String, expect: TokenTypes.TokenType*): Either[Throwable, T] = {
    val tokenType = for {
      tokenString <- jwt.getClaimFromToken(token, TokenTypeClaim)
      tokenType   <- Try(TokenTypes.stringToTokenType(tokenString)).toEither
    } yield tokenType

    tokenType.fold(
      error => Left(error),
      tokenType =>
        if (expect.nonEmpty && !expect.contains(tokenType)) {
          Left(InvalidTokenType(tokenType))
        } else {
          (tokenExtractionChain orElse invalidTokenType)((tokenType, token))
            .map(_.asInstanceOf[T])
        }
    )
  }

  private val invalidTokenType = new TokenExtraction[Token] {
    override def apply(v1: TokenDescription): Either[Throwable, Token] = Left(InvalidTokenType(v1._1))
    override def isDefinedAt(x: TokenDescription): Boolean             = true
  }

  private val extractAccessToken = new TokenExtraction[AccessToken] {

    override def apply(v1: TokenDescription): Either[Throwable, AccessToken] = v1._1 match {
      case TokenTypes.AccessToken =>
        for {
          userId            <- jwt.getUserID(v1._2)
          roles             <- jwt.getRoles(v1._2)
          maybeSanDiegoId   <- jwt.getOptionalClaimFromToken(v1._2, SanDiegoIdClaim).map(_.map(_.toInt))
          maybeEmailAddress <- jwt.getOptionalClaimFromToken(v1._2, EmailAddressClaim)
          maybeSite         <- jwt.getOptionalClaimFromToken(v1._2, SiteClaim)
        } yield AccessToken(userId, maybeSanDiegoId, roles.head, maybeEmailAddress, maybeSite)
    }

    override def isDefinedAt(x: TokenDescription): Boolean =
      x._1 == TokenTypes.AccessToken
  }

  private val extractVerificationToken = new TokenExtraction[VerificationToken] {

    override def apply(v1: TokenDescription): Either[Throwable, VerificationToken] = v1._1 match {
      case TokenTypes.VerificationToken =>
        for {
          userId <- jwt.getUserID(v1._2)
          roles  <- jwt.getRoles(v1._2)
        } yield VerificationToken(userId, roles.head)
    }

    override def isDefinedAt(x: TokenDescription): Boolean =
      x._1 == TokenTypes.VerificationToken
  }

  private val extractResetPasswordToken = new TokenExtraction[ResetPasswordToken] {

    override def apply(v1: TokenDescription): Either[Throwable, ResetPasswordToken] = v1._1 match {
      case TokenTypes.ResetPasswordToken =>
        for {
          userId  <- jwt.getUserID(v1._2)
          roles   <- jwt.getRoles(v1._2)
          tokenId <- jwt.getClaimFromToken(v1._2, PasswordTokenIdClaim)
        } yield ResetPasswordToken(userId, roles.head, tokenId)
    }

    override def isDefinedAt(x: TokenDescription): Boolean =
      x._1 == TokenTypes.ResetPasswordToken
  }

  private val extractChangeEmailToken = new TokenExtraction[ChangeEmailToken] {

    override def apply(v1: TokenDescription): Either[Throwable, ChangeEmailToken] = v1._1 match {
      case TokenTypes.ChangeEmailToken =>
        for {
          userId  <- jwt.getUserID(v1._2)
          roles   <- jwt.getRoles(v1._2)
          tokenId <- jwt.getClaimFromToken(v1._2, EmailTokenIdClaim)
        } yield ChangeEmailToken(userId, roles.head, tokenId)
    }

    override def isDefinedAt(x: TokenDescription): Boolean =
      x._1 == TokenTypes.ChangeEmailToken
  }

  private val extractLoginAsToken = new TokenExtraction[LoginAsToken] {

    override def apply(v1: TokenDescription): Either[Throwable, LoginAsToken] = v1._1 match {
      case TokenTypes.LoginAsToken =>
        for {
          userId <- jwt.getUserID(v1._2)
          roles  <- jwt.getRoles(v1._2)
        } yield LoginAsToken(userId, roles.head)
    }

    override def isDefinedAt(x: TokenDescription): Boolean =
      x._1 == TokenTypes.LoginAsToken
  }

  def generate(token: AccessToken): String = {
    val claims = Map[String, String](
      TokenTypeClaim    -> token.tokenType,
      SanDiegoIdClaim   -> token.sanDiegoId.map(_.toString).getOrElse(""),
      EmailAddressClaim -> token.emailAddress.getOrElse(""),
      SiteClaim         -> token.site.getOrElse("")
    ).filter(_._2 != "")

    jwt.createAndSignJWTToken(token.userId, List(token.role), AccessTokenValidity, claims)
  }

  def generate(token: VerificationToken): String =
    jwt.createAndSignJWTToken(token.userId, List(token.role), VerificationTokenValidity, Map(TokenTypeClaim -> token.tokenType))

  def generate(token: ResetPasswordToken): String =
    jwt.createAndSignJWTToken(
      token.userId,
      List(token.role),
      PasswordTokenValidity,
      Map(TokenTypeClaim -> token.tokenType, PasswordTokenIdClaim -> token.tokenId)
    )

  def generate(token: ChangeEmailToken): String =
    jwt.createAndSignJWTToken(
      token.userId,
      List(token.role),
      ChangeEmailTokenValidity,
      Map(TokenTypeClaim -> token.tokenType, EmailTokenIdClaim -> token.tokenId)
    )

  def generate(token: LoginAsToken): String =
    jwt.createAndSignJWTToken(token.userId, List(token.role), LoginAsTokenValidity, Map(TokenTypeClaim -> token.tokenType))
}
