package nl.dpes.core

package object services {

  case class Filter(elements: Map[String, String]) {
    def asMap: Map[String, String] = elements
  }

  object Filter {

    def apply(filterValues: (String, Option[String])*): Filter =
      new Filter(filterValues.collect { case (key, Some(value)) => key -> value }.toMap)

    implicit def fromTuple(keyValuePair: (String, String)): Filter = Filter(keyValuePair._1 -> Some(keyValuePair._2))
  }
}
