package nl.dpes.core.services.ndsm

import akka.http.scaladsl.marshallers.sprayjson.SprayJsonSupport
import nl.dpes.core.domain.Sites.Site
import nl.dpes.core.services.Filter
import nl.dpes.core.services.ndsm.JobSeekerServiceClient._
import org.slf4j.Logger
import spray.json.{DefaultJsonProtocol, JsonFormat, RootJsonFormat}
import sttp.client3.sprayJson._
import sttp.client3.{basicRequest, Response, SttpBackend, UriContext}
import sttp.model.Header

import scala.concurrent.ExecutionContext.Implicits.global
import scala.concurrent.Future

class HttpJobSeekerServiceClient(baseUrl: String, apiKey: String)(implicit
  sttpBackend: SttpBackend[Future, _],
  logger: Logger
) extends JobSeekerServiceClient {

  private object JsonProtocol extends SprayJsonSupport with DefaultJsonProtocol {
    implicit val experienceFormat: JsonFormat[Experience]   = jsonFormat1(Experience)
    implicit val commuteFormat: JsonFormat[Commute]         = jsonFormat1(Commute)
    implicit val jobSeekerFormat: RootJsonFormat[JobSeeker] = jsonFormat14(JobSeeker)

    implicit val searchResultFormat: RootJsonFormat[SearchResult[JobSeeker]] =
      jsonFormat(SearchResult[JobSeeker], "result", "result_count", "location_not_found", "invalid_query")
    implicit val searchTermsFormat: RootJsonFormat[SearchTerms]           = jsonFormat6(SearchTerms)
    implicit val searchParametersFormat: RootJsonFormat[SearchParameters] = jsonFormat14(SearchParameters)

    case class MinimumProfile(id: String, emailAddress: String)
    implicit val minimumProfileFormat: RootJsonFormat[MinimumProfile] = jsonFormat2(MinimumProfile)
  }

  import JobSeekerServiceClient._
  import JsonProtocol._

  private def getHeaders(siteName: String) = List(
    Header.accept(sttp.model.MediaType.ApplicationJson),
    Header("X-API-Key", apiKey),
    Header("X-NDSM-Token", siteName),
    Header("Accept-Language", "nl_NL")
  )

  override def retrieveJobSeekers(filter: Filter, site: Site): Future[List[JobSeeker]] = {
    val siteName: Option[String] = NdsmTokensBySite.get(site)
    val jobSeekers = siteName.map { siteName =>
      val eventualResponse = basicRequest
        .get(uri"$baseUrl/job-seekers".withParams(filter.asMap))
        .headers(getHeaders(siteName), replaceExisting = true)
        .response(asJson[List[JobSeeker]])
        .send(sttpBackend)

      for {
        response <- eventualResponse
        jobSeekers <- response.body match {
          case Right(jobSeekers) => Future(jobSeekers)
          case _ =>
            logger.error(
              s"Request to retrieve jobseekers on site $siteName with filter ${filter.asMap.toString} failed with status ${response.statusText}"
            )
            Future.failed(new Error(s"Request to retrieve jobseekers on site $siteName failed with status ${response.code.code}"))
        }
      } yield jobSeekers
    }
    jobSeekers.getOrElse(Future.successful(List.empty))
  }

  override def findByUuid(uuid: String): Future[Option[JobSeeker]] = {
    val siteName = NdsmTokensBySite.head._2

    val eventualResponse = basicRequest
      .get(uri"$baseUrl/job-seekers/$uuid")
      .headers(getHeaders(siteName), replaceExisting = true)
      .response(asJson[Option[JobSeeker]])
      .send(sttpBackend)

    for {
      response <- eventualResponse
      maybeJobSeeker <- response match {
        case Response(Right(jobSeeker), sttp.model.StatusCode.Ok, _, _, _, _) => Future.successful(jobSeeker)
        case Response(_, sttp.model.StatusCode.NotFound, _, _, _, _)          => Future.successful(None)
        case Response(_, sttp.model.StatusCode.Gone, _, _, _, _)              => Future.successful(None)
        case response                                                         => Future.failed(new RuntimeException(s"Failed to find UUID $uuid: ${response.toString}"))
      }
    } yield maybeJobSeeker
  }

  override def search(searchParameters: SearchParameters): Future[SearchResult[JobSeeker]] = {
    val siteName = getDefaultSiteName
    val uri      = uri"$baseUrl/search"

    for {
      response <- basicRequest
        .headers(getHeaders(siteName), replaceExisting = true)
        .post(uri)
        .body(searchParameters)
        .response(asJson[SearchResult[JobSeeker]])
        .send(sttpBackend)
      result <- response match {
        case Response(Right(searchResult), sttp.model.StatusCode.Ok, _, _, _, _) => Future.successful(searchResult)
        case failedResponse                                                      => Future.failed(new RuntimeException("JobSeeker search for saved-search failed " + failedResponse.toString()))
      }
    } yield result
  }

  private def getDefaultSiteName =
    // send first site in the list
    NdsmTokensBySite.head._2
}
