package nl.dpes.core.services.ndsm

import nl.dpes.core.domain.{EMailadres, Sites}
import nl.dpes.core.domain.Sites.Site
import nl.dpes.core.services.Filter

import scala.concurrent.Future

object JobSeekerServiceClient {

  val NdsmTokensBySite = Map(
    Sites.Iol -> "intermediair.nl",
    Sites.Itb -> "itbanen.nl",
    Sites.Nvb -> "nationalevacaturebank.nl"
  )

  final case class JobSeeker(
    id: String,
    legacyJobSeekerId: Option[Int],
    site: String,
    firstName: Option[String],
    lastName: Option[String],
    emailAddress: String,
    experiences: Option[Seq[Experience]] = None,
    preferredJobs: Option[Seq[String]] = None,
    workLevels: Option[Seq[String]] = None,
    workingHours: Option[Seq[String]] = None,
    availability: Option[String] = None,
    city: Option[String] = None,
    commute: Option[Commute] = None,
    isSavedSearchProfile: Option[Boolean] = None
  )

  final case class Experience(jobTitle: Option[String])

  final case class Commute(city: String)

  final case class SearchResult[T](
    result: Seq[T],
    resultCount: Int,
    locationNotFound: Boolean,
    invalidQuery: Boolean
  )

  final case class SearchParameters(
    term: Option[SearchTerms] = None,
    location: Option[String] = None,
    radius: Option[Seq[String]] = None,
    updatedDate: Option[Seq[String]] = None,
    workLevels: Option[Seq[String]] = None,
    workingHours: Option[Seq[String]] = None,
    availability: Option[Seq[String]] = None,
    driverLicenses: Option[Seq[String]] = None,
    languages: Option[Seq[String]] = None,
    careerLevel: Option[Seq[String]] = None,
    functionGroups: Option[Seq[String]] = None,
    requestedSalary: Option[Seq[String]] = None,
    provinces: Option[Seq[String]] = None,
    pageSize: Option[Int] = None
  )

  final case class SearchTerms(
    all: Option[String] = None,
    educationName: Option[String] = None,
    educationDescription: Option[String] = None,
    preferredJob: Option[String] = None,
    functionTitle: Option[String] = None,
    functionDescription: Option[String] = None
  )
}

trait JobSeekerServiceClient {
  import JobSeekerServiceClient._

  def retrieveJobSeekers(filter: Filter, site: Site): Future[List[JobSeeker]]

  def findByUuid(uuid: String): Future[Option[JobSeeker]]

  def search(searchParameters: SearchParameters): Future[SearchResult[JobSeeker]]
}
