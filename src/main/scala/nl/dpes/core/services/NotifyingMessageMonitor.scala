package nl.dpes.core.services

import nl.dpes.core.services.NotifyingMessageMonitor.FailureActions
import nl.dpes.core.services.notifier.Notifier
import nl.dpes.core.services.notifier.Notifier.Severities
import org.axonframework.commandhandling.CommandMessage
import org.axonframework.eventhandling.DomainEventMessage
import org.axonframework.messaging.Message
import org.axonframework.monitoring.MessageMonitor
import org.slf4j.Logger

import scala.concurrent.ExecutionContext
import scala.util.{Failure, Success}

object NotifyingMessageMonitor {

  object Actions extends Enumeration {
    type Action = Value

    val Ignore, Notify = Value
  }

  type FailureActions = Map[Class[_ <: Throwable], Actions.Action]

  object GenericMessage {

    final def failure(node: String, messageId: String): String =
      s"Processing of message failed: $messageId [node $node]"

    final def ignored(node: String, messageId: String): String =
      s"Message has been ignored: $messageId [node $node]"

    final def success(node: String, messageId: String): String =
      s"Message successfully processed: $messageId [node $node]"
  }

  object DomainEvent {

    final def failure(node: String, messageType: String, aggregateId: String, sequenceNumber: Long): String =
      s"Domain event $messageType has failed to be handled [node: $node, aggregate: $aggregateId, seq.no. $sequenceNumber]"

    final def ignored(node: String, messageType: String, aggregateId: String, sequenceNumber: Long): String =
      s"Domain event $messageType has been ignored [node: $node, aggregate: $aggregateId, seq.no. $sequenceNumber]"

    final def success(node: String, messageType: String, aggregateId: String, sequenceNumber: Long): String =
      s"Domain event $messageType successfully processed [node: $node, aggregate: $aggregateId, seq.no. $sequenceNumber]"
  }

  object Command {

    final def failure(node: String, commandName: String): String =
      s"Command $commandName has failed to be handled [node $node]"

    final def ignored(node: String, commandName: String): String =
      s"Command $commandName has been ignored [node $node]"

    final def success(node: String, commandName: String): String =
      s"Command $commandName successfully handled [node $node]"
  }
}

class NotifyingMessageMonitor(notifier: Notifier, nodeName: String, actions: FailureActions = Map.empty)(implicit
  logger: Logger,
  executionContext: ExecutionContext
) extends MessageMonitor[Message[_]] {
  import NotifyingMessageMonitor._

  override def onMessageIngested(message: Message[_]): MessageMonitor.MonitorCallback =
    new MessageMonitor.MonitorCallback {

      override def reportSuccess(): Unit =
        notifier.notify(createSuccessMessage(message)) onComplete {
          case Success(_) => ()
          case Failure(e) => logger.error(e.getMessage, e)
        }

      override def reportFailure(cause: Throwable): Unit =
        if (!actions.exists(action => action._2 == Actions.Ignore && action._1 == cause.getClass)) {
          notifier.notify(createFailureMessage(message, cause)) onComplete {
            case Success(_) => ()
            case Failure(e) => logger.error(e.getMessage, e)
          }
        }

      override def reportIgnored(): Unit =
        notifier.notify(createIgnoreMessage(message)) onComplete {
          case Success(_) => ()
          case Failure(e) => logger.error(e.getMessage, e)
        }
    }

  private def createFailureMessage(message: Message[_], cause: Throwable): Notifier.Message = message match {
    case msg: DomainEventMessage[_] =>
      Notifier.Message(
        DomainEvent.failure(nodeName, msg.getPayloadType.getCanonicalName, msg.getAggregateIdentifier, msg.getSequenceNumber),
        cause.toString,
        Severities.Alert
      )
    case msg: CommandMessage[_] =>
      Notifier.Message(
        Command.failure(nodeName, msg.getCommandName),
        cause.toString,
        Severities.Critical
      )
    case _ =>
      Notifier.Message(
        GenericMessage.failure(nodeName, message.getIdentifier),
        cause.toString,
        Severities.Error
      )
  }

  private def createIgnoreMessage(message: Message[_]): Notifier.Message = message match {
    case msg: DomainEventMessage[_] =>
      Notifier.Message(
        DomainEvent.ignored(nodeName, msg.getPayloadType.getCanonicalName, msg.getAggregateIdentifier, msg.getSequenceNumber),
        DomainEvent.ignored(nodeName, msg.getPayloadType.getCanonicalName, msg.getAggregateIdentifier, msg.getSequenceNumber),
        Severities.Warning
      )
    case msg: CommandMessage[_] =>
      Notifier.Message(
        Command.ignored(nodeName, msg.getCommandName),
        Command.ignored(nodeName, msg.getCommandName),
        Severities.Warning
      )
    case _ =>
      Notifier.Message(
        GenericMessage.ignored(nodeName, message.getIdentifier),
        GenericMessage.ignored(nodeName, message.getIdentifier),
        Severities.Warning
      )
  }

  private def createSuccessMessage(message: Message[_]): Notifier.Message = message match {
    case msg: DomainEventMessage[_] =>
      Notifier.Message(
        DomainEvent.success(nodeName, msg.getPayloadType.getCanonicalName, msg.getAggregateIdentifier, msg.getSequenceNumber),
        DomainEvent.success(nodeName, msg.getPayloadType.getCanonicalName, msg.getAggregateIdentifier, msg.getSequenceNumber),
        Severities.Info
      )
    case msg: CommandMessage[_] =>
      Notifier.Message(
        Command.success(nodeName, msg.getCommandName),
        Command.success(nodeName, msg.getCommandName),
        Severities.Info
      )
    case _ =>
      Notifier.Message(
        GenericMessage.success(nodeName, message.getIdentifier),
        GenericMessage.success(nodeName, message.getIdentifier),
        Severities.Info
      )
  }
}
