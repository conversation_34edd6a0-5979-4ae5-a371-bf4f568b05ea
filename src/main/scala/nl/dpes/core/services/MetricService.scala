package nl.dpes.core.services

import com.amazonaws.services.cloudwatch.AmazonCloudWatch
import com.amazonaws.services.cloudwatch.model.{Dimension, MetricDatum, PutMetricDataRequest, StandardUnit}
import nl.dpes.core.config.Environment
import org.slf4j.Logger

import scala.util.{Failure, Try}

class MetricService(client: AmazonCloudWatch)(private implicit val logger: Logger, environment: Environment) {

  def measureSanDiegoEventHandle(): Unit = {
    val dimension = new Dimension()
      .withName("Environment")
      .withValue(environment.abbreviation)

    val datum = new MetricDatum()
      .withMetricName("SanDiegoEventsHandled")
      .withDimensions(dimension)
      .withUnit(StandardUnit.Count)
      .withValue(1.0)

    Try {
      client.putMetricData(
        new PutMetricDataRequest()
          .withNamespace("NDP")
          .withMetricData(datum)
      )
    } match {
      case Failure(e) => logger.error(e.getMessage, e)
      case _          => ()
    }
  }
}
