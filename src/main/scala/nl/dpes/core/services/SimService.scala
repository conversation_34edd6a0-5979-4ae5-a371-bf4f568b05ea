package nl.dpes.core.services

import nl.dpes.core.domain.Werkzoekende
import nl.dpes.core.projections.core.CoreWerkzoekendeAccountProjections
import nl.dpes.core.services.sim.SimClient
import nl.dpes.core.services.sim.SimClient.UserProfile
import org.axonframework.eventhandling.{EventHandler, Timestamp}
import org.joda.time.DateTime
import org.slf4j.Logger

import scala.concurrent.ExecutionContext.Implicits.global
import scala.util.Failure

object SimService {
  final private val Origin = "NDP"

  object Statuses extends Enumeration {
    type Status = Value

    val Geregistreerd: Status = Value("Geregistreerd")
    val Geverifieerd: Status  = Value("Geverifieerd")

    implicit def statusToString(status: Status): String =
      status.toString
  }
}

class SimService(client: SimClient, werkzoekendeAccountProjections: CoreWerkzoekendeAccountProjections)(implicit
  private val logger: Logger
) {
  import SimService._

  @EventHandler
  def onGeregistreerd(event: Werkzoekende.Geregistreerd, @Timestamp createdDateTime: java.time.Instant): Unit =
    createOrUpdateUserProfile(
      UserProfile(
        event.werkzoekendeId,
        event.eMailadres,
        event.site,
        Origin,
        None,
        None,
        Statuses.Geregistreerd,
        Some(new DateTime(createdDateTime.toEpochMilli)),
        Some(new DateTime(createdDateTime.toEpochMilli))
      )
    )

  @EventHandler
  def onGeregistreerdExternal(event: Werkzoekende.GeregistreerdExternal, @Timestamp createdDateTime: java.time.Instant): Unit =
    createOrUpdateUserProfile(
      UserProfile(
        event.werkzoekendeId,
        event.eMailadres,
        event.site,
        Origin,
        None,
        None,
        Statuses.Geverifieerd,
        Some(new DateTime(createdDateTime.toEpochMilli)),
        Some(new DateTime(createdDateTime.toEpochMilli))
      )
    )

  @EventHandler
  def onGeverifieerd(event: Werkzoekende.Geverifieerd, @Timestamp updatedDatetime: java.time.Instant): Unit =
    werkzoekendeAccountProjections.findByWerkzoekendeId(event.werkzoekendeId).foreach { werkzoekende =>
      createOrUpdateUserProfile(
        UserProfile(
          werkzoekende.werkzoekendeId,
          werkzoekende.eMailadres,
          werkzoekende.site,
          Origin,
          None,
          None,
          Statuses.Geverifieerd,
          None,
          Some(new DateTime(updatedDatetime.toEpochMilli))
        )
      )
    }

  @EventHandler
  def onEMailadresGewijzigd(event: Werkzoekende.EMailadresGewijzigd, @Timestamp updatedDatetime: java.time.Instant): Unit =
    werkzoekendeAccountProjections.findByWerkzoekendeId(event.werkzoekendeId).foreach { werkzoekende =>
      createOrUpdateUserProfile(
        UserProfile(
          werkzoekende.werkzoekendeId,
          event.nieuwEMailadres,
          werkzoekende.site,
          Origin,
          None,
          None,
          Statuses.Geverifieerd,
          None,
          Some(new DateTime(updatedDatetime.toEpochMilli))
        )
      )
    }

  @EventHandler
  def onIngeschrevenVoorEMail(event: Werkzoekende.IngeschrevenVoorEMail): Unit =
    client.subscribe(event.werkzoekendeId, event.eMailinschrijving, event.site) onComplete {
      case Failure(e) =>
        logger.error(e.getMessage, e)
      case _ =>
    }

  @EventHandler
  def onUitgeschrevenVoorEMail(event: Werkzoekende.UitgeschrevenVoorEMail): Unit =
    client.unsubscribe(event.werkzoekendeId, event.eMailinschrijving, event.site) onComplete {
      case Failure(e) =>
        logger.error(e.getMessage, e)
      case _ =>
    }

  @EventHandler
  def onAccountOpgezegd(event: Werkzoekende.AccountOpgezegd): Unit =
    client.deleteProfile(event.werkzoekendeId)

  private def createOrUpdateUserProfile(profile: UserProfile): Unit =
    client.createOrUpdateUserProfile(profile) onComplete {
      case Failure(e) =>
        logger.error(e.getMessage, e)
      case _ =>
    }
}
