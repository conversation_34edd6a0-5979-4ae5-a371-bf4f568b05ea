package nl.dpes.core.services

import nl.dpes.core.domain.Sites._
import nl.dpes.core.domain.Werkzoekende.{AccountOpgezegd, EMailadresGewijzigd}
import nl.dpes.core.domain.{EMailadres, Zoekparameters, Zoektermen}
import nl.dpes.core.services.ndsm.JobSeekerServiceClient
import nl.dpes.core.services.ndsm.JobSeekerServiceClient.{SearchParameters, SearchResult, SearchTerms}
import org.axonframework.eventhandling.EventHandler
import org.slf4j.Logger

import scala.concurrent.ExecutionContext.Implicits.global
import scala.concurrent.Future
import scala.util.Failure

object JobSeekerService {

  val SearchDefaultPageSize = 25

  object JobSeeker {

    implicit def fromClientJobSeeker(jobSeeker: JobSeekerServiceClient.JobSeeker): JobSeeker =
      JobSeeker(
        jobSeeker.id,
        jobSeeker.legacyJobSeekerId,
        jobSeeker.site,
        jobSeeker.firstName.getOrElse(""),
        jobSeeker.lastName.getOrElse(""),
        jobSeeker.emailAddress,
        jobSeeker.experiences.getOrElse(Seq.empty).map(exp => fromClientExperience(exp)),
        jobSeeker.preferredJobs.getOrElse(Seq.empty),
        jobSeeker.workLevels.getOrElse(Seq.empty).headOption,
        jobSeeker.workingHours.getOrElse(Seq.empty).headOption,
        jobSeeker.availability,
        jobSeeker.commute.map(_.city).orElse(jobSeeker.city),
        jobSeeker.isSavedSearchProfile.getOrElse(false) // By default we assume that werkzoekenden have a full profile
      )

    implicit def fromClientExperience(experience: JobSeekerServiceClient.Experience): Experience =
      Experience(experience.jobTitle)
  }

  implicit def fromZoekParameters(zoekparameters: Zoekparameters): SearchParameters =
    SearchParameters(
      zoekparameters.zoektermen.map(fromZoekTermen),
      zoekparameters.locatie,
      zoekparameters.afstandTotWerklocatie.map(filter => Seq(filter)),
      zoekparameters.wijzigingsdatum.map(filter => Seq(filter)),
      zoekparameters.opleidingsniveaus,
      zoekparameters.aantallenUren,
      zoekparameters.beschikbaarheden,
      zoekparameters.rijbewijzen,
      zoekparameters.talen,
      zoekparameters.carriereniveau,
      zoekparameters.functiegroep,
      zoekparameters.gewenstSalaris,
      zoekparameters.provincies,
      Some(SearchDefaultPageSize)
    )

  implicit def fromZoekTermen(zoektermen: Zoektermen): SearchTerms =
    SearchTerms(
      zoektermen.alles.getOrElse(Seq.empty).headOption,
      zoektermen.opleidingNaam.getOrElse(Seq.empty).headOption,
      zoektermen.opleidingBeschrijving.getOrElse(Seq.empty).headOption,
      zoektermen.gewensteBaan.getOrElse(Seq.empty).headOption,
      zoektermen.functieTitel.getOrElse(Seq.empty).headOption,
      zoektermen.functieBeschrijving.getOrElse(Seq.empty).headOption
    )

  final case class JobSeeker(
    id: String,
    legacyJobSeekerId: Option[Int],
    site: String,
    firstName: String,
    lastName: String,
    emailAddress: String,
    experiences: Seq[Experience] = Seq.empty,
    preferredJobs: Seq[String] = Seq.empty,
    workLevel: Option[String] = None,
    workingHours: Option[String] = None,
    availability: Option[String] = None,
    city: Option[String] = None,
    isSavedSearchProfile: Boolean = false
  )

  final case class Experience(jobTitle: Option[String])
}

class JobSeekerService(client: JobSeekerServiceClient, sites: List[Site] = Alle)(implicit private val logger: Logger) {
  import JobSeekerService._

  def findByUuid(uuid: String): Future[Option[JobSeeker]] = client.findByUuid(uuid).map(_.map(JobSeeker.fromClientJobSeeker))

  def search(zoekparameters: Zoekparameters): Future[SearchResult[JobSeeker]] = {

    // some radius values are wrong. Instead of "30", they are "*-30". This is a workaround to fix those
    def fixRadius =
      zoekparameters.afstandTotWerklocatie match {
        case Some(radius) =>
          val fixedRadius: String = "([0-9])+".r.findFirstIn(radius).getOrElse("0")
          zoekparameters.copy(
            afstandTotWerklocatie = Some(fixedRadius)
          )
        case None => zoekparameters
      }

    val radiusFixedSearchParameters: Zoekparameters = fixRadius

    client.search(radiusFixedSearchParameters).map { result: SearchResult[JobSeekerServiceClient.JobSeeker] =>
      SearchResult[JobSeeker](
        result.result.map(JobSeeker.fromClientJobSeeker),
        result.resultCount,
        result.locationNotFound,
        result.invalidQuery
      )
    }
  }
}
