package nl.dpes.core.services.protobuf

import java.time.{Instant, ZoneId, ZonedDateTime}

import nl.dpes.core.domain.Event

case class Envelope[T] private (correlationId: String, timestamp: Instant, source: String, payload: T)

object Envelope {

  implicit def toInstant(
    timestamp: Option[com.google.protobuf.timestamp.Timestamp]
  )(implicit zoneId: ZoneId = ZoneId.of("Z"): ZoneId): Instant =
    timestamp
      .map(src => Instant.ofEpochSecond(src.seconds, src.nanos))
      .getOrElse(ZonedDateTime.now(zoneId).toInstant)
}

trait ProtobufMapper[T <: Event] extends ProtobufReader[T] with ProtobufWriter[T]

trait ProtobufReader[T <: Event] {
  def fromProto(envelope: nl.dpes.protocol.v1.Envelope): Envelope[T]
}

trait ProtobufWriter[T <: Event] {
  def toProto(envelope: Envelope[T]): nl.dpes.protocol.v1.Envelope
}
