package nl.dpes.core.services.protobuf

import nl.dpes.core.domain.{Frequenties, Sites}
import nl.dpes.protocol.v1.Frequence.{DAILY, NEVER, WEEKLY}
import nl.dpes.protocol.v1.Site.{IOL, ITB, NVB}

object TypeConversions {

  implicit def toFrequence(value: nl.dpes.core.domain.Frequenties.Frequentie): nl.dpes.protocol.v1.Frequence = value match {
    case Frequenties.Nooit     => NEVER
    case Frequenties.Dagelijks => DAILY
    case Frequenties.Wekelijks => WEEKLY
    case _                     => nl.dpes.protocol.v1.Frequence.Unrecognized(value.id)
  }

  implicit def toSite(value: Sites.Site): nl.dpes.protocol.v1.Site = value match {
    case Sites.Iol => IOL
    case Sites.Itb => ITB
    case Sites.Nvb => NVB
    case _         => nl.dpes.protocol.v1.Site.Unrecognized(value.id)
  }
}
