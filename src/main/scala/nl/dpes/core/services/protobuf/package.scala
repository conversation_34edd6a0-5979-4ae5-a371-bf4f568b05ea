package nl.dpes.core.services

import nl.dpes.protocol.v1.{Envelope => GeneratedEnvelope}

import com.google.protobuf.timestamp.Timestamp

package object protobuf {

  def mapEnvelope[A](envelope: Envelope[A]): GeneratedEnvelope.Payload => GeneratedEnvelope = GeneratedEnvelope(
    envelope.correlationId,
    Some(Timestamp(envelope.timestamp.toEpochMilli / 1000, (envelope.timestamp.toEpochMilli % 1000).toInt * 1000000)),
    envelope.source,
    classOf[GeneratedEnvelope].getPackage.getSpecificationVersion,
    None,
    _: GeneratedEnvelope.Payload
  )
}
