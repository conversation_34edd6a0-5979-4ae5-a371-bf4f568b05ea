package nl.dpes.core.services.protobuf

import nl.dpes.core.domain.Sites
import nl.dpes.protocol.v1
import nl.dpes.protocol.v1.Envelope.Payload
import nl.dpes.protocol.v1.Envelope.Payload.{JobseekerModifiedEmailAddress, JobseekerRegistered}
import nl.dpes.protocol.v1.Password

object WerkzoekendeMappers {

  import nl.dpes.core.domain.Werkzoekende._
  import nl.dpes.core.protocol.v1.Jobseeker._
  import Envelope._

  implicit object AccountGeimporteerdProtobufMapper extends ProtobufWriter[AccountGeimporteerd] {

    import TypeConversions._

    override def toProto(envelope: Envelope[AccountGeimporteerd]): v1.Envelope =
      mapEnvelope(envelope)(
        Payload.JobseekerAccountImported(
          AccountImported(
            envelope.payload.werkzoekendeId,
            envelope.payload.sanDiegoId,
            envelope.payload.eMailadres,
            envelope.payload.site,
            Option(Password(envelope.payload.wachtwoord.hash, envelope.payload.wachtwoord.salt))
          )
        )
      )
  }

  implicit object AccountObgezegdProtobufMapper extends ProtobufWriter[AccountOpgezegd] {

    override def toProto(envelope: Envelope[AccountOpgezegd]): v1.Envelope =
      mapEnvelope(envelope)(
        Payload.JobseekerAccountCanceled(
          AccountCanceled(
            envelope.payload.werkzoekendeId,
            envelope.payload.reden
          )
        )
      )
  }

  implicit object EMailadresWijzigingVerzochtProtobufMapper extends ProtobufWriter[EMailadresWijzigingVerzocht] {

    import TypeConversions._

    override def toProto(envelope: Envelope[EMailadresWijzigingVerzocht]): v1.Envelope =
      mapEnvelope(envelope)(
        Payload.JobseekerRequestedEmailAddressModification(
          RequestedEmailAddressModification(
            envelope.payload.werkzoekendeId,
            envelope.payload.site,
            envelope.payload.nieuwEMailadres,
            envelope.payload.verificatieUrl,
            envelope.payload.verificatieToken,
            envelope.payload.verificatieTokenId
          )
        )
      )
  }

  implicit object GeverifieerdProtobufMapper extends ProtobufWriter[Geverifieerd] {

    override def toProto(envelope: Envelope[Geverifieerd]): v1.Envelope =
      mapEnvelope(envelope)(
        Payload.JobseekerVerified(
          Verified(
            envelope.payload.werkzoekendeId
          )
        )
      )
  }

  implicit object OpzeggingVerzochtProtobufMapper extends ProtobufWriter[OpzeggingVerzocht] {

    override def toProto(envelope: Envelope[OpzeggingVerzocht]): v1.Envelope =
      mapEnvelope(envelope)(
        Payload.JobseekerRequestedCancellation(
          RequestedCancellation(
            envelope.payload.werkzoekendeId,
            envelope.payload.sanDiegoId,
            envelope.payload.reden
          )
        )
      )
  }

  implicit object WachtwoordIngesteldProtobufMapper extends ProtobufWriter[WachtwoordIngesteld] {

    override def toProto(envelope: Envelope[WachtwoordIngesteld]): v1.Envelope =
      mapEnvelope(envelope)(
        Payload.JobseekerSetPassword(
          SetPassword(
            envelope.payload.werkzoekendeId,
            Option(Password(envelope.payload.wachtwoord.hash, envelope.payload.wachtwoord.salt))
          )
        )
      )
  }

  implicit object WachtwoordOpnieuwIngesteldProtobufMapper extends ProtobufWriter[WachtwoordOpnieuwIngesteld] {

    override def toProto(envelope: Envelope[WachtwoordOpnieuwIngesteld]): v1.Envelope =
      mapEnvelope(envelope)(
        Payload.JobseekerResetPassword(
          ResetPassword(
            envelope.payload.werkzoekendeId,
            Option(Password(envelope.payload.wachtwoord.hash, envelope.payload.wachtwoord.salt))
          )
        )
      )
  }

  implicit object WachtwoordVergetenProtobufMapper extends ProtobufWriter[WachtwoordVergeten] {

    import TypeConversions._

    override def toProto(envelope: Envelope[WachtwoordVergeten]): v1.Envelope =
      mapEnvelope(envelope)(
        Payload.JobseekerForgotPassword(
          ForgotPassword(
            envelope.payload.werkzoekendeId,
            envelope.payload.eMailadres,
            envelope.payload.site,
            envelope.payload.herstelUrl,
            envelope.payload.herstelToken,
            envelope.payload.herstelTokenId,
            envelope.payload.customTemplate.getOrElse("")
          )
        )
      )
  }

  implicit object GeregistreerdProtobufMapper extends ProtobufMapper[Geregistreerd] {

    import TypeConversions._

    override def toProto(envelope: Envelope[Geregistreerd]): v1.Envelope =
      mapEnvelope(envelope)(
        Payload.JobseekerRegistered(
          Registered(
            envelope.payload.werkzoekendeId,
            envelope.payload.eMailadres,
            envelope.payload.site,
            envelope.payload.verificatieUrl,
            envelope.payload.verificatieToken
          )
        )
      )

    override def fromProto(envelope: v1.Envelope): Envelope[Geregistreerd] =
      if (envelope.payload.isJobseekerRegistered) {
        val payload: Registered = envelope.payload.asInstanceOf[JobseekerRegistered].value
        Envelope(
          envelope.correlationId,
          envelope.timestamp,
          envelope.source,
          Geregistreerd(
            payload.jobseekerId,
            payload.emailAddress,
            payload.site match {
              case _ if payload.site.isIol => Sites.Iol
              case _ if payload.site.isNvb => Sites.Nvb
              case _ if payload.site.isItb => Sites.Itb
              case x                       => Sites.stringToSite(x.name)
            },
            payload.verificationUrl,
            payload.verificationToken
          )
        )
      } else {
        throw new UnsupportedOperationException("Not implemented")
      }
  }

  implicit object EMailadresGewijzigdProtobufMapper extends ProtobufMapper[EMailadresGewijzigd] {

    override def fromProto(envelope: v1.Envelope): Envelope[EMailadresGewijzigd] =
      if (envelope.payload.isJobseekerModifiedEmailAddress) {
        val payload: ModifiedEmailAddress = envelope.payload.asInstanceOf[JobseekerModifiedEmailAddress].value
        Envelope(
          envelope.correlationId,
          envelope.timestamp,
          envelope.source,
          EMailadresGewijzigd(payload.jobseekerId, payload.newEmailAddress)
        )
      } else {
        throw new UnsupportedOperationException("Not implemented")
      }

    override def toProto(envelope: Envelope[EMailadresGewijzigd]): v1.Envelope =
      mapEnvelope(envelope)(
        Payload.JobseekerModifiedEmailAddress(
          ModifiedEmailAddress(
            envelope.payload.werkzoekendeId,
            envelope.payload.nieuwEMailadres
          )
        )
      )
  }
}
