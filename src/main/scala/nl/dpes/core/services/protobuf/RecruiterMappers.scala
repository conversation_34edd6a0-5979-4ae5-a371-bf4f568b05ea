package nl.dpes.core.services.protobuf

import nl.dpes.protocol.v1
import nl.dpes.protocol.v1.Envelope.Payload

object RecruiterMappers {

  import nl.dpes.core.domain.Recruiter._
  import nl.dpes.core.protocol.v1.Recruiter._

  implicit object GeimporteerdProtobufMapper extends ProtobufWriter[Geimporteerd] {

    import TypeConversions._

    override def toProto(envelope: Envelope[Geimporteerd]): v1.Envelope =
      mapEnvelope(envelope)(
        Payload.RecruiterImported(
          Imported(
            envelope.payload.recruiterId,
            envelope.payload.sanDiegoId,
            envelope.payload.eMailadres,
            envelope.payload.site
          )
        )
      )
  }

  implicit object EMailadresGewijzigdProtobufMapper extends ProtobufWriter[EMailadresGewijzigd] {

    override def toProto(envelope: Envelope[EMailadresGewijzigd]): v1.Envelope =
      mapEnvelope(envelope)(
        Payload.RecruiterChangedEmailAddress(
          ChangedEmailAddress(
            envelope.payload.recruiterId,
            envelope.payload.eMailadres
          )
        )
      )
  }

  implicit object FavorietGemaaktProtobufMapper extends ProtobufWriter[FavorietGemaakt] {

    override def toProto(envelope: Envelope[FavorietGemaakt]): v1.Envelope =
      mapEnvelope(envelope)(
        Payload.RecruiterMadeFavorite(
          MadeFavorite(
            envelope.payload.recruiterId,
            envelope.payload.werkzoekendeId
          )
        )
      )
  }

  implicit object FavorietVerwijderdProtobufMapper extends ProtobufWriter[FavorietVerwijderd] {

    override def toProto(envelope: Envelope[FavorietVerwijderd]): v1.Envelope =
      mapEnvelope(envelope)(
        Payload.RecruiterRemovedAsFavorite(
          RemovedAsFavorite(
            envelope.payload.recruiterId,
            envelope.payload.werkzoekendeId
          )
        )
      )
  }
}
