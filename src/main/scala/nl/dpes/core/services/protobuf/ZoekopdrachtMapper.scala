package nl.dpes.core.services.protobuf

import scala.reflect.runtime.universe._

import nl.dpes.core.domain.{<PERSON><PERSON><PERSON><PERSON><PERSON>, Zoektermen}
import nl.dpes.protocol.v1
import nl.dpes.protocol.v1.Envelope.Payload

object ZoekopdrachtMapper {

  import nl.dpes.core.domain.Zoekopdracht._
  import nl.dpes.core.protocol.v1.SearchRequest._
  import TypeConversions._

  implicit object VerwijderdDoorRecruiterProtobufMapper extends ProtobufWriter[VerwijderdDoorRecruiter] {

    override def toProto(envelope: Envelope[VerwijderdDoorRecruiter]): v1.Envelope =
      mapEnvelope(envelope)(
        Payload.SearchrequestRemovedByRecruiter(
          RemovedByRecruiter(
            envelope.payload.zoekopdrachtId,
            envelope.payload.recruiterId
          )
        )
      )
  }

  implicit object OpgeslagenDoorRecruiterProtobufMapper extends ProtobufWriter[OpgeslagenDoorRecruiter] {

    override def toProto(envelope: Envelope[OpgeslagenDoorRecruiter]): v1.Envelope =
      mapEnvelope(envelope)(
        Payload.SearchrequestStoredByRecruiter(
          StoredByRecruiter(
            envelope.payload.zoekopdrachtId,
            envelope.payload.recruiterId,
            envelope.payload.naam,
            envelope.payload.frequentie,
            envelope.payload.zoekparameters
          )
        )
      )
  }

  implicit object AangemaaktDoorRecruiterProtobufMapper extends ProtobufWriter[AangemaaktDoorRecruiter] {

    override def toProto(envelope: Envelope[AangemaaktDoorRecruiter]): v1.Envelope =
      mapEnvelope(envelope)(
        Payload.SearchrequestCreatedByRecruiter(
          CreatedByRecruiter(
            envelope.payload.zoekopdrachtId,
            envelope.payload.zoekparameters
          )
        )
      )
  }

  def classAccessors[T: TypeTag]: List[MethodSymbol] = typeOf[T].members.collect {
    case m: MethodSymbol if m.isCaseAccessor => m
  }.toList

  def classAccessorsToMap[T: TypeTag](value: T): Map[String, Any] =
    classAccessors[T]
      .map { accessor =>
        accessor.name.toString -> value.getClass.getDeclaredMethod(accessor.name.toString).invoke(value)
      }
      .toMap
      .filter { t =>
        t._2 match {
          case x: Option[_] if x.isEmpty => false
          case _                         => true
        }
      }

  implicit def toSearchParameters(value: Zoekparameters): Map[String, SearchParameterValues] = Option(value) match {
    case None => Map()
    case Some(_) =>
      classAccessorsToMap(value)
        .foldLeft(Map.empty[String, SearchParameterValues]) { (map, field) =>
          field match {
            case (fieldName, fieldValue) =>
              fieldValue match {
                case Some(x: String)      => map + (fieldName -> SearchParameterValues(Seq(x)))
                case Some(x: Seq[String]) => map + (fieldName -> SearchParameterValues(x))
                case Some(x: Zoektermen)  => map ++ toSearchParameters(x)
                case _                    => map
              }
          }
        }
  }

  def toSearchParameters(value: Zoektermen): Map[String, SearchParameterValues] = Option(value) match {
    case None => Map()
    case Some(_) =>
      classAccessorsToMap(value)
        .foldLeft(Map.empty[String, SearchParameterValues]) { (map, field) =>
          field match {
            case (fieldName, fieldValue) =>
              fieldValue match {
                case Some(x: Seq[String]) => map + ("term." + fieldName -> SearchParameterValues(x))
                case _                    => map
              }
          }
        }
  }

  implicit object VerwijderdProtobufMapper extends ProtobufWriter[Verwijderd] {

    override def toProto(envelope: Envelope[Verwijderd]): v1.Envelope =
      mapEnvelope(envelope)(
        Payload.SearchrequestRemoved(
          Removed(
            envelope.payload.zoekopdrachtId
          )
        )
      )
  }

  implicit object GewijzigdProtobufMapper extends ProtobufWriter[Gewijzigd] {

    override def toProto(envelope: Envelope[Gewijzigd]): v1.Envelope =
      mapEnvelope(envelope)(
        Payload.SearchrequestChanged(
          Changed(
            envelope.payload.zoekopdrachtId,
            envelope.payload.recruiterId,
            envelope.payload.naam,
            envelope.payload.frequentie
          )
        )
      )
  }

}
