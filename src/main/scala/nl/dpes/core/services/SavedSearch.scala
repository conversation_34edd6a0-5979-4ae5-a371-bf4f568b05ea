package nl.dpes.core.services

import cats.data.EitherT
import nl.dpes.b2b.domain.{GenericError, ValidationError}
import nl.dpes.b2b.salesforce.service.RecruiterService
import nl.dpes.core.domain.Frequenties.Frequentie
import nl.dpes.core.domain.Zoekopdracht.{Maak<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, SlaOpDoorRecruiter, VerwijderDoorRecruiter}
import nl.dpes.core.domain._
import nl.dpes.core.domain.exceptions.AccountNietGevonden
import nl.dpes.core.projections.core.{CoreRecruiterAccountProjections, CoreZoekopdrachtProjections}
import nl.dpes.core.projections.index.IndexOpgeslagenZoekopdrachtProjections
import nl.dpes.core.projections.index.IndexOpgeslagenZoekopdrachtProjections.IndexOpgeslagenZoekopdracht
import nl.dpes.core.services.JobSeekerService.JobSeeker
import nl.dpes.core.services.ndsm.JobSeekerServiceClient
import nl.dpes.common.async._
import nl.dpes.core.services.MailService.OpgeslagenZoekopdrachtMail
import org.axonframework.commandhandling.gateway.CommandGateway
import org.slf4j.Logger

import scala.concurrent.ExecutionContext.Implicits.global
import scala.concurrent.Future

class SavedSearch(
  zoekopdrachtProjections: CoreZoekopdrachtProjections,
  opgeslagenZoekopdrachtProjections: IndexOpgeslagenZoekopdrachtProjections,
  recruiterAccountProjections: CoreRecruiterAccountProjections,
  identifierService: IdentifierService,
  commandGateway: CommandGateway,
  mailService: MailService,
  jobSeekerService: JobSeekerService,
  recruiterService: RecruiterService
)(implicit private val logger: Logger) {

  def saveByRecruiter(recruiterId: String, zoekparameters: Zoekparameters, naam: String, frequentie: Frequentie): Future[String] =
    recruiterAccountProjections.findByRecruiterId(recruiterId) match {
      case None => Future.failed(AccountNietGevonden(recruiterId, Rollen.Recruiter))
      case _ =>
        val zoekopdrachtId = zoekopdrachtProjections.findByParameters(zoekparameters) match {
          case None =>
            val zoekopdrachtId = identifierService.generateUUID()
            commandGateway.sendAndWait[Unit](MaakAanDoorRecruiter(zoekopdrachtId, zoekparameters))
            zoekopdrachtId
          case Some(coreZoekopdracht) => coreZoekopdracht.zoekopdrachtId
        }

        commandGateway.send[Unit](SlaOpDoorRecruiter(zoekopdrachtId, recruiterId, naam, frequentie)).map(_ => zoekopdrachtId)
    }

  def editByRecruiter(recruiterId: String, zoekopdrachtId: String, naam: String, frequentie: Frequentie): Future[String] =
    recruiterAccountProjections.findByRecruiterId(recruiterId) match {
      case None => Future.failed(AccountNietGevonden(recruiterId, Rollen.Recruiter))
      case _    => commandGateway.send[Unit](SlaOpDoorRecruiter(zoekopdrachtId, recruiterId, naam, frequentie)).map(_ => zoekopdrachtId)
    }

  def deleteByRecruiter(zoekopdrachtId: String, recruiterId: String): Future[Unit] =
    Future(commandGateway.sendAndWait[Unit](VerwijderDoorRecruiter(zoekopdrachtId, recruiterId)))

  def searchAndSend(zoekopdrachtId: String, recruiterId: String): Future[Unit] = {

    def sendMail(
      opgeslagenZoekopdracht: IndexOpgeslagenZoekopdracht,
      recruiter: CoreRecruiterAccountProjections.CoreRecruiterAccount,
      searchResult: JobSeekerServiceClient.SearchResult[JobSeeker]
    ) =
      mailService.send(
        OpgeslagenZoekopdrachtMail(
          EMailadres.stringToEMailadres(recruiter.eMailadres),
          Sites.stringToSite(recruiter.site),
          recruiter,
          searchResult.result.toVector,
          opgeslagenZoekopdracht
        )
      )

    def toEither[T](value: Option[T], message: String): Either[String, T] = value match {
      case Some(x) => Right(x)
      case None    => Left(message)
    }

    (for {
      recruiter <- toEither(
        recruiterAccountProjections.findByRecruiterId(recruiterId),
        "recruiter not found"
      )
      opgeslagenZoekopdracht <- toEither(
        opgeslagenZoekopdrachtProjections.findBySavedSearchId(recruiterId, zoekopdrachtId),
        "saved search not found"
      )
      zoekparameters <- toEither(
        Option(
          opgeslagenZoekopdracht.zoekparameters.copy(
            wijzigingsdatum = Some(Frequenties.frequentieToWijzigingsDatumFilter(opgeslagenZoekopdracht.frequentie))
          )
        ),
        "search parameters cannot be constructed"
      )
    } yield recruiterService.getRecruiterEmailById(recruiterId).flatMap {
      case Right(recruiterEmailAddress) =>
        jobSeekerService.search(zoekparameters).flatMap { searchResult =>
          if (searchResult.resultCount > 0) {
            sendMail(opgeslagenZoekopdracht, recruiter.copy(eMailadres = recruiterEmailAddress), searchResult)
          } else {
            logger.info(s"No results found for saved search by recruiterId=$recruiterId")
            Future.unit
          }
        }
      case Left(_) =>
        logger.info(s"Recruiter was not found. recruiterId=$recruiterId")
        recruiterService.isRecruiterDeleted(recruiterId).flatMap {
          case Right(true) =>
            deleteByRecruiter(opgeslagenZoekopdracht.zoekopdrachtId, recruiterId)
          case Right(false) =>
            logger.info(
              s"Skipping saved search because email address from recruiterId=$recruiterId is currently not available but contact is still valid"
            )
            Future.unit
          case Left(GenericError(errorCode, message, _)) =>
            logger.warn(s"Error trying to check if contact was deleted on saved search: $message")
            Future.unit
          case Left(ValidationError(_)) =>
            logger.warn(s"Validation error on saved search")
            Future.unit
        }
    }) match {
      case Right(c) => c
      case Left(m) =>
        Future.failed(
          new RuntimeException(
            s"Sending recruiter opgeslagen zoekopdracht failed. Recruiter $recruiterId, zoekopdracht $zoekopdrachtId with message $m"
          )
        )
    }
  }
}
