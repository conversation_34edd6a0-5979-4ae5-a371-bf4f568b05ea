package nl.dpes.core.services

import io.axoniq.gdpr.cryptoengine.CryptoEngine
import nl.dpes.core.domain.Recruiter
import nl.dpes.core.domain.Werkzoekende.AccountOpgezegd
import org.axonframework.eventhandling.EventHandler

class GDPRService(cryptoEngine: CryptoEngine) {

  @EventHandler
  def onAccountOpgezegd(event: AccountOpgezegd): Unit =
    cryptoEngine.deleteKey(event.werkzoekendeId)

  def onRecruiterVerwijderd(event: Recruiter.Verwijderd): Unit =
    cryptoEngine.deleteKey(event.recruiterId)
}
