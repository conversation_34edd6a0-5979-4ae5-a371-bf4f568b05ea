package nl.dpes.core.services.mail

import akka.http.scaladsl.marshallers.sprayjson.SprayJsonSupport
import nl.dpes.core.domain.EMailadres
import nl.dpes.core.services.MailService.Mail
import nl.dpes.common.marshalling.{JsonDateTimeSupport, JsonMapSupport}
import spray.json.{deserializationError, DefaultJsonProtocol, JsNull, JsObject, JsString, JsValue, RootJsonFormat}

trait JsonMailSupport extends SprayJsonSupport with DefaultJsonProtocol with <PERSON>sonDateTimeSupport with JsonMapSupport {

  def jsonMail(): RootJsonFormat[Mail] = new RootJsonFormat[Mail] {
    override def read(json: JsValue): Mail = deserializationError("Deserialization is not supported")

    override def write(mail: Mail): JsValue = JsObject(
      Map(
        "type"      -> JsString(mail.`type`),
        "recipient" -> JsString(mail.recipient),
        "parameters" -> JsObject(mail.parameters.map { case (key, value) =>
          key -> AnyJsonFormat.write(value)
        }),
        "subAccountId" -> mail.subAccountId.map(JsString(_)).getOrElse(JsNull),
        "metaData"     -> JsObject(mail.metaData.map { case (key, value) => key -> JsString(value) }),
        "sendAt"       -> mail.sendAt.map(x => JsString(x.toString)).getOrElse(JsNull)
      )
    )
  }

  implicit val eMailadresFormat: RootJsonFormat[EMailadres] = jsonFormat1(EMailadres.apply)
  implicit val MailFormat: RootJsonFormat[Mail]             = jsonMail()
}
