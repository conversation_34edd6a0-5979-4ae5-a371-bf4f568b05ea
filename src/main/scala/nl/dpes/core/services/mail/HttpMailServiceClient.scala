package nl.dpes.core.services.mail

import nl.dpes.core.services.MailService.Mail
import sttp.client3.sprayJson._
import sttp.client3.{basicRequest, SttpBackend, UriContext}
import sttp.model.{Header, MediaType, StatusCode}

import scala.concurrent.ExecutionContext.Implicits.global
import scala.concurrent.Future

class HttpMailServiceClient(baseUrl: String)(implicit
  val sttpBackend: SttpBackend[Future, _]
) extends MailServiceClient
    with JsonMailSupport {

  def send(mail: Mail): Future[Unit] =
    for {
      response <- basicRequest
        .put(uri"$baseUrl/mail")
        .headers(Header.contentType(MediaType.ApplicationJson))
        .body(mail)
        .send(sttpBackend)

      _ <- response.code match {
        case StatusCode.Ok => Future.unit
        case _             => Future.failed(new Exception("Sending mail failed: " + response.toString()))
      }
    } yield ()
}
