package nl.dpes.core.services.eventSubscriber

import nl.dpes.core.domain.Recruiter.{<PERSON><PERSON><PERSON>, WijzigEMailadres}
import nl.dpes.core.domain.Sites
import nl.dpes.salesforce.protocol.v1.{ContactCreated => PbContactCreated, ContactEmailAddressChanged => PbContactEmailAddressChanged}

package object protobufmarshaller {

  implicit val registreerMarshaller: ProtobufReader[Regis<PERSON><PERSON>, PbContactCreated] =
    contactCreated => Right(Registreer(contactCreated.contactId, contactCreated.emailAddress, Sites.Ndp))

  implicit val wijzigEMailadresMarshaller: ProtobufReader[WijzigEMailadres, PbContactEmailAddressChanged] =
    emailAddressChanged => Right(WijzigEMailadres(emailAddressChanged.contactId, emailAddressChanged.emailAddress))
}
