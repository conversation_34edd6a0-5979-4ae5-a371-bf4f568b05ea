package nl.dpes.core.services.eventSubscriber.protobufmarshaller

trait ProtobufReader[D, P] {
  def read(value: P): Either[ProtobufMarshaller.Error, D]
}

trait ProtobufWriter[D, P] {
  def write(value: D): P
}

trait ProtobufMarshaller[D, P] extends ProtobufReader[D, P] with ProtobufWriter[D, P]

object ProtobufMarshaller {
  type Error = String
  def apply[D, P](implicit marshaller: ProtobufMarshaller[D, P]): ProtobufMarshaller[D, P] = marshaller

  implicit class ReaderOps[P](value: P) {
    def toDomain[D](implicit marshaller: ProtobufReader[D, P]): Either[Error, D] = ProtobufMarshaller.toDomain(value)
  }

  implicit class WriterOps[D](value: D) {
    def toProtobuf[P](implicit marshaller: ProtobufWriter[D, P]): P = ProtobufMarshaller.toProtobuf(value)
  }

  def toDomain[D, P](value: P)(implicit marshaller: ProtobufReader[D, P]): Either[Error, D] = marshaller.read(value)

  def toProtobuf[D, P](value: D)(implicit marshaller: ProtobufWriter[D, P]): P = marshaller.write(value)
}
