package nl.dpes.core.services.eventSubscriber

import akka.NotUsed
import akka.stream.scaladsl.Flow
import nl.dpes.core.projections.core.CoreRecruiterAccountProjections
import nl.dpes.core.services.eventSubscriber.protobufmarshaller.ProtobufMarshaller._
import nl.dpes.protocol.v1.Envelope
import nl.dpes.protocol.v1.Envelope.Payload.{ContactCreated, ContactEmailAddressChanged}
import org.axonframework.commandhandling.gateway.CommandGateway

object KinesisMessageToCommandBus {

  def apply(gateway: CommandGateway, accountProjection: CoreRecruiterAccountProjections): Flow[Envelope, Unit, NotUsed] =
    Flow[Envelope]
      .map { message =>
        message.payload match {
          case ContactCreated(contactCreated) =>
            accountProjection
              .findByRecruiterId(contactCreated.contactId)
              .toRight("No recruiter with this id")
              .swap
              .flatMap(_ => contactCreated.toDomain)
              .foreach(gateway.sendAndWait[Any])
          case ContactEmailAddressChanged(contactEmailAddressChanged) =>
            accountProjection
              .findByRecruiterId(contactEmailAddressChanged.contactId)
              .toRight("No recruiter with this id")
              .flatMap(_ => contactEmailAddressChanged.toDomain)
              .foreach(gateway.sendAndWait[Any])
          case _ => ()
        }
      }
}
