package nl.dpes.core.services

import nl.dpes.core.services.security.PasswordHasher.HashedPassword
import nl.dpes.core.services.security.PasswordPolicy.PasswordValidationError
import nl.dpes.core.services.security.{PasswordHasher, PasswordPolicy}

class PasswordService(hasher: PasswordHasher, policy: PasswordPolicy) {

  def validatePassword(password: String): Either[PasswordValidationError, Unit] =
    policy.validate(password)

  def hashPassword(password: String): HashedPassword =
    hasher.hashPassword(password)

  def verifyHashedPassword(hashedPassword: HashedPassword, candidate: String): Boolean =
    hasher.verifyHashedPassword(hashedPassword, candidate)

  def verifyHashedPassword(hashedPassword: String, salt: String, candidate: String): Boolean =
    verifyHashedPassword(HashedPassword(hashedPassword, salt), candidate)

  def validateAndHashPassword(password: String): Either[PasswordValidationError, HashedPassword] =
    validatePassword(password).fold(
      error => Left(error),
      _ => Right(hashPassword(password))
    )
}
