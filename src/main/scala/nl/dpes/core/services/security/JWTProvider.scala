package nl.dpes.core.services.security

import com.auth0.jwt.algorithms.Algorithm
import com.auth0.jwt.exceptions._
import com.auth0.jwt.impl.NullClaim
import com.auth0.jwt.interfaces.{Claim, DecodedJWT}
import com.auth0.jwt.{JWTCreator, JWT => auth0JWT}

import scala.concurrent.duration.Duration
import scala.util.Try

object JWTProvider {
  sealed case class ClaimMissingException(claim: String) extends JWTVerificationException(s"Missing claim: $claim")

  private final val UserIdClaim   = "uid"
  private final val IssuedAtClaim = "iat"
  private final val RoleClaim     = "rol"
}

class JWTProvider(issuer: String, rsaKeys: RsaKeys, rsaKeyId: String) {
  import JWTProvider._

  private val algorithm = Algorithm.RSA256(rsaKeys.publicKey, rsaKeys.privateKey)

  def createAndSignJWTToken(userId: String, userRole: List[String], expiresIn: Duration, claims: Map[String, String]): String =
    claims.foldLeft[JWTCreator.Builder](createJWTToken(userId, userRole, new org.joda.time.Duration(expiresIn.toMillis))) {
      (builder, claim) =>
        builder.withClaim(claim._1, claim._2)
    } sign algorithm

  def createAndSignJWTToken(userId: String, userRole: List[String], expiresIn: Duration): String =
    createAndSignJWTToken(userId, userRole, expiresIn, Map.empty[String, String])

  def getClaimFromToken(token: String, claim: String): Either[Throwable, String] =
    decodeAndVerify(token).map(
      _.getClaim(claim).asString
    )

  def getOptionalClaimFromToken(token: String, claim: String): Either[Throwable, Option[String]] =
    decodeAndVerify(token).map(
      _.getClaim(claim) match {
        case _: NullClaim => None
        case v            => Some(v.asString)
      }
    )

  /** Create a JWT token but do not sign it yet.
    * This enables adding claims and sign it afterwards.
    */
  private def createJWTToken(userId: String, userRole: List[String], expiresIn: org.joda.time.Duration): JWTCreator.Builder = {
    val now              = new java.util.Date()
    val expirationMillis = now.getTime + expiresIn.getMillis
    val expirationDate   = new java.util.Date(expirationMillis)

    auth0JWT
      .create()
      .withIssuedAt(now)
      .withExpiresAt(expirationDate)
      .withIssuer(issuer)
      .withClaim(UserIdClaim, userId)
      .withClaim(RoleClaim, userRole.mkString(","))
      .withKeyId(rsaKeyId)
  }

  /** Decode and verify a token. This means the following:
    * - Successfully decoding with RSA256
    * - Making sure the token is not expired
    * - Making sure required headers are present in the token
    * - Making sure the issuer is set and has the proper value
    * - If provided, a role that should be present in the token
    */
  private def decodeAndVerify(token: String): Either[Throwable, DecodedJWT] =
    for {
      decoded <- Try {
        auth0JWT
          .require(algorithm)
          .withIssuer(issuer)
          .build()
          .verify(token)
      }.toEither
      _ <- verifyClaims(decoded)
    } yield decoded

  private def verifyClaims(decodedJWT: DecodedJWT): Either[Throwable, Unit] =
    for {
      _ <- asEither(IssuedAtClaim, decodedJWT.getClaim(IssuedAtClaim))
      _ <- asEither(RoleClaim, decodedJWT.getClaim(RoleClaim))
      _ <- asEither(UserIdClaim, decodedJWT.getClaim(UserIdClaim))
    } yield Right(())

  def checkHasRole(role: String)(implicit token: String): Either[Throwable, Boolean] =
    getRoles(token).map(_.contains(role))

  def getRoles(token: String): Either[Throwable, Seq[String]] =
    decodeAndVerify(token).map(
      _.getClaim(RoleClaim).asString
        .split(',')
    )

  def getUserID(token: String): Either[Throwable, String] =
    decodeAndVerify(token).map(
      _.getClaim(UserIdClaim).asString
    )

  private def asEither(name: String, claim: Claim): Either[ClaimMissingException, Unit] = claim match {
    case _: NullClaim =>
      Left(ClaimMissingException(name))
    case _ =>
      Right(())
  }
}
