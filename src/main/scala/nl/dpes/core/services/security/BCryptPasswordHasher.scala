package nl.dpes.core.services.security

import org.mindrot.jbcrypt.BCrypt
import org.slf4j.Logger

import scala.util.{Failure, Success, Try}

class BCryptPasswordHasher(logger: Logger) extends PasswordHasher {
  import PasswordHasher._

  override def hashPassword(password: String): HashedPassword = {
    val salt = BCrypt.gensalt()
    val hash = BCrypt.hashpw(password, salt)
    HashedPassword(hash, salt)
  }

  override def verifyHashedPassword(hashedPassword: HashedPassword, candidate: String): <PERSON><PERSON>an =
    Try(BCrypt.checkpw(candidate, hashedPassword.hash)) match {
      case Failure(exception) =>
        logger.error(s"The BCrypt hashing failed for the hash `${hashedPassword.hash}` with the error message `${exception.getMessage}``")
        false
      case Success(value) => value
    }
}
