package nl.dpes.core.services.security

object PasswordPolicy {

  sealed abstract class PasswordValidationError(message: String) extends Throwable {
    override def toString: String = message
  }

  case class TooShort(minimumLength: Int) extends PasswordValidationError(s"Password is too short; minimum length: $minimumLength")
}

abstract class PasswordPolicy {
  import PasswordPolicy._

  def validate(password: String): Either[PasswordValidationError, Unit]
}
