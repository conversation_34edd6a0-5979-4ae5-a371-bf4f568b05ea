package nl.dpes.core.services.security

import scala.util.{Failure, Success, Try}

package object tokens {
  case class InvalidTokenType(tokenType: String) extends RuntimeException(s"Invalid token type: $tokenType")

  object TokenTypes extends Enumeration {
    type TokenType = Value
    val AccessToken, VerificationToken, ResetPasswordToken, ChangeEmailToken, LoginAsToken = Value

    private val Index = Map(
      "access"        -> AccessToken,
      "verification"  -> VerificationToken,
      "resetPassword" -> ResetPasswordToken,
      "changeEmail"   -> ChangeEmailToken,
      "loginAs"       -> LoginAsToken
    )

    implicit def tokenTypeToString(tokenType: TokenType): String = (for {
      (key, value) <- Index if value == tokenType
    } yield key).head

    implicit def stringToTokenType(`type`: String): TokenType = Try(Index(`type`)) match {
      case Success(value) => value
      case Failure(_)     => throw InvalidTokenType(`type`)
    }
  }

  import TokenTypes._

  sealed abstract class Token(val tokenType: TokenType) {
    def userId: String
    def role: String
  }

  sealed trait TokenId {
    def tokenId: String
  }

  sealed trait PasswordToken extends Token

  sealed case class AccessToken(
    userId: String,
    sanDiegoId: Option[Int],
    role: String,
    emailAddress: Option[String] = None,
    site: Option[String] = None
  )                                                                 extends Token(TokenTypes.AccessToken)
  sealed case class VerificationToken(userId: String, role: String) extends Token(TokenTypes.VerificationToken) with PasswordToken

  sealed case class ResetPasswordToken(userId: String, role: String, tokenId: String)
      extends Token(TokenTypes.ResetPasswordToken)
      with TokenId
      with PasswordToken
  sealed case class ChangeEmailToken(userId: String, role: String, tokenId: String) extends Token(TokenTypes.ChangeEmailToken) with TokenId
  sealed case class LoginAsToken(userId: String, role: String)                      extends Token(TokenTypes.LoginAsToken)
}
