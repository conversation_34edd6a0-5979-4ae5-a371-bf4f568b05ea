package nl.dpes.core.services.security

import java.security.KeyFactory
import java.security.interfaces.{RSAPrivate<PERSON><PERSON>, RSAP<PERSON><PERSON><PERSON><PERSON>}
import java.security.spec.{PKCS8EncodedKeySpec, X509EncodedKeySpec}

import org.apache.commons.codec.binary.Base64

import scala.util.Try

case class RsaKeys(privateKey: RSAPrivateKey, publicKey: RSAPublicKey)

object RsaKeys {
  private val keyFactory = KeyFactory.getInstance("RSA")

  def fromStrings(privateKey: String, publicKey: String): Either[Throwable, RsaKeys] =
    Try(RsaKeys(rsaPrivateKeyFromString(privateKey), rsaPublicKeyFromString(publicKey))).toEither

  private def rsaPrivateKeyFromString(key: String): RSAPrivateKey =
    keyFactory
      .generatePrivate(
        new PKCS8EncodedKeySpec(
          Base64.decodeBase64(
            removeStringsFromKey(key, List("-----<PERSON><PERSON><PERSON> PRIVATE KEY-----", "-----<PERSON><PERSON> PRIVATE KEY-----"))
          )
        )
      )
      .asInstanceOf[RSAPrivateKey]

  private def rsaPublicKeyFromString(key: String): RSAPublicKey =
    keyFactory
      .generatePublic(
        new X509EncodedKeySpec(
          Base64.decodeBase64(
            removeStringsFromKey(key, List("-----BEGIN PUBLIC KEY-----", "-----END PUBLIC KEY-----"))
          )
        )
      )
      .asInstanceOf[RSAPublicKey]

  private def removeStringsFromKey(key: String, strings: List[String]): String =
    strings.foldLeft(key)((result, string) => result.replace(string, ""))
}
