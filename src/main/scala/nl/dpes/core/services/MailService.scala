package nl.dpes.core.services

import nl.dpes.core.domain.Sites._
import nl.dpes.core.domain.Werkzoekende.{EMailadresWijzigingVerzocht, Geregistreerd, GeregistreerdExternal, WachtwoordVergeten}
import nl.dpes.core.domain.{EMailadres, Sites, Url}
import nl.dpes.core.projections.core.CoreRecruiterAccountProjections.CoreRecruiterAccount
import nl.dpes.core.projections.index.IndexOpgeslagenZoekopdrachtProjections.IndexOpgeslagenZoekopdracht
import nl.dpes.core.services.JobSeekerService.JobSeeker
import nl.dpes.core.services.mail.MailServiceClient
import org.axonframework.eventhandling.EventHandler
import org.joda.time.DateTime
import org.slf4j.Logger

import scala.concurrent.{ExecutionContext, Future}
import scala.util.{Failure, Success}

object MailService {

  protected[services] trait Mail {
    val `type`: String
    val recipient: EMailadres
    val parameters: Map[String, Any]
    val subAccountId: Option[String]  = None
    val metaData: Map[String, String] = Map.empty
    val sendAt: Option[DateTime]      = None

    protected def formatType(format: String, site: String) = format.format(site)

    implicit def siteToString(site: Site): String = Map[Site, String](
      Iol -> "iol",
      Itb -> "itb",
      Nvb -> "nvb",
      Ndp -> "ndp"
    )(site)
  }

  sealed case class RegistratieMail(
    recipient: EMailadres,
    site: Site,
    verificatieUrl: Url,
    registratieToken: String
  ) extends Mail {
    override val `type`: String = formatType("registration mail %s", site)

    override val parameters: Map[String, String] = Map(
      "verificationurl" -> (verificatieUrl + ("token" -> registratieToken)),
      "emailaddress"    -> recipient
    )
  }

  sealed case class VerificatieHerinneringsMail(
    recipient: EMailadres,
    site: Site,
    verificatieUrl: Url,
    registratieToken: String,
    timeLeft: String
  ) extends Mail {
    override val `type`: String = formatType("verification reminder mail %s", site)

    override val parameters: Map[String, String] = Map(
      "verificationurl" -> (verificatieUrl + ("token" -> registratieToken)),
      "emailaddress"    -> recipient,
      "timeleft"        -> timeLeft
    )
  }

  sealed case class WachtwoordVergetenMail(
    recipient: EMailadres,
    site: Site,
    herstelUrl: Url,
    token: String,
    template: String = "reset password mail"
  ) extends Mail {
    override val `type`: String = formatType(s"$template %s", site)

    override val parameters: Map[String, String] = Map(
      "herstelurl"   -> herstelUrl.setQuery("token" -> token),
      "emailaddress" -> recipient
    )
  }

  sealed case class WijzigEMailadresMail(
    recipient: EMailadres,
    site: Site,
    verificatieUrl: Url,
    token: String
  ) extends Mail {
    override val `type`: String = formatType("change email address mail %s", site)

    override val parameters: Map[String, String] = Map(
      "verificationurl" -> (verificatieUrl + ("token" -> token)),
      "emailaddress"    -> recipient
    )
  }

  sealed case class OpgeslagenZoekopdrachtMail(
    recipient: EMailadres,
    site: Site,
    recruiter: CoreRecruiterAccount,
    jobSeekers: Vector[JobSeeker],
    opgeslagenZoekOpdracht: IndexOpgeslagenZoekopdracht
  ) extends Mail {
    override val `type`: String = formatType("recruiter saved search %s", site)

    private val siteShortname = site match {
      case Sites.Iol => "iol"
      case Sites.Itb => "itb"
      case Sites.Ndp => "ndp"
      case _         => "nvb"
    }

    override val parameters = Map(
      "recruiter" -> Map(
        "id" -> recruiter.recruiterId
      ),
      "candidateCount" -> jobSeekers.size,
      "jobSeekers" -> jobSeekers.map { jobSeeker =>
        val experiences = jobSeeker.experiences.toVector.map(experience => experience.jobTitle.getOrElse(""))

        Map(
          "id"                   -> jobSeeker.id,
          "functionName"         -> experiences.headOption.getOrElse(""),
          "experiences"          -> experiences.slice(1, 3), // function name is the first, then 2nd and 3rd most recent jobs
          "desiredJobs"          -> jobSeeker.preferredJobs.toVector,
          "workLevels"           -> jobSeeker.workLevel.getOrElse(""),
          "workingHours"         -> jobSeeker.workingHours.getOrElse(""),
          "availability"         -> jobSeeker.availability.getOrElse(""),
          "city"                 -> jobSeeker.city.getOrElse(""),
          s"site_$siteShortname" -> 1
        )
      },
      "search" -> Map(
        "id"   -> opgeslagenZoekOpdracht.zoekopdrachtId,
        "name" -> opgeslagenZoekOpdracht.naam
      )
    )
  }

  sealed case class OpgeslagenZoekopdrachtMailold(
    recipient: EMailadres,
    site: Site,
    parameters: Map[String, Any]
  ) extends Mail {
    override val `type`: String = formatType("recruiter saved search %s", site)
  }
}

class MailService(client: MailServiceClient)(implicit logger: Logger, executionContext: ExecutionContext) {
  import MailService._

  def send(mail: MailService.Mail): Future[Unit] = client.send(mail)

  @EventHandler
  def onGeregistreerd(event: Geregistreerd): Unit =
    client.send(RegistratieMail(event.eMailadres, event.site, event.verificatieUrl, event.verificatieToken)) onComplete {
      case Success(_) =>
      case Failure(e) =>
        logger.error(e.getMessage, e)
    }

  @EventHandler
  def onGeregistreerd(event: GeregistreerdExternal): Unit = {} // explicitly do nothing

  @EventHandler
  def onWachtwoordVergeten(event: WachtwoordVergeten): Unit =
    client.send(
      event.customTemplate
        .map(customTemplate => WachtwoordVergetenMail(event.eMailadres, event.site, event.herstelUrl, event.herstelToken, customTemplate))
        .getOrElse(
          WachtwoordVergetenMail(event.eMailadres, event.site, event.herstelUrl, event.herstelToken)
        )
    ) onComplete {
      case Success(_) =>
      case Failure(e) =>
        logger.error(e.getMessage, e)
    }

  @EventHandler
  def onWijzigEMailadres(event: EMailadresWijzigingVerzocht): Unit =
    client.send(WijzigEMailadresMail(event.nieuwEMailadres, event.site, event.verificatieUrl, event.verificatieToken)) onComplete {
      case Success(_) =>
      case Failure(e) =>
        logger.error(e.getMessage, e)
    }
}
