package nl.dpes.core.services.eventpublisher

import java.nio.ByteBuffer

import scala.util.Try

import nl.dpes.core.domain.Event
import nl.dpes.core.domain.Recruiter.RecruiterEvent
import nl.dpes.core.domain.Werkzoekende.WerkzoekendeEvent
import nl.dpes.core.domain.Zoekopdracht.ZoekopdrachtEvent
import nl.dpes.core.services.IdentifierService
import nl.dpes.core.services.protobuf.{Envelope, ProtobufWriter}

import com.amazonaws.services.kinesis.AmazonKinesis
import com.amazonaws.services.kinesis.model._
import org.axonframework.eventhandling.{EventHandler, Timestamp}
import org.slf4j.Logger

class KinesisEventPublisherService private (kinesisClient: AmazonKinesis, streamName: String)(implicit
  logger: Logger,
  identifierService: IdentifierService
) extends EventPublisherService {

  def sendEventToStream[T <: Event](event: Envelope[T], partitionKey: String)(implicit mapper: ProtobufWriter[T]): Unit = {
    logger.debug(s"Sending event '${event.payload.getClass.getCanonicalName}' to Amazon Kinesis Stream: '$streamName'")

    Try {
      kinesisClient.putRecord(streamName, ByteBuffer.wrap(mapper.toProto(event).toByteArray), partitionKey)
    } recover {
      case e: ResourceNotFoundException =>
        logger.error(
          "Error sending record to Amazon Kinesis: " +
          s"The requested resource could not be found; The stream might not be specified correctly ${e.getMessage}"
        )
      case e: InvalidArgumentException =>
        logger.error(
          "Error sending record to Amazon Kinesis: " +
          s"A specified parameter exceeds its restrictions is not supported or can 't be used. ${e.getMessage}"
        )
      case e: ProvisionedThroughputExceededException =>
        logger.error(
          "Error sending record to Amazon Kinesis: " +
          "The request rate for the stream is too high, or the requested data is too large for the available throughput. " +
          s"Reduce the frequency or size of your requests. ${e.getMessage}"
        )
      case e: KMSDisabledException =>
        logger.error(
          "Error sending record to Amazon Kinesis: " +
          s"The request was rejected because the specified customer master key(CMK) isn 't enabled. ${e.getMessage}"
        )
      case e: KMSInvalidStateException =>
        logger.error(
          "Error sending record to Amazon Kinesis: " +
          s"The request was rejected because the state of the specified resource isn't valid for this request. ${e.getMessage}"
        )
      case e: KMSAccessDeniedException =>
        logger.error(
          "Error sending record to Amazon Kinesis: " +
          s"The ciphertext references a key that doesn 't exist or that you don 't have access to. ${e.getMessage}"
        )
      case e: KMSNotFoundException =>
        logger.error(
          "Error sending record to Amazon Kinesis: " +
          s"The request was rejected because the specified entity or resource can 't be found. ${e.getMessage}"
        )
      case e: KMSOptInRequiredException =>
        logger.error(
          "Error sending record to Amazon Kinesis: " +
          s"The AWS access key ID needs a subscription for the service. ${e.getMessage}"
        )
      case e: KMSThrottlingException =>
        logger.error(
          "Error sending record to Amazon Kinesis: " +
          s"The request was denied due to request throttling.For more information about throttling. ${e.getMessage}"
        )
      case e => logger.error(s"Error sending record to Amazon Kinesis: ${e.getClass.getCanonicalName} ${e.getMessage}")
    }
  }

  @EventHandler
  def onWerkzoekendeEvent(event: WerkzoekendeEvent, @Timestamp timestamp: java.time.Instant): Unit = {
    import nl.dpes.core.domain.Werkzoekende._
    import nl.dpes.core.services.protobuf.WerkzoekendeMappers._

    def envelope[T] = Envelope(identifierService.generateUUID(), timestamp, "core", _: T)

    event match {
      case event: Geregistreerd                                                          => sendEventToStream(envelope(event), event.werkzoekendeId)
      case event: GeregistreerdExternal                                                  => // TODO sendEventToStream(envelope(event), event.werkzoekendeId)
      case event: AccountGeimporteerd                                                    => sendEventToStream(envelope(event), event.werkzoekendeId)
      case event: AccountOpgezegd                                                        => sendEventToStream(envelope(event), event.werkzoekendeId)
      case event: EMailadresGewijzigd                                                    => sendEventToStream(envelope(event), event.werkzoekendeId)
      case event: EMailadresWijzigingVerzocht                                            => sendEventToStream(envelope(event), event.werkzoekendeId)
      case event: Geverifieerd                                                           => sendEventToStream(envelope(event), event.werkzoekendeId)
      case event: OpzeggingVerzocht                                                      => sendEventToStream(envelope(event), event.werkzoekendeId)
      case event: WachtwoordIngesteld                                                    => sendEventToStream(envelope(event), event.werkzoekendeId)
      case event: WachtwoordIngesteldVoorExternalWerkzoekende                            => // TODO this needs to be added
      case event: WachtwoordOpnieuwIngesteld                                             => sendEventToStream(envelope(event), event.werkzoekendeId)
      case event: WachtwoordVergeten                                                     => sendEventToStream(envelope(event), event.werkzoekendeId)
      case _: IngeschrevenVoorEMail | _: UitgeschrevenVoorEMail | _: WachtwoordGewijzigd => //TODO: send remaining events to Kinesis
    }
  }

  @EventHandler
  def onRecruiterEvent(event: RecruiterEvent, @Timestamp timestamp: java.time.Instant): Unit = {
    import nl.dpes.core.domain.Recruiter._
    import nl.dpes.core.services.protobuf.RecruiterMappers._

    def envelope[T] = Envelope(identifierService.generateUUID(), timestamp, "core", _: T)

    event match {
      case event: Geimporteerd              => sendEventToStream(envelope(event), event.recruiterId)
      case event: EMailadresGewijzigd       => sendEventToStream(envelope(event), event.recruiterId)
      case event: FavorietGemaakt           => sendEventToStream(envelope(event), event.recruiterId)
      case event: FavorietVerwijderd        => sendEventToStream(envelope(event), event.recruiterId)
      case _: Verwijderd | _: Geregistreerd => ()
    }
  }

  @EventHandler
  def onZoekopdrachtEvent(event: ZoekopdrachtEvent, @Timestamp timestamp: java.time.Instant): Unit = {
    import nl.dpes.core.domain.Zoekopdracht._
    import nl.dpes.core.services.protobuf.ZoekopdrachtMapper._

    def envelope[T] = Envelope(identifierService.generateUUID(), timestamp, "core", _: T)

    event match {
      case event: VerwijderdDoorRecruiter => sendEventToStream(envelope(event), event.zoekopdrachtId)
      case event: OpgeslagenDoorRecruiter => sendEventToStream(envelope(event), event.zoekopdrachtId)
      case event: AangemaaktDoorRecruiter => sendEventToStream(envelope(event), event.zoekopdrachtId)
      case event: Verwijderd              => sendEventToStream(envelope(event), event.zoekopdrachtId)
      case event: Gewijzigd               => sendEventToStream(envelope(event), event.zoekopdrachtId)
    }
  }
}

object KinesisEventPublisherService {

  def fail(message: String)(implicit logger: Logger, identifierService: IdentifierService): Option[Nothing] = {
    logger.warn(message)
    None
  }

  def apply(kinesisClient: AmazonKinesis, streamName: String)(implicit
    logger: Logger,
    identifierService: IdentifierService
  ): Option[KinesisEventPublisherService] = {
    Try {
      if (kinesisClient.describeStream(streamName).getStreamDescription.getStreamStatus == "ACTIVE") {
        Some(new KinesisEventPublisherService(kinesisClient, streamName))
      } else {
        None
      }
    } recover {
      case _: ResourceNotFoundException => fail(s"Stream '$streamName' does not exist. Please create it in the console.")
      case e                            => fail(s"Error found while describing the stream '$streamName': ${e.getMessage}")
    }
  } get
}
