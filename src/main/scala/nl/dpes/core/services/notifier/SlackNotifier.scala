package nl.dpes.core.services.notifier

import nl.dpes.common.marshalling.JsonSupport
import nl.dpes.core.services.notifier.Notifier.Severities.Severity
import nl.dpes.core.services.notifier.SlackNotifier.{Colors, SlackMessage, SlackMessageAttachment, SlackMessageField}
import spray.json.{JsonFormat, RootJsonFormat}
import sttp.client3.{basicRequest, SttpBackend}
import sttp.model.{Header, MediaType, Uri}

import scala.concurrent.{ExecutionContext, Future}

object SlackNotifier {

  object Colors extends Enumeration {
    type Color = Value
    val Green, Orange, Red = Value

    implicit def colorToString(color: Color): String = color match {
      case Green  => "#6bb62c"
      case Orange => "#36a64f"
      case Red    => "#D00000"
    }
  }

  final case class SlackMessage(
    channel: String,
    username: String,
    attachments: List[SlackMessageAttachment],
    icon_emoji: Option[String] = None
  )
  final case class SlackMessageAttachment(fallback: String, pretext: String, color: String, fields: List[SlackMessageField])
  final case class SlackMessageField(title: String, value: String, short: Boolean)
}

class SlackNotifier(override protected val level: Severity, webHook: Uri, channel: String, username: String, icon: Option[String])(implicit
  sttpBackend: SttpBackend[Future, _]
) extends Notifier {
  import Notifier._

  private object JsonProtocol extends JsonSupport {
    implicit lazy val slackMessageFormat: RootJsonFormat[SlackMessage]                 = jsonFormat4(SlackMessage)
    implicit lazy val slackMessageAttachmentFormat: JsonFormat[SlackMessageAttachment] = jsonFormat4(SlackMessageAttachment)
    implicit lazy val slackMessageFieldFormat: JsonFormat[SlackMessageField]           = jsonFormat3(SlackMessageField)
  }

  import JsonProtocol._

  private implicit val executionContext: ExecutionContext = ExecutionContext.global

  override def notify(message: Message): Future[Unit] =
    if (shouldNotify(message.severity)) {
      for {
        json <- Future(slackMessageFormat.write(SlackMessage(channel, username, attachments(message), icon)).toString)
        _ <- basicRequest
          .post(webHook)
          .header(Header.contentType(MediaType.ApplicationXWwwFormUrlencoded))
          .body(Map("payload" -> json))
          .send(sttpBackend)
      } yield ()
    } else {
      Future.unit
    }

  private def attachments(message: Message): List[SlackMessageAttachment] =
    List(
      SlackMessageAttachment(
        message.title,
        message.title,
        color(message.severity),
        List(
          SlackMessageField(
            message.title,
            message.body,
            short = false
          )
        )
      )
    )

  private def color(severity: Severity): String = severity match {
    case Severities.Debug | Severities.Info => Colors.Green
    case Severities.Warning                 => Colors.Orange
    case _                                  => Colors.Red
  }
}
