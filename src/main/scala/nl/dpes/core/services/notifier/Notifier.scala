package nl.dpes.core.services.notifier

import nl.dpes.core.services.notifier.Notifier.Severities.Severity

import scala.concurrent.Future
import scala.util.{Failure, Success, Try}

object Notifier {
  sealed case class InvalidSeverity(severity: String) extends RuntimeException(s"Invalid severity: $severity")

  object Severities extends Enumeration {
    type Severity = Value
    val Debug, Info, Warning, Error, Critical, Alert, Emergency = Value

    val All: Seq[Severity] = Index.values.toList

    private lazy val Index = Map(
      "DEBUG"     -> Debug,
      "INFO"      -> Info,
      "WARNING"   -> Warning,
      "ERROR"     -> Error,
      "CRITICAL"  -> Critical,
      "ALERT"     -> <PERSON><PERSON>,
      "EMERGENCY" -> Emergency
    )

    def higherIncluding(severity: Severity): Seq[Severity] = severity match {
      case Debug     => Seq(Debug, Info, Warning, Error, Critical, Alert, Emergency)
      case Info      => higherIncluding(Debug).tail
      case Warning   => higherIncluding(Info).tail
      case Error     => higherIncluding(Warning).tail
      case Critical  => higherIncluding(Error).tail
      case Alert     => higherIncluding(Critical).tail
      case Emergency => higherIncluding(Alert).tail
    }

    implicit def stringToSeverity(severity: String): Severity = Try(Index(severity)) match {
      case Success(s) => s
      case Failure(_) => throw InvalidSeverity(severity)
    }
  }

  case class Message(title: String, body: String, severity: Severities.Severity)
}

trait Notifier {
  import nl.dpes.core.services.notifier.Notifier._

  protected def level: Severities.Severity
  private val notifyLevels = Severities.higherIncluding(level)

  protected def shouldNotify(severity: Severity): Boolean = notifyLevels.contains(severity)
  def notify(message: Message): Future[Unit]
}
