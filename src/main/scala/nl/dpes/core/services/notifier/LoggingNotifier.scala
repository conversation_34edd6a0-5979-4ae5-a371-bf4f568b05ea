package nl.dpes.core.services.notifier

import nl.dpes.core.services.notifier.Notifier.Severities
import org.slf4j.Logger

import scala.concurrent.{ExecutionContext, Future}

class LoggingNotifier(logger: Logger, override protected val level: Severities.Severity)(implicit executionContext: ExecutionContext)
    extends Notifier {

  override def notify(message: Notifier.Message): Future[Unit] = Future {
    if (shouldNotify(message.severity)) {
      val msg = s"${message.title}: ${message.body}"

      message.severity match {
        case Severities.Debug =>
          logger.debug(msg)
        case Severities.Info =>
          logger.info(msg)
        case Severities.Warning =>
          logger.warn(msg)
        case _ =>
          logger.error(msg)
      }
    }
  }
}
