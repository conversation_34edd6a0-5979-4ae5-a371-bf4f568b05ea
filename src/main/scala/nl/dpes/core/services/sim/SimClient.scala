package nl.dpes.core.services.sim

import nl.dpes.core.domain.Sites.Site
import nl.dpes.core.domain.werkzoekende.EMailinschrijvingen.EMailinschrijving
import org.joda.time.DateTime

import scala.concurrent.Future

object SimClient {

  final case class UserProfile(
    werkzoekendeId: String,
    emailAddress: String,
    site: String,
    origin: String,
    firstName: Option[String],
    lastName: Option[String],
    status: String,
    createdDateTime: Option[DateTime],
    modifiedDateTime: Option[DateTime]
  )
}

trait SimClient {
  import SimClient._

  def createOrUpdateUserProfile(profile: UserProfile): Future[Unit]

  def subscribe(userId: String, subscription: EMailinschrijving, site: Site): Future[Unit]

  def unsubscribe(userId: String, subscription: EMailinschrijving, site: Site): Future[Unit]

  def getSubscriptions(userId: String, site: Site): Future[SubscriptionStatus]

  def deleteProfile(userId: String): Future[Unit]
}
