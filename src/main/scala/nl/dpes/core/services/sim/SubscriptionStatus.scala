package nl.dpes.core.services.sim

import nl.dpes.common.marshalling.JsonSupport

case class SubscriptionStatus(
  personal: <PERSON><PERSON><PERSON>,
  partner: <PERSON><PERSON><PERSON>,
  newsletter: <PERSON><PERSON><PERSON>
)

object SubscriptionStatus extends JsonSupport {

  implicit val subscriptionStatusFmt                                             = jsonFormat3(SubscriptionStatus.apply)
  implicit val subscriptionStatusCirceCocdec: io.circe.Codec[SubscriptionStatus] = io.circe.generic.semiauto.deriveCodec[SubscriptionStatus]

}
