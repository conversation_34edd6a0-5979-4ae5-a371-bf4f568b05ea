package nl.dpes.core.services.sim

import nl.dpes.common.marshalling.JsonSupport
import nl.dpes.core.domain.Sites.Site
import nl.dpes.core.domain.werkzoekende.EMailinschrijvingen.EMailinschrijving
import nl.dpes.core.services.sim.SimClient.UserProfile
import spray.json.RootJsonFormat
import sttp.client3.sprayJson._
import sttp.client3.{basicRequest, SttpBackend, UriContext}
import sttp.model.{Header, MediaType}

import scala.concurrent.Future
import scala.concurrent.ExecutionContext.Implicits.global

class HttpSimClient(baseUrl: String)(implicit
  sttpBackend: SttpBackend[Future, _]
) extends SimClient {

  private object JsonProtocol extends JsonSupport {
    implicit lazy val userProfileFormat: RootJsonFormat[UserProfile] = jsonFormat9(UserProfile)
  }

  import JsonProtocol._

  override def createOrUpdateUserProfile(profile: UserProfile): Future[Unit] = {
    val uri = uri"$baseUrl/user-profiles/b2c"

    for {
      response <- basicRequest
        .put(uri)
        .header(Header.contentType(MediaType.ApplicationJson))
        .body(profile)
        .send(sttpBackend)
    } yield response.code match {
      case sttp.model.StatusCode.Accepted => Future.unit
      case _ =>
        throw new RuntimeException(s"Failed to create user profile in SIM: status ${response.statusText}")
    }
  }

  override def subscribe(userId: String, subscription: EMailinschrijving, site: Site): Future[Unit] = {
    val uri = uri"$baseUrl/user-profiles/b2c/$userId/subscriptions/$site/$subscription"

    for {
      response <- basicRequest
        .put(uri)
        .header(Header.contentType(MediaType.ApplicationJson))
        .send(sttpBackend)
    } yield response.code match {
      case sttp.model.StatusCode.Accepted => Future.unit
      case _ =>
        throw new RuntimeException(s"Failed to subscribe user in SIM: status ${response.statusText}")
    }
  }

  override def unsubscribe(userId: String, subscription: EMailinschrijving, site: Site): Future[Unit] = {
    val uri = uri"$baseUrl/user-profiles/b2c/$userId/unsubscriptions/$site/$subscription"

    for {
      response <- basicRequest
        .put(uri)
        .header(Header.contentType(MediaType.ApplicationJson))
        .send(sttpBackend)
    } yield response.code match {
      case sttp.model.StatusCode.Accepted => Future.unit
      case _ =>
        throw new RuntimeException(s"Failed to unsubscribe user in SIM: status ${response.statusText}")
    }
  }

  override def getSubscriptions(userId: String, site: Site): Future[SubscriptionStatus] = {
    val uri = uri"$baseUrl/user-profiles/b2c/$userId/subscriptions/$site"

    for {
      response <- basicRequest
        .get(uri)
        .header(Header.contentType(MediaType.ApplicationJson))
        .response(asJson[SubscriptionStatus])
        .send(sttpBackend)
    } yield response.body match {
      case Right(subscriptionStatus) => subscriptionStatus
      case _ =>
        throw new RuntimeException(s"Failed to get subscriptions for user in SIM: status ${response.statusText}")
    }
  }

  override def deleteProfile(userId: String): Future[Unit] = {
    val uri = uri"$baseUrl/user-profiles/b2c/$userId"

    for {
      response <- basicRequest
        .delete(uri)
        .send(sttpBackend)
    } yield response.code match {
      case sttp.model.StatusCode.Accepted => Future.unit
      case _ =>
        throw new RuntimeException(s"Failed to unsubscribe user in SIM: status ${response.statusText}")
    }
  }
}
