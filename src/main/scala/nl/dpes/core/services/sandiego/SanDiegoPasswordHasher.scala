package nl.dpes.core.services.sandiego

import java.io.ByteArrayInputStream
import java.nio.charset.Charset
import java.security.SecureRandom
import javax.xml.bind.DatatypeConverter

import nl.dpes.core.services.security.PasswordHasher
import nl.dpes.core.services.security.PasswordHasher.HashedPassword

import scala.io.Source
import scala.sys.process._

object SanDiegoPasswordHasher {
  private val SaltByteLength = 20
}

class SanDiegoPasswordHasher(phpExecutable: String) extends PasswordHasher {
  import SanDiegoPasswordHasher._

  private val script      = Source.fromResource("generate-sd-hash.php").mkString.getBytes(Charset.forName("UTF-8"))
  private lazy val random = new SecureRandom()

  private def base64Encode(value: String) =
    DatatypeConverter.printBase64Binary(value.getBytes(Charset.forName("UTF-8")))

  override def hashPassword(password: String): HashedPassword =
    hashPassword(password, generateSalt)

  def hashPassword(password: String, salt: String): HashedPassword = HashedPassword(
    (s"$phpExecutable -- ${base64Encode(password)} ${base64Encode(salt)}" #< new ByteArrayInputStream(script) !!).trim,
    salt
  )

  override def verifyHashedPassword(hashedPassword: HashedPassword, candidate: String): Boolean =
    hashPassword(candidate, hashedPassword.salt).equals(hashedPassword)

  private def generateSalt: String = {
    val salt = new Array[Byte](SaltByteLength)
    random.nextBytes(salt)
    salt.mkString
  }
}
