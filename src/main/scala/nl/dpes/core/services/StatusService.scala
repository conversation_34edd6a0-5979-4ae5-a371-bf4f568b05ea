package nl.dpes.core.services

import nl.dpes.core.services.StatusService.License
import org.joda.time.DateTime
import org.slf4j.Logger
import scalikejdbc._

import scala.concurrent.duration._
import scala.util.{Failure, Success, Try}

object StatusService {

  object ApplicationStatus extends Enumeration {
    type State = Value
    val Started, Starting, Stopping, Stopped, Error, Warning = Value
  }

  case class License(name: String, expires: DateTime)
}

class StatusService(val licenses: Set[License])(implicit session: DBSession, logger: Logger) {
  import StatusService._
  import ApplicationStatus._

  private var applicationState: State = ApplicationStatus.Starting

  def onApplicationStarting(): Unit = applicationState = ApplicationStatus.Starting
  def onApplicationStarted(): Unit  = applicationState = ApplicationStatus.Started
  def onApplicationStopping(): Unit = applicationState = ApplicationStatus.Stopping
  def onApplicationStopped(): Unit  = applicationState = ApplicationStatus.Stopped

  def getApplicationState: State =
    applicationState match {
      case Started =>
        (for {
          _ <- checkDatabase()
          _ <- checkLicenses()
        } yield true).fold(
          state => state,
          _ => Started
        )
      case state => state
    }

  private def checkDatabase(): Either[State, Boolean] =
    Try(sql"""SELECT 1 FROM dual""".execute.apply()) match {
      case Success(valid) if valid =>
        Right(true)
      case Failure(e) =>
        logger.error(e.getMessage, e)
        Left(Error)
      case _ =>
        logger.error("Database state is not valid")
        Left(Error)
    }

  private def checkLicenses(): Either[State, Boolean] = {
    def getTimeLeft(dateTime: DateTime): Duration =
      Duration(
        dateTime.minus(DateTime.now().getMillis).getMillis,
        MILLISECONDS
      )

    Right(true)
  }
}
