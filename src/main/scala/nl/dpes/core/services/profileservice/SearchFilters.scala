package nl.dpes.core.services.profileservice

case class SearchFilters(
  searchTerm: Option[String],
  city: Option[String],
  provinces: Option[Seq[String]],
  updatedDate: Option[String],
  functionGroups: Option[Seq[String]],
  workLevels: Option[Seq[String]],
  workingHours: Option[Seq[String]],
  careerLevels: Option[Seq[String]],
  requestedSalaries: Option[Seq[String]],
  availabilities: Option[Seq[String]],
  driversLicenses: Option[Seq[String]],
  languages: Option[Seq[String]]
)
