package nl.dpes.core.services

import nl.dpes.core.domain.Werkzoekende
import nl.dpes.core.services.sim.SimClient
import org.axonframework.eventhandling.EventHandler
import org.slf4j.Logger

import scala.concurrent.Future

class TrackingSimService(client: SimClient)(implicit private val logger: <PERSON>gger) {

  @EventHandler
  def onAccountOpgezegd(event: Werkzoekende.AccountOpgezegd): Future[Unit] =
    client.deleteProfile(event.werkzoekendeId)
}
