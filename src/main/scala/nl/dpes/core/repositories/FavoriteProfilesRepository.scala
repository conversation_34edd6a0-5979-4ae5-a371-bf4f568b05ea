package nl.dpes.core.repositories

import cats.effect._
import cats.implicits._
import doobie.implicits._
import doobie.{Fragment, Meta}
import doobie.util.transactor.Transactor
import org.typelevel.log4cats.LoggerFactory
import org.typelevel.log4cats.slf4j.Slf4jFactory

import java.sql.Timestamp
import java.time.Instant

class FavoriteProfilesRepository[F[_]: Async](tableName: String, transactor: Transactor[F]) {

  implicit val inst: Meta[Instant]        = Meta[Timestamp].imap(_.toInstant)(Timestamp.from)
  implicit val logger: Slf4jFactory[F]    = Slf4jFactory.create[F]
  private val tableNameFragment: Fragment = Fragment.const(tableName)

  def saveFavorite(dbRecruiterId: String, dbProfileId: String): F[Unit] =
    sql"""
        REPLACE INTO $tableNameFragment (recruiterId, profileId, timestamp)
        VALUES ($dbRecruiterId, $dbProfileId, ${Instant.now()})
       """.update.run.transact(transactor).void.handleErrorWith { thr =>
      LoggerFactory[F].getLogger.error(thr.getMessage) *>
      thr.raiseError[F, Unit]
    }

  def deleteFavorite(dbRecruiterId: String, dbProfileId: String): F[Unit] =
    sql"""
        DELETE FROM $tableNameFragment
        WHERE recruiterId = $dbRecruiterId AND profileId = $dbProfileId
       """.update.run.transact(transactor).void.handleErrorWith { thr =>
      LoggerFactory[F].getLogger.error(thr.getMessage) *>
      thr.raiseError[F, Unit]
    }
}
