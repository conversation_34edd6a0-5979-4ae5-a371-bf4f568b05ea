package nl.dpes.core.repositories

import com.amazonaws.services.dynamodbv2.document.{DynamoDB, Table}
import nl.dpes.core.config.Environment
import nl.dpes.core.repositories.VersionRepository.VersionInformation
import nl.dpes.utils.dynamodb.DynamoDBSupport
import nl.dpes.utils.dynamodb.DynamoDBSupport._
import spray.json.{DefaultJsonProtocol, RootJsonFormat}

object VersionRepository {
  final case class VersionInformation(name: String, version: Int, ready: <PERSON>olean)
}

class VersionRepository(override protected val db: DynamoDB)(implicit environment: Environment) extends Repository with DynamoDBSupport {

  private object JsonProtocol extends Default<PERSON><PERSON><PERSON><PERSON>ocol {
    implicit lazy val versionInformationFormat: RootJsonFormat[VersionInformation] = jsonFormat3(VersionInformation)
  }

  import JsonProtocol._

  private lazy val tableName                = s"ndp-versions_${environment.abbreviation}"
  private implicit lazy val versions: Table = table(tableName)
  override lazy val tables: Seq[String]     = Seq(versions.getTableName)

  override protected val primaryIndex = PrimaryIndex(HashField("name", DataTypes.String), Some(SortField("version", DataTypes.Number)))

  def findByNameAndVersion(name: String, version: Int): Option[VersionInformation] =
    findOnPrimaryIndex[VersionInformation](Identity("name", name), Some(Identity("version", version)))

  def updateVersion(name: String, updatedVersion: Int): Unit =
    insert[VersionInformation](
      Identity("name", name),
      VersionInformation(name, updatedVersion, ready = false),
      Identity("version", updatedVersion)
    )

  def finalizeVersion(name: String, version: Int): Unit =
    insert[VersionInformation](Identity("name", name), VersionInformation(name, version, ready = true), Identity("version", version))
}
