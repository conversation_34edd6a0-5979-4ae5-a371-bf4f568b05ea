package nl.dpes.core.repositories

import com.amazonaws.services.dynamodbv2.document.DynamoDB
import nl.dpes.utils.dynamodb.DynamoDBSupport
import nl.dpes.utils.dynamodb.DynamoDBSupport.{DataTypes, HashField, Identity, PrimaryIndex}
import nl.dpes.common.marshalling.JsonSupport
import nl.dpes.core.config.Environment
import org.slf4j.Logger
import spray.json.RootJsonFormat

object TokenRepository {
  sealed case class Token(name: String, owner: Option[String] = None)

  object EventStoreMaintenanceToken           extends Token("event-store-maintenance")
  object CoreProjectionsRebuildToken          extends Token("core-projections-rebuild")
  object IndexProjectionsRebuildToken         extends Token("index-projections-rebuild")
  object ElasticSearchProjectionsRebuildToken extends Token("elasticsearch-projections-rebuild")
}

class TokenRepository(override protected val db: DynamoDB, owner: String)(implicit logger: Logger, environment: Environment)
    extends Repository
    with DynamoDBSupport {
  import TokenRepository._

  private object JsonProtocol extends JsonSupport {
    implicit lazy val tokenFormat: RootJsonFormat[Token] = jsonFormat2(Token)
  }

  import JsonProtocol._

  private implicit lazy val tokens      = table(s"ndp-tokens_${environment.abbreviation}")
  override lazy val tables: Seq[String] = Seq(tokens.getTableName)

  override protected val primaryIndex = PrimaryIndex(HashField("name", DataTypes.String))

  def fetchToken(token: Token): Option[Token] =
    findOnPrimaryIndex[Token](Identity("name", token.name))

  def claim(token: Token): Boolean =
    fetchToken(token).map { lockedToken =>
      logger.warn(s"Failed to claim token '${token.name}'; it is already owned by '${lockedToken.owner.getOrElse("???")}'")
      false
    } getOrElse {
      insert[Token](Identity("name", token.name), token.copy(owner = Some(owner)))
      true
    }

  def release(token: Token): Unit =
    fetchToken(token).foreach { claimedToken =>
      if (claimedToken.owner.getOrElse("").equals(owner)) {
        delete(Identity("name", token.name))
        logger.info(s"${token.name} token released")
      } else {
        logger.warn(s"${token.name} token could not be released because it is owned by another node: ${token.owner.getOrElse("???")}")
      }
    }
}
