package nl.dpes.core.repositories

import cats.effect._
import cats.implicits._
import doobie.implicits._
import doobie.Fragment
import doobie.util.transactor.Transactor
import io.circe.syntax._
import io.circe.generic.auto._
import nl.dpes.core.services.profileservice.{Frequency, RecruiterId, SavedSearchId, SavedSearchName, SearchFilters}
import org.typelevel.log4cats.LoggerFactory
import org.typelevel.log4cats.slf4j.Slf4jFactory

class SavedSearchRepository[F[_]: Async](tableName: String, transactor: Transactor[F]) {

  implicit val logger: Slf4jFactory[F]    = Slf4jFactory.create[F]
  private val tableNameFragment: Fragment = Fragment.const(tableName)

  def create(savedSearchId: SavedSearchId, filters: SearchFilters): F[Unit] =
    sql"""
      REPLACE INTO $tableNameFragment (id, name, recruiterId, filters, frequency)
      VALUES (${savedSearchId.value}, '', '', ${filters.asJson.toString()}, 'Nooit')
    """.update.run.transact(transactor).void.handleErrorWith { thr =>
      LoggerFactory[F].getLogger.error(thr.getMessage) *>
      thr.raiseError[F, Unit]
    }

  def create(
    savedSearchId: SavedSearchId,
    savedSearchName: SavedSearchName,
    recruiterId: RecruiterId,
    filters: SearchFilters,
    frequency: Frequency
  ): F[Unit] =
    sql"""
      REPLACE INTO $tableNameFragment (id, name, recruiterId, filters, frequency)
      VALUES (${savedSearchId.value}, ${savedSearchName.value}, ${recruiterId.value}, ${filters.asJson.toString()}, ${frequency.value})
    """.update.run.transact(transactor).void.handleErrorWith { thr =>
      LoggerFactory[F].getLogger.error(thr.getMessage) *>
      thr.raiseError[F, Unit]
    }

  def updateFrequency(savedSearchId: SavedSearchId, frequency: Frequency): F[Unit] =
    sql"""
    UPDATE $tableNameFragment
    SET frequency = ${frequency.value}
    WHERE id = ${savedSearchId.value}
  """.update.run.transact(transactor).void.handleErrorWith { thr =>
      LoggerFactory[F].getLogger.error(thr.getMessage) *>
      thr.raiseError[F, Unit]
    }

  def delete(savedSearchId: SavedSearchId): F[Unit] =
    sql"""
    DELETE FROM $tableNameFragment
    WHERE id = ${savedSearchId.value}
    """.update.run.transact(transactor).void.handleErrorWith { thr =>
      LoggerFactory[F].getLogger.error(thr.getMessage) *>
      thr.raiseError[F, Unit]
    }

  def delete(savedSearchId: SavedSearchId, recruiterId: RecruiterId): F[Unit] =
    sql"""
    DELETE FROM $tableNameFragment
    WHERE id = ${savedSearchId.value} AND recruiterId = ${recruiterId.value}
    """.update.run.transact(transactor).void.handleErrorWith { thr =>
      LoggerFactory[F].getLogger.error(thr.getMessage) *>
      thr.raiseError[F, Unit]
    }
}
