package nl.dpes.core.projections

import com.sksamuel.elastic4s.{HitReader, Indexable, RefreshPolicy}
import com.sksamuel.elastic4s.http.{ElasticClient, RequestFailure, RequestSuccess, Response}
import com.sksamuel.elastic4s.searches.queries.Query
import nl.dpes.core.projections.Projections.ProjectionsType
import org.slf4j.Logger

import scala.concurrent.ExecutionContext.Implicits.global
import scala.concurrent.Future
import scala.reflect.ClassTag
import scala.util.{Failure, Success}

protected[projections] abstract class ElasticSearchProjections[T](
  index: String,
  indexType: String,
  client: ElasticClient,
  refreshPolicy: RefreshPolicy
)(implicit logger: Logger)
    extends Projections {
  import com.sksamuel.elastic4s.http.ElasticDsl._
  import ElasticSearchProjections._

  protected def projection(name: String, projectionsType: ProjectionsType): String =
    s"${projectionsType.prefix}_${name}_v${projectionsType.version}"

  def searchDocument[A](query: Query, from: Int, size: Int)(implicit hitReader: HitReader[A], ct: ClassTag[A]): Future[ResultSet[A]] = {
    val eventualSearchResult = client.execute(
      search(index) query query from from size size
    )

    eventualSearchResult.map[ResultSet[A]] {
      case RequestSuccess(_, _, _, result) => ResultSet(result.to[A], result.totalHits)
      case RequestFailure(status, body, _, _) =>
        logger.error(s"ElasticSearchProjections failed with status ($status ${body.getOrElse("")})")
        ResultSet(Seq.empty, 0)
    }
  }

  def insert(document: T)(implicit indexable: Indexable[T]): Future[Unit] = {
    val eventuallyInsertedResult = client.execute {
      indexInto(index / indexType).doc[T](document).refresh(refreshPolicy)
    }

    logErrors(eventuallyInsertedResult)
    eventuallyInsertedResult.map(_ => ())
  }

  def deleteDocument(query: Query): Future[Unit] = {
    val eventuallyDeletedResult = client.execute(
      deleteIn(index / indexType) by query refresh refreshPolicy
    )

    logErrors(eventuallyDeletedResult)
    eventuallyDeletedResult.map(_ => ())
  }

  private def logErrors(f: Future[Response[_]]): Unit =
    f onComplete {
      case Success(response) =>
        response match {
          case RequestFailure(status, body, _, _) =>
            logger.error(s"ElasticSearchProjections failed with status ($status ${body.getOrElse("")})")
          case _ =>
        }
      case Failure(e) =>
        logger.error(s"ElasticSearchProjections failed with message ${e.getMessage}", e)
    }

  protected def createIndexDefinition(): Unit

  override def initialize(): Unit = createIndexDefinition()
}

object ElasticSearchProjections {
  final case class ResultSet[A](items: Seq[A], totalCount: Long)
}
