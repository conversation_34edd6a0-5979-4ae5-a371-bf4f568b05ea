package nl.dpes.core.projections.rebuilding

import nl.dpes.core.services.notifier.Notifier
import org.slf4j.Logger

trait Reporter[T] {
  def debug(reporter: T, message: String): Unit
  def info(reporter: T, message: String): Unit
  def warn(reporter: T, message: String): Unit
  def error(reporter: T, message: String, cause: Throwable): Unit
}

object Reporter {
  def apply[T: Reporter]: Reporter[T] = implicitly

  implicit object LoggingReporter extends Reporter[Logger] {
    override def debug(logger: Logger, message: String): Unit                   = logger.debug(message)
    override def info(logger: Logger, message: String): Unit                    = logger.info(message)
    override def warn(logger: Logger, message: String): Unit                    = logger.warn(message)
    override def error(logger: Logger, message: String, cause: Throwable): Unit = logger.error(message, cause)
  }

  implicit object NotifyingReporter extends Reporter[Notifier] {
    val title = "Projections Rebuild"

    override def debug(notifier: Notifier, message: String): Unit = notifier.notify(
      Notifier.Message(title, message, Notifier.Severities.Debug)
    )

    override def info(notifier: Notifier, message: String): Unit = notifier.notify(
      Notifier.Message(title, message, Notifier.Severities.Info)
    )

    override def warn(notifier: Notifier, message: String): Unit = notifier.notify(
      Notifier.Message(title, message, Notifier.Severities.Warning)
    )

    override def error(notifier: Notifier, message: String, cause: Throwable): Unit = notifier.notify(
      Notifier.Message(title, message, Notifier.Severities.Error)
    )
  }
}
