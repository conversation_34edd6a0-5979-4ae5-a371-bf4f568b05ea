package nl.dpes.core.projections.rebuilding

import org.axonframework.eventhandling.DomainEventMessage
import org.axonframework.messaging.Message
import org.axonframework.monitoring.MessageMonitor

class Monitor[T](logger: T)(implicit reporter: Reporter[T]) extends MessageMonitor[Message[_]] {
  val info: String => Unit               = reporter.info(logger, _)
  val debug: String => Unit              = reporter.debug(logger, _)
  val warn: String => Unit               = reporter.warn(logger, _)
  val error: (String, Throwable) => Unit = reporter.error(logger, _, _)

  info(s"Start logging rebuild of projections with ${logger.getClass.getName}")

  override def onMessageIngested(message: Message[_]): MessageMonitor.MonitorCallback =
    new MessageMonitor.MonitorCallback {

      override def reportSuccess(): Unit =
        message match {
          case msg: DomainEventMessage[_] => debug(s"Event ${msg.getIdentifier} handled successfully")
          case _                          =>
        }

      override def reportFailure(cause: Throwable): Unit =
        message match {
          case msg: DomainEventMessage[_] =>
            error(s"Event ${msg.getIdentifier} handling FAILED! ${cause.getMessage}", cause)
          case _ =>
        }

      override def reportIgnored(): Unit =
        message match {
          case msg: DomainEventMessage[_] => warn(s"Event ${msg.getIdentifier} was IGNORED")
          case _                          =>
        }
    }
}
