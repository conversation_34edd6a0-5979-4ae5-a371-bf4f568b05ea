package nl.dpes.core.projections.rebuilding

import scala.util.matching.Regex

case class TokenEntry(name: String, version: Int, index: Long) extends Ordered[TokenEntry] {
  import scala.math.Ordered.orderingToOrdered
  override def compare(that: TokenEntry): Int = (name, version) compare (that.name, that.version)
}

object TokenEntry {
  lazy val namePattern: Regex = """nl\.dpes\.core\.projections\.([^-]*)(?:-v(\d+))?""".r

  def apply(tokenName: String, index: Long): TokenEntry =
    tokenName match {
      case namePattern(name, version) =>
        new TokenEntry(name, Option(version).getOrElse("1").toInt, index)
      case _ => new TokenEntry(tokenName, 1, index)
    }
}
