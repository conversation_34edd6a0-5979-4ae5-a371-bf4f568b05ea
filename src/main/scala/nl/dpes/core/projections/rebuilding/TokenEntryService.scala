package nl.dpes.core.projections.rebuilding

import com.thoughtworks.xstream.XStream
import nl.dpes.core.projections.Projections.Projection
import org.axonframework.eventhandling.GapAwareTrackingToken
import scalikejdbc.DBSession
import scalikejdbc._

class TokenEntryService(implicit dBSession: DBSession) {

  def getTokens: Seq[TokenEntry] = sql"""
      SELECT processorName, token
      FROM TokenEntry
      ORDER BY processorName
    """
    .map(rs =>
      for {
        name  <- Option(rs.string("processorName"))
        index <- Option(rs.string("token")).map(new XStream().fromXML(_).asInstanceOf[GapAwareTrackingToken].getIndex)
      } yield TokenEntry(name, index)
    )
    .list
    .apply()
    .flatten
    .sorted

  def getMaxGlobalIndex: Option[Long] = sql"""
      SELECT MAX(globalIndex)
      FROM DomainEventEntry
    """.map(_.long("globalIndex")).headOption.apply()

  def getToken(projection: Projection): Option[TokenEntry] = sql"""
      SELECT processorName, token
      FROM TokenEntry
      WHERE processorName LIKE "%%${projection.prefix}"
    """
    .map(rs =>
      for {
        name  <- Option(rs.string("processorName"))
        index <- Option(rs.string("token")).map(new XStream().fromXML(_).asInstanceOf[GapAwareTrackingToken].getIndex)
      } yield TokenEntry(name, index)
    )
    .list
    .headOption
    .apply()
    .flatten
}
