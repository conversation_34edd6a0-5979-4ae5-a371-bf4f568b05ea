package nl.dpes.core.projections.rebuilding

import nl.dpes.core.projections.Projections.Projection
import nl.dpes.core.repositories.{TokenRepository, VersionRepository}
import org.slf4j.Logger

import scala.annotation.tailrec
import scala.concurrent.duration._

sealed abstract class RebuildResult {

  def map(f: String => String): RebuildResult = this match {
    case RebuildInProgress(message) => RebuildInProgress(f(message))
    case _                          => this
  }

  def flatMap(f: String => RebuildResult): RebuildResult = this match {
    case RebuildInProgress(message) => f(message)
    case _                          => this
  }
}

final case class RebuildFailed(reason: String)      extends RebuildResult
final case class RebuildStopped(reason: String)     extends RebuildResult
final case class RebuildInProgress(message: String) extends RebuildResult

class ProjectionRebuilder(projection: Projection)(implicit
  logger: Logger,
  tokenRepository: TokenRepository,
  versionRepository: VersionRepository,
  tokenEntryService: TokenEntryService
) {

  def rebuild(): RebuildResult =
    for {
      _ <- claimToken()
      _ <- checkRebuildStatus()
//      _ <- waitForTracker()
//      _ <- finalizeVersion()
    } yield s"Still busy rebuilding ${projection.prefix} after finalizing version ${projection.version}"

  private def claimToken(): RebuildResult =
    if (tokenRepository.claim(projection.token)) {
      RebuildInProgress(s"Token ${projection.token.name} claimed")
    } else {
      RebuildFailed(s"Token ${projection.token.name} already claimed")
    }

  private def checkRebuildStatus(): RebuildResult =
    versionRepository.findByNameAndVersion(projection.prefix, projection.version).map { version =>
      if (!version.ready) {
        RebuildFailed(s"Rebuild ${projection.prefix} v${projection.version} already in progress")
      } else {
        RebuildStopped(s"Rebuild ${projection.prefix} v${projection.version} already done")
      }
    } getOrElse RebuildInProgress(s"Ready for rebuilding ${projection.prefix} v${projection.version}")

  @tailrec
  private def waitForTracker(): RebuildResult = {
    val progress: Long = tokenEntryService.getToken(projection).map(_.index).getOrElse(0)
    val target         = tokenEntryService.getMaxGlobalIndex.get
    if ((target - progress) <= 0) {
      RebuildInProgress(s"Rebuild ${projection.prefix} v${projection.version} finished")
    } else {
      logger.info(s"Tracker index at $progress, target is now at $target.")
      Thread.sleep(1.second.toMillis)
      waitForTracker()
    }
  }

  def finalizeVersion(): RebuildResult = {
    versionRepository.finalizeVersion(projection.prefix, projection.version)
    RebuildStopped(s"Projection ${projection.prefix} v${projection.version} finalized")
  }
}
