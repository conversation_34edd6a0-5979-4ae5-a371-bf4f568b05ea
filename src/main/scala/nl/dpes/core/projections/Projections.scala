package nl.dpes.core.projections

import nl.dpes.core.config.Configuration
import nl.dpes.core.repositories.TokenRepository.{
  CoreProjectionsRebuildToken,
  ElasticSearchProjectionsRebuildToken,
  IndexProjectionsRebuildToken,
  Token
}

abstract class Projections {
  def initialize(): Unit
}

object Projections extends Configuration {

  trait ProjectionsType {
    val prefix: String
    val version: Int
  }

  sealed abstract class Projection(val prefix: String, val version: Int, val token: Token) extends ProjectionsType

  case object Core  extends Projection(prefix = "core", version = ProjectionDetails.Core.version, CoreProjectionsRebuildToken)
  case object Index extends Projection(prefix = "index", version = ProjectionDetails.Index.version, IndexProjectionsRebuildToken)

  case object ElasticSearchIndex
      extends Projection(
        prefix = "elasticsearch",
        version = ProjectionDetails.ElasticSearch.version,
        ElasticSearchProjectionsRebuildToken
      )
}
