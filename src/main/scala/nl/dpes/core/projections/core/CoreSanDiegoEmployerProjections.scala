package nl.dpes.core.projections.core

import com.amazonaws.services.dynamodbv2.document.DynamoDB
import nl.dpes.core.config.Environment
import nl.dpes.core.domain.Recruiter
import nl.dpes.core.projections.DynamoDBProjections
import nl.dpes.core.projections.Projections.Core
import nl.dpes.core.projections.core.CoreSanDiegoEmployerProjections.CoreSanDiegoEmployer
import nl.dpes.utils.dynamodb.DynamoDBSupport.{DataTypes, HashField, Identity, PrimaryIndex}
import org.axonframework.eventhandling.EventHandler
import spray.json.{DefaultJsonProtocol, RootJsonFormat}

class CoreSanDiegoEmployerProjections(override protected val db: DynamoDB)(implicit environment: Environment)
    extends DynamoDBProjections(db) {

  private object JsonProtocol extends DefaultJsonProtocol {
    implicit lazy val coreSanDiegoEmployerFormat: RootJsonFormat[CoreSanDiegoEmployer] = jsonFormat2(CoreSanDiegoEmployer)
  }

  import JsonProtocol._

  private implicit lazy val sanDiegoEmployers = projection("ndp-sandiego-employers", Core)
  override lazy val tables: Seq[String]       = Seq(sanDiegoEmployers.getTableName)

  override protected val primaryIndex: PrimaryIndex = PrimaryIndex(
    HashField("sanDiegoId", DataTypes.Number)
  )

  override def initialize(): Unit = sanDiegoEmployers

  def findBySanDiegoId(sanDiegoId: Int): Option[CoreSanDiegoEmployer] =
    findOnPrimaryIndex[CoreSanDiegoEmployer](Identity("sanDiegoId", sanDiegoId))

  @EventHandler
  def onGeimporteerd(event: Recruiter.Geimporteerd): Unit =
    insert[CoreSanDiegoEmployer](
      Identity("sanDiegoId", event.sanDiegoId),
      CoreSanDiegoEmployer(event.sanDiegoId, event.recruiterId)
    )
}

object CoreSanDiegoEmployerProjections {
  final case class CoreSanDiegoEmployer(sanDiegoId: Int, recruiterId: String)
}
