package nl.dpes.core.projections.core

import com.amazonaws.services.dynamodbv2.document.DynamoDB
import nl.dpes.core.config.Environment
import nl.dpes.core.domain.{EMailadres, Sites}
import nl.dpes.core.domain.Recruiter.{EMail<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>erwij<PERSON>d}
import nl.dpes.core.domain.Sites.Site
import nl.dpes.core.projections.DynamoDBProjections
import nl.dpes.core.projections.Projections.Core
import nl.dpes.core.projections.core.CoreRecruiterAccountProjections.CoreRecruiterAccount
import nl.dpes.utils.dynamodb.DynamoDBSupport
import nl.dpes.utils.dynamodb.DynamoDBSupport._
import org.axonframework.eventhandling.EventHandler
import spray.json.{DefaultJsonProtocol, RootJsonFormat}

class CoreRecruiterAccountProjections(override protected val db: DynamoDB)(implicit environment: Environment)
    extends DynamoDBProjections(db) {

  private object JsonProtocol extends DefaultJsonProtocol {
    implicit lazy val coreRecruiterAccountFormat: RootJsonFormat[CoreRecruiterAccount] = jsonFormat3(CoreRecruiterAccount)
  }

  import JsonProtocol._

  private implicit lazy val recruiterAccounts = projection("ndp-recruiter-accounts", Core)
  override lazy val tables: Seq[String]       = Seq(recruiterAccounts.getTableName)

  override def initialize(): Unit = recruiterAccounts

  override protected val primaryIndex: DynamoDBSupport.PrimaryIndex = PrimaryIndex(HashField("recruiterId", DataTypes.String))

  override protected val secondaryIndexes = List(
    SecondaryIndex(
      "eMailadresIndex",
      HashField("eMailadres", DataTypes.String),
      Some(SortField("site", DataTypes.String))
    )
  )

  def isEMailadresAvailable(eMailadres: EMailadres, site: Site): Boolean =
    count("eMailadresIndex", Identity("eMailadres", eMailadres: String), Identity("site", site: String)) == 0

  def findByRecruiterId(recruiterId: String): Option[CoreRecruiterAccount] =
    findOnPrimaryIndex[CoreRecruiterAccount](Identity("recruiterId", recruiterId))

  def findByEMailadresAndSite(eMailadres: EMailadres, site: Site): Option[CoreRecruiterAccount] =
    findOnSecondaryIndex[CoreRecruiterAccount](
      "eMailadresIndex",
      Identity("eMailadres", eMailadres: String),
      Some(Identity("site", site: String))
    ).headOption

  @EventHandler
  def onGeimporteerd(event: Geimporteerd): Unit =
    insertAccount(
      Identity("recruiterId", event.recruiterId),
      CoreRecruiterAccount(
        event.recruiterId,
        event.eMailadres.value,
        event.site
      )
    )

  @EventHandler
  def onGeregistreerd(event: Geregistreerd): Unit =
    insertAccount(
      Identity("recruiterId", event.recruiterId),
      CoreRecruiterAccount(
        event.recruiterId,
        event.eMailadres.value,
        event.site
      )
    )

  @EventHandler
  def onEMailadresGewijzigd(event: EMailadresGewijzigd): Unit =
    findByRecruiterId(event.recruiterId).foreach { account =>
      insertAccount(
        Identity("recruiterId", event.recruiterId),
        CoreRecruiterAccount(
          event.recruiterId,
          event.eMailadres,
          Sites.siteToString(account.site)
        )
      )
    }

  @EventHandler
  def onVerwijderd(event: Verwijderd): Unit =
    delete(Identity("recruiterId", event.recruiterId))

  private def insertAccount(id: Identity, document: CoreRecruiterAccount): Unit =
    insert[CoreRecruiterAccount](id, document, Identity("eMailadres", document.eMailadres), Identity("site", document.site))
}

object CoreRecruiterAccountProjections {
  final case class CoreRecruiterAccount(recruiterId: String, eMailadres: String, site: String)
}
