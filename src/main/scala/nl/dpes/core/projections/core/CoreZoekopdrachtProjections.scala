package nl.dpes.core.projections.core

import com.amazonaws.services.dynamodbv2.document.DynamoDB
import nl.dpes.core.config.Environment
import nl.dpes.core.domain.Zoekopdracht.{AangemaaktD<PERSON><PERSON><PERSON><PERSON><PERSON>, Verwijderd}
import nl.dpes.core.domain.Zoekparameters
import nl.dpes.core.projections.DynamoDBProjections
import nl.dpes.core.projections.Projections.Core
import nl.dpes.core.projections.core.CoreZoekopdrachtProjections.CoreZoekopdracht
import nl.dpes.utils.dynamodb.DynamoDBSupport
import nl.dpes.utils.dynamodb.DynamoDBSupport._
import org.axonframework.eventhandling.EventHandler
import org.axonframework.serialization.Serializer
import spray.json.{DefaultJsonProtocol, RootJsonFormat}

class CoreZoekopdrachtProjections(override protected val db: DynamoDB, serializer: Serializer)(implicit environment: Environment)
    extends DynamoDBProjections(db) {

  private object JsonProtocol extends DefaultJsonProtocol {
    implicit lazy val coreRecruiterAccountFormat: RootJsonFormat[CoreZoekopdracht] = jsonFormat2(CoreZoekopdracht)
  }

  import JsonProtocol._

  private implicit lazy val zoekopdrachten = projection("ndp-zoekopdrachten", Core)
  override def tables: Seq[String]         = Seq(zoekopdrachten.getTableName)

  override def initialize(): Unit = zoekopdrachten

  override protected val primaryIndex: DynamoDBSupport.PrimaryIndex = PrimaryIndex(HashField("zoekopdrachtId", DataTypes.String))

  override protected val secondaryIndexes = List(
    SecondaryIndex("parametersIndex", HashField("zoekparameters", DataTypes.String))
  )

  def findByParameters(zoekparameters: Zoekparameters): Option[CoreZoekopdracht] =
    findOnSecondaryIndex[CoreZoekopdracht](
      "parametersIndex",
      Identity("zoekparameters", identity(zoekparameters)),
      None
    ).headOption

  @EventHandler
  def onAangemaaktDoorRecruiter(event: AangemaaktDoorRecruiter): Unit =
    insertZoekopdracht(
      Identity("zoekopdrachtId", event.zoekopdrachtId),
      CoreZoekopdracht(
        event.zoekopdrachtId,
        identity(event.zoekparameters)
      )
    )

  @EventHandler
  def onVerwijderd(event: Verwijderd): Unit =
    delete(Identity("zoekopdrachtId", event.zoekopdrachtId))

  private def insertZoekopdracht(id: Identity, document: CoreZoekopdracht): Unit =
    insert[CoreZoekopdracht](id, document, Identity("zoekparameters", document.zoekparameters))

  private def identity(parameters: Zoekparameters): String = {
    val maybeSortedZoektermen = parameters.zoektermen.map(zoektermen =>
      zoektermen.copy(
        alles = zoektermen.alles.map((mySeq: Seq[String]) => mySeq.sorted),
        opleidingNaam = zoektermen.opleidingNaam.map((mySeq: Seq[String]) => mySeq.sorted),
        opleidingBeschrijving = zoektermen.opleidingBeschrijving.map((mySeq: Seq[String]) => mySeq.sorted),
        gewensteBaan = zoektermen.gewensteBaan.map((mySeq: Seq[String]) => mySeq.sorted),
        functieTitel = zoektermen.functieTitel.map((mySeq: Seq[String]) => mySeq.sorted),
        functieBeschrijving = zoektermen.functieBeschrijving.map((mySeq: Seq[String]) => mySeq.sorted),
        cursussen = zoektermen.cursussen.map((mySeq: Seq[String]) => mySeq.sorted)
      )
    )

    val sortedZoekparameters = parameters.copy(
      zoektermen = maybeSortedZoektermen,
      opleidingsniveaus = parameters.opleidingsniveaus.map((mySeq: Seq[String]) => mySeq.sorted),
      aantallenUren = parameters.aantallenUren.map((mySeq: Seq[String]) => mySeq.sorted),
      soortenWerk = parameters.soortenWerk.map((mySeq: Seq[String]) => mySeq.sorted),
      beschikbaarheden = parameters.beschikbaarheden.map((mySeq: Seq[String]) => mySeq.sorted),
      rijbewijzen = parameters.rijbewijzen.map((mySeq: Seq[String]) => mySeq.sorted),
      talen = parameters.talen.map((mySeq: Seq[String]) => mySeq.sorted),
      afstandTotWerklocatie = parameters.afstandTotWerklocatie,
      carriereniveau = parameters.carriereniveau.map((mySeq: Seq[String]) => mySeq.sorted),
      functiegroep = parameters.functiegroep.map((mySeq: Seq[String]) => mySeq.sorted),
      gewenstSalaris = parameters.gewenstSalaris.map((mySeq: Seq[String]) => mySeq.sorted),
      provincies = parameters.provincies.map((mySeq: Seq[String]) => mySeq.sorted)
    )

    val serialized = serializer.serialize[String](sortedZoekparameters, classOf[String])
    serialized.getData.hashCode.toString
  }
}

object CoreZoekopdrachtProjections {
  final case class CoreZoekopdracht(zoekopdrachtId: String, zoekparameters: String)
}
