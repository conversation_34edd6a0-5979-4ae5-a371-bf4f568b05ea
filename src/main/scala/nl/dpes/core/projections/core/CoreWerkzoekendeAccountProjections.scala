package nl.dpes.core.projections.core

import com.amazonaws.services.dynamodbv2.document.DynamoDB
import nl.dpes.core.config.Environment
import nl.dpes.core.domain.Sites.Site
import nl.dpes.core.domain.{EMailadres, Sites, Werkzoekende}
import nl.dpes.core.projections.DynamoDBProjections
import nl.dpes.core.projections.Projections.Core
import nl.dpes.core.projections.core.CoreWerkzoekendeAccountProjections.CoreWerkzoekendeAccount
import nl.dpes.utils.dynamodb.DynamoDBSupport._
import org.axonframework.eventhandling.EventHandler
import spray.json.{DefaultJsonProtocol, RootJsonFormat}

object CoreWerkzoekendeAccountProjections {

  final case class CoreWerkzoekendeAccount(
    werkzoekendeId: String,
    sanDiegoId: Option[Int],
    eMailadres: String,
    site: String,
    wachtwoordHash: Option[String],
    salt: Option[String],
    hasLegacyPassword: Option[Boolean] = None,
    lastActivity: Option[Long] = None
  )
}

class CoreWerkzoekendeAccountProjections(override protected val db: DynamoDB)(implicit environment: Environment)
    extends DynamoDBProjections(db) {

  private object JsonProtocol extends DefaultJsonProtocol {
    implicit lazy val coreAccountFormat: RootJsonFormat[CoreWerkzoekendeAccount] = jsonFormat8(CoreWerkzoekendeAccount)
  }

  import JsonProtocol._

  private implicit lazy val werkzoekendeAccounts = projection("ndp-werkzoekende-accounts", Core)
  override lazy val tables: Seq[String]          = Seq(werkzoekendeAccounts.getTableName)

  override protected val primaryIndex = PrimaryIndex(HashField("werkzoekendeId", DataTypes.String))

  override protected val secondaryIndexes = List(
    SecondaryIndex(
      "eMailadresIndex",
      HashField("eMailadres", DataTypes.String),
      Some(SortField("site", DataTypes.String))
    ),
    SecondaryIndex(
      "sanDiegoIdIndex",
      HashField("sanDiegoId", DataTypes.Number),
      None
    )
  )

  override def initialize(): Unit = werkzoekendeAccounts

  def eMailadresAvailable(eMailadres: EMailadres, site: Site): Boolean =
    count("eMailadresIndex", Identity("eMailadres", eMailadres.value), Identity("site", Sites.siteToString(site))) == 0

  def findByWerkzoekendeId(werkzoekendeId: String): Option[CoreWerkzoekendeAccount] =
    findOnPrimaryIndex[CoreWerkzoekendeAccount](Identity("werkzoekendeId", werkzoekendeId))

  def findBySanDiegoId(sanDiegoId: Int): Option[CoreWerkzoekendeAccount] =
    findOnSecondaryIndex[CoreWerkzoekendeAccount](
      "sanDiegoIdIndex",
      Identity("sanDiegoId", sanDiegoId),
      None
    ).headOption

  def findByEMailadres(eMailadres: EMailadres, site: Site): Option[CoreWerkzoekendeAccount] =
    findOnSecondaryIndex[CoreWerkzoekendeAccount](
      "eMailadresIndex",
      Identity("eMailadres", eMailadres.value),
      Some(Identity("site", Sites.siteToString(site)))
    ).headOption

  @EventHandler
  def onGeimporteerd(event: Werkzoekende.AccountGeimporteerd): Unit =
    insertAccount(
      Identity("werkzoekendeId", event.werkzoekendeId),
      CoreWerkzoekendeAccount(
        event.werkzoekendeId,
        Some(event.sanDiegoId),
        event.eMailadres,
        Sites.siteToString(event.site),
        Some(event.wachtwoord.hash),
        Some(event.wachtwoord.salt),
        if (event.wachtwoord.isLegacy) Some(true) else None
      )
    )

  @EventHandler
  def onGeregistreerd(event: Werkzoekende.Geregistreerd): Unit = insertAccount(
    Identity("werkzoekendeId", event.werkzoekendeId),
    CoreWerkzoekendeAccount(event.werkzoekendeId, None, event.eMailadres, Sites.siteToString(event.site), None, None)
  )

  @EventHandler
  def onGeregistreerdExternal(event: Werkzoekende.GeregistreerdExternal): Unit = insertAccount(
    Identity("werkzoekendeId", event.werkzoekendeId),
    CoreWerkzoekendeAccount(event.werkzoekendeId, None, event.eMailadres, Sites.siteToString(event.site), None, None)
  )

  @EventHandler
  def onGeverifieerd(event: Werkzoekende.Geverifieerd): Unit =
    findByWerkzoekendeId(event.werkzoekendeId).foreach { account =>
      insertAccount(Identity("werkzoekendeId", event.werkzoekendeId), account)
    }

  @EventHandler
  def onWachtwoordIngesteld(event: Werkzoekende.WachtwoordIngesteld): Unit =
    findByWerkzoekendeId(event.werkzoekendeId).foreach { account =>
      insertAccount(
        Identity("werkzoekendeId", event.werkzoekendeId),
        account.copy(wachtwoordHash = Some(event.wachtwoord.hash), salt = Some(event.wachtwoord.salt), hasLegacyPassword = None)
      )
    }

  @EventHandler
  def onWachtwoordIngesteldVoorExternalWerkzoekende(event: Werkzoekende.WachtwoordIngesteldVoorExternalWerkzoekende): Unit =
    findByWerkzoekendeId(event.werkzoekendeId).foreach { account =>
      insertAccount(
        Identity("werkzoekendeId", event.werkzoekendeId),
        account.copy(wachtwoordHash = Some(event.wachtwoord.hash), salt = Some(event.wachtwoord.salt), hasLegacyPassword = None)
      )
    }

  @EventHandler
  def onWachtwoordOpnieuwIngesteld(event: Werkzoekende.WachtwoordOpnieuwIngesteld): Unit =
    findByWerkzoekendeId(event.werkzoekendeId).foreach { account =>
      insertAccount(
        Identity("werkzoekendeId", event.werkzoekendeId),
        account.copy(wachtwoordHash = Some(event.wachtwoord.hash), salt = Some(event.wachtwoord.salt), hasLegacyPassword = None)
      )
    }

  @EventHandler
  def onWachtwoordGewijzigd(event: Werkzoekende.WachtwoordGewijzigd): Unit =
    findByWerkzoekendeId(event.werkzoekendeId).foreach { account =>
      insertAccount(
        Identity("werkzoekendeId", event.werkzoekendeId),
        account.copy(wachtwoordHash = Some(event.nieuwWachtwoord.hash), salt = Some(event.nieuwWachtwoord.salt), hasLegacyPassword = None)
      )
    }

  @EventHandler
  def onEMailadresGewijzigd(event: Werkzoekende.EMailadresGewijzigd): Unit =
    findByWerkzoekendeId(event.werkzoekendeId).foreach { account =>
      insertAccount(
        Identity("werkzoekendeId", event.werkzoekendeId),
        account.copy(eMailadres = event.nieuwEMailadres)
      )
    }

  @EventHandler
  def onAccountOpgezegd(event: Werkzoekende.AccountOpgezegd): Unit =
    delete(Identity("werkzoekendeId", event.werkzoekendeId))

  def updateLastActivity(werkzoekendeId: String, timestamp: Long): Unit =
    findByWerkzoekendeId(werkzoekendeId).foreach { account =>
      insertAccount(Identity("werkzoekendeId", werkzoekendeId), account.copy(lastActivity = Some(timestamp)))
    }

  private def insertAccount(id: Identity, document: CoreWerkzoekendeAccount): Unit = {
    val identities = Seq(
      Some(Identity("eMailadres", document.eMailadres)),
      Some(Identity("site", document.site)),
      document.sanDiegoId.map(Identity("sanDiegoId", _))
    ).flatten

    insert[CoreWerkzoekendeAccount](
      id,
      document,
      identities: _*
    )
  }
}
