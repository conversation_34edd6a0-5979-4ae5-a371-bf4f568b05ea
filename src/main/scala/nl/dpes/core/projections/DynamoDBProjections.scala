package nl.dpes.core.projections

import com.amazonaws.services.dynamodbv2.document.{DynamoDB, Table}
import nl.dpes.core.config.Environment
import nl.dpes.core.projections.Projections.ProjectionsType
import nl.dpes.utils.dynamodb.DynamoDBSupport

protected[projections] abstract class DynamoDBProjections(override protected val db: DynamoDB)(implicit environment: Environment)
    extends Projections
    with DynamoDBSupport {

  def tables: Seq[String]

  protected def projection(name: String, projectionsType: ProjectionsType): Table =
    table(s"${projectionsType.prefix}_${name}_v${projectionsType.version}_${environment.abbreviation}")
}
