package nl.dpes.core.projections.index

import com.amazonaws.services.dynamodbv2.document.DynamoDB
import nl.dpes.core.domain.Frequenties.Frequentie
import nl.dpes.core.domain.Zoekopdracht.{<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Opgeslagen<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, VerwijderdDoorRec<PERSON><PERSON>}
import nl.dpes.core.domain.{Fre<PERSON>ies, Zoekparameters, Zoektermen}
import nl.dpes.core.projections.DynamoDBProjections
import nl.dpes.core.projections.Projections.Index
import nl.dpes.core.projections.index.IndexOpgeslagenZoekopdrachtProjections.IndexOpgeslagenZoekopdracht
import nl.dpes.common.marshalling.JsonSupport
import nl.dpes.core.config.Environment
import nl.dpes.utils.dynamodb.DynamoDBSupport._
import org.axonframework.eventhandling.EventHandler
import spray.json.{JsonFormat, RootJsonFormat}

class IndexOpgeslagenZoekopdrachtProjections(override protected val db: DynamoDB)(implicit environment: Environment)
    extends DynamoDBProjections(db) {

  private object JsonProtocol extends JsonSupport {
    implicit lazy val opgeslagenZoekopdrachtFormat: RootJsonFormat[IndexOpgeslagenZoekopdracht] = jsonFormat6(IndexOpgeslagenZoekopdracht)
    implicit lazy val frequentieFormat: JsonFormat[Frequentie]                                  = jsonEnum(Frequenties)
    implicit lazy val zoekparametersFormat: JsonFormat[Zoekparameters]                          = jsonFormat14(Zoekparameters)
    implicit lazy val zoektermenFormat: JsonFormat[Zoektermen]                                  = jsonFormat7(Zoektermen)
  }

  import JsonProtocol._

  private implicit lazy val opgeslagenZoekopdrachten = projection("ndp-opgeslagen-zoekopdrachten", Index)
  override def tables: Seq[String]                   = Seq(opgeslagenZoekopdrachten.getTableName)

  override def initialize(): Unit = opgeslagenZoekopdrachten

  override protected val primaryIndex: PrimaryIndex = PrimaryIndex(HashField("opgeslagenZoekopdrachtId", DataTypes.String))

  override protected val secondaryIndexes = List(
    SecondaryIndex("accountIdIndex", HashField("accountId", DataTypes.String))
  )

  def findByRecruiterId(recruiterId: String): List[IndexOpgeslagenZoekopdracht] =
    findOnSecondaryIndex[IndexOpgeslagenZoekopdracht](
      "accountIdIndex",
      Identity("accountId", recruiterId),
      None
    )

  def findBySavedSearchId(recruiterId: String, zoekopdrachtId: String): Option[IndexOpgeslagenZoekopdracht] =
    findOnPrimaryIndex[IndexOpgeslagenZoekopdracht](
      Identity("opgeslagenZoekopdrachtId", opgeslagenZoekopdrachtId(recruiterId, zoekopdrachtId))
    )

  private def opgeslagenZoekopdrachtId(accountId: String, zoekopdrachtId: String): String = s"${accountId}_$zoekopdrachtId"

  @EventHandler
  def onOpgeslagenDoorRecruiter(event: OpgeslagenDoorRecruiter): Unit = {
    val indexOpgeslagenZoekopdracht = IndexOpgeslagenZoekopdracht(
      opgeslagenZoekopdrachtId(event.recruiterId, event.zoekopdrachtId),
      event.recruiterId,
      event.zoekopdrachtId,
      event.naam,
      event.frequentie,
      event.zoekparameters
    )

    insertOpgeslagenZoekopdracht(event.recruiterId, indexOpgeslagenZoekopdracht)
  }

  @EventHandler
  def onGewijzigd(event: Gewijzigd): Unit =
    findBySavedSearchId(event.recruiterId, event.zoekopdrachtId) match {
      case Some(indexOpgeslagenZoekopdracht) =>
        insertOpgeslagenZoekopdracht(
          event.recruiterId,
          indexOpgeslagenZoekopdracht.copy(naam = event.naam, frequentie = event.frequentie)
        )
      case _ =>
    }

  def insertOpgeslagenZoekopdracht(recruiterId: String, indexOpgeslagenZoekopdracht: IndexOpgeslagenZoekopdracht): Unit =
    insert[IndexOpgeslagenZoekopdracht](
      Identity("opgeslagenZoekopdrachtId", indexOpgeslagenZoekopdracht.opgeslagenZoekopdrachtId),
      indexOpgeslagenZoekopdracht,
      Identity("accountId", recruiterId)
    )

  @EventHandler
  def onVerwijderdDoorRecruiter(event: VerwijderdDoorRecruiter): Unit =
    delete(Identity("opgeslagenZoekopdrachtId", opgeslagenZoekopdrachtId(event.recruiterId, event.zoekopdrachtId)))
}

object IndexOpgeslagenZoekopdrachtProjections {

  final case class IndexOpgeslagenZoekopdracht(
    opgeslagenZoekopdrachtId: String,
    accountId: String,
    zoekopdrachtId: String,
    naam: String,
    frequentie: Frequentie,
    zoekparameters: Zoekparameters
  )
}
