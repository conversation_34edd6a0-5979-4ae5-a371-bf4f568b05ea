package nl.dpes.core.projections.index

import akka.http.scaladsl.marshallers.sprayjson.SprayJsonSupport
import com.sksamuel.elastic4s._
import com.sksamuel.elastic4s.http.delete.DeleteByQueryResponse
import com.sksamuel.elastic4s.http.{ElasticClient, RequestFailure, RequestSuccess}
import nl.dpes.core.domain.Recruiter
import nl.dpes.core.domain.Recruiter.{FavorietGemaakt, FavorietVerwijderd}
import nl.dpes.core.projections.{ElasticSearchProjections, Projections}
import nl.dpes.core.projections.index.IndexRecruiterFavorietProjections.{RecruiterFavoriet, RecruiterFavorietDocument}
import org.axonframework.eventhandling.EventHandler
import org.slf4j.Logger
import spray.json.{enrichAny, DefaultJsonProtocol, RootJsonFormat}

import scala.concurrent.ExecutionContext.Implicits.global
import scala.concurrent.Future
import scala.util.{Success, Try}

class IndexRecruiterFavorietProjections(
  index: String,
  indexType: String,
  client: ElasticClient,
  refreshPolicy: RefreshPolicy = RefreshPolicy.NONE
)(implicit logger: Logger)
    extends ElasticSearchProjections[RecruiterFavoriet](index, indexType, client, refreshPolicy) {

  import com.sksamuel.elastic4s.http.ElasticDsl._
  import ElasticSearchProjections._
  import nl.dpes.core.projections.index.IndexRecruiterFavorietProjections.JsonProtocol._

  private val defaultOffset = 0
  private val defaultLimit  = 100

  override def createIndexDefinition(): Unit =
    client.execute {
      createIndex(projection(index, Projections.ElasticSearchIndex))
        .alias(index)
        .mappings(
          mapping(indexType).fields(
            keywordField("recruiterId"),
            keywordField("werkzoekendeId"),
            textField("tags")
          )
        )
    }.await

  implicit object RecruiterFavorietHitReader extends HitReader[RecruiterFavorietDocument] {

    override def read(hit: Hit): Try[RecruiterFavorietDocument] =
      Success(
        RecruiterFavorietDocument(
          hit.id,
          hit.sourceAsMap("recruiterId").toString,
          hit.sourceAsMap("werkzoekendeId").toString
        )
      )
  }

  def searchFavoriet(recruiterId: String, page: Int, size: Int): Future[ResultSet[RecruiterFavorietDocument]] = {

    def calculateOffset(p: Int, s: Int) = s * (p - 1)

    val query  = boolQuery().filter(matchQuery("recruiterId", recruiterId))
    val offset = calculateOffset(page, size)

    searchDocument[RecruiterFavorietDocument](query, offset, size)
  }

  def searchFavorietByWerkzoekendeIds(recruiterId: String, werkzoekendeIds: Seq[String]): Future[ResultSet[RecruiterFavorietDocument]] = {
    val query = boolQuery().filter(matchQuery("recruiterId", recruiterId), termsQuery("werkzoekendeId", werkzoekendeIds))

    searchDocument[RecruiterFavorietDocument](query, defaultOffset, defaultLimit)
  }

  def insertFavoriet(document: RecruiterFavoriet): Future[Unit] = {
    implicit object RecruiterFavorietIndexable extends Indexable[RecruiterFavoriet] {
      override def json(t: RecruiterFavoriet): String = t.toJson.toString()
    }

    insert(document)
  }

  def deleteFavoriet(recruiterId: String, werkzoekendeId: String): Future[Unit] = {
    val query = boolQuery().filter(matchQuery("recruiterId", recruiterId), matchQuery("werkzoekendeId", werkzoekendeId))
    deleteDocument(query)
  }

  @EventHandler
  def onFavorietGemaakt(event: FavorietGemaakt): Unit =
    insertFavoriet(RecruiterFavoriet(event.recruiterId, event.werkzoekendeId)).await

  @EventHandler
  def onFavorietVerwijderd(event: FavorietVerwijderd): Unit =
    deleteFavoriet(event.recruiterId, event.werkzoekendeId).await

  @EventHandler
  def onRecruiterVerwijderd(event: Recruiter.Verwijderd): Unit =
    deleteAllByRecruiter(event.recruiterId)

  def deleteAllByRecruiter(recruiterId: String): Future[Either[RequestFailure, RequestSuccess[DeleteByQueryResponse]]] =
    client
      .execute {
        deleteByQuery(index, indexType, termsQuery("recruiterId", recruiterId)).refreshImmediately
      }
      .map {
        case err: RequestFailure                         => Left(err)
        case succ: RequestSuccess[DeleteByQueryResponse] => Right(succ)
      }
}

object IndexRecruiterFavorietProjections {
  final case class RecruiterFavoriet(recruiterId: String, werkzoekendeId: String)
  final case class RecruiterFavorietDocument(id: String, recruiterId: String, werkzoekendeId: String)

  private object JsonProtocol extends SprayJsonSupport with DefaultJsonProtocol {
    implicit val recruiterDocumentFormat: RootJsonFormat[RecruiterFavoriet] = DefaultJsonProtocol.jsonFormat2(RecruiterFavoriet)
  }
}
