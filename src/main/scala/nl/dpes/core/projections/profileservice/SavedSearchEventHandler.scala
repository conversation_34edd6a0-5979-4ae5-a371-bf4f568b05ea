package nl.dpes.core.projections.profileservice

import cats.effect.IO
import cats.effect.unsafe.implicits.global
import nl.dpes.core.domain.Zoekopdracht.{AangemaaktDoor<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, OpgeslagenD<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, VerwijderdDoorRecruiter}
import nl.dpes.core.domain.ZoekparametersHelper
import nl.dpes.core.repositories.SavedSearchRepository
import nl.dpes.core.services.profileservice.{Frequency, RecruiterId, SavedSearchId, SavedSearchName}
import org.axonframework.eventhandling.EventHandler
import org.slf4j.{Logger, LoggerFactory}

class SavedSearchEventHandler(savedSearchRepository: SavedSearchRepository[IO]) {

  implicit val logger: Logger = LoggerFactory.getLogger(getClass)

  @EventHandler
  def onOpgeslagenDoorRecruiter(event: OpgeslagenDoorRecruiter) =
    if (event.recruiterId.length == 15 || event.recruiterId.length == 18)
      (for {
        filters <- IO.fromEither(ZoekparametersHelper.toSearchFilters(event.zoekparameters))
        result <- savedSearchRepository
          .create(
            SavedSearchId(event.zoekopdrachtId),
            SavedSearchName(event.naam),
            RecruiterId(event.recruiterId),
            filters,
            Frequency(event.frequentie.toString)
          )
      } yield result)
        .onError { case thr: Throwable =>
          IO.pure(logger.error(s"Error occurred when trying to save the saved search '$event': ${getNestedExceptions(thr)}"))
        }
        .unsafeRunSync()
    else
      logger.info(
        s"Ignoring to save the saved search '${event.zoekopdrachtId}' for recruiter '${event.recruiterId}', because the recruiter id length is not 15 or 18."
      )

  @EventHandler
  def onAangemaaktDoorRecruiter(event: AangemaaktDoorRecruiter) =
    (for {
      filters <- IO.fromEither(ZoekparametersHelper.toSearchFilters(event.zoekparameters))
      result  <- savedSearchRepository.create(SavedSearchId(event.zoekopdrachtId), filters)
    } yield result)
      .onError { case thr: Throwable =>
        IO.pure(logger.error(s"Error occurred when trying to save the saved search '$event': ${getNestedExceptions(thr)}"))
      }
      .unsafeRunSync()

  @EventHandler
  def onGewijzigd(event: Gewijzigd) =
    if (event.recruiterId.length == 15 || event.recruiterId.length == 18)
      savedSearchRepository
        .updateFrequency(
          SavedSearchId(event.zoekopdrachtId),
          Frequency(event.frequentie.toString)
        )
        .attempt
        .unsafeRunSync() match {
        case Right(value) => value
        case Left(exception) =>
          logger.error(s"Error occurred when trying to update the saved search: ${exception.getMessage}")
          throw exception
      }
    else
      logger.info(
        s"Ignoring to update the saved search '${event.zoekopdrachtId}' for recruiter '${event.recruiterId}', because the recruiter id length is not 15 or 18."
      )

  @EventHandler
  def onVerwijderd(event: Verwijderd) =
    savedSearchRepository
      .delete(SavedSearchId(event.zoekopdrachtId))
      .attempt
      .unsafeRunSync() match {
      case Right(value) => value
      case Left(exception) =>
        logger.error(s"Error occurred when trying to delete the saved search: ${exception.getMessage}")
        throw exception
    }

  @EventHandler
  def onVerwijderdDoorRecruiter(event: VerwijderdDoorRecruiter) =
    if (event.recruiterId.length == 15 || event.recruiterId.length == 18)
      savedSearchRepository
        .delete(SavedSearchId(event.zoekopdrachtId), RecruiterId(event.recruiterId))
        .attempt
        .unsafeRunSync() match {
        case Right(value) => value
        case Left(exception) =>
          logger.error(s"Error occurred when trying to delete the saved search: ${exception.getMessage}")
          throw exception
      }
    else
      logger.info(
        s"Ignoring to delete the saved search '${event.zoekopdrachtId}' for recruiter '${event.recruiterId}', because the recruiter id length is not 15 or 18."
      )

  private def getNestedExceptions(thr: Throwable): String = {
    val causes = Iterator
      .iterate[Throwable](thr)(_.getCause)
      .takeWhile(_ != null)
      .map(e => s"${e.getClass.getName}: ${e.getMessage}")
      .mkString(" -> ")
    s"$causes"
  }
}
