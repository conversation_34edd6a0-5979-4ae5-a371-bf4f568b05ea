package nl.dpes.core.projections.profileservice

import cats.effect.IO
import cats.effect.unsafe.implicits.global
import nl.dpes.core.domain.Recruiter.{FavorietGemaakt, FavorietVerwijderd}
import nl.dpes.core.repositories.FavoriteProfilesRepository
import org.axonframework.eventhandling.EventHandler
import org.slf4j.{Logger, LoggerFactory}

import scala.util.{Failure, Success, Try}

class FavoriteProfilesEventHandler(favoriteProfilesRepository: FavoriteProfilesRepository[IO]) {

  implicit val logger: Logger = LoggerFactory.getLogger(getClass)

  @EventHandler
  def onFavorietGemaakt(event: FavorietGemaakt) =
    Try {
      if (event.recruiterId.length == 15 || event.recruiterId.length == 18)
        favoriteProfilesRepository
          .saveFavorite(event.recruiterId, event.werkzoekendeId)
          .unsafeRunSync()
      else
        logger.info(
          s"Ignoring to save the favorite profile '${event.werkzoekendeId}' for recruiter '${event.recruiterId}', because the recruiter id length is not 15 or 18."
        )
    } match {
      case Success(value) => value
      case Failure(exception) =>
        logger.error(s"Error occurred when trying to save the favorite: ${exception.getMessage}")
        throw exception
    }

  @EventHandler
  def onFavorietVerwijderd(event: FavorietVerwijderd) =
    Try {
      favoriteProfilesRepository
        .deleteFavorite(event.recruiterId, event.werkzoekendeId)
        .unsafeRunSync()
    } match {
      case Success(value) => value
      case Failure(exception) =>
        logger.error(s"Error occurred when trying to delete the favorite: ${exception.getMessage}")
        throw exception
    }
}
