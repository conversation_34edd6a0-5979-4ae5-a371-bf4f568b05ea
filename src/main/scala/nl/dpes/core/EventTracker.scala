package nl.dpes.core

import nl.dpes.core.config.ReplayModule

import scala.annotation.tailrec
import scala.concurrent.Await
import scala.concurrent.duration._
import scala.util.Failure

object EventTracker extends App with ReplayModule {
  sys.addShutdownHook(terminate)

  logger.info(s"Starting replay in '${Application.environment}' mode")

  onBeforeStart() match {
    case Failure(e) => handleFailure(e)
    case _          => ()
  }
  onStart() match {
    case Failure(e) => handleFailure(e)
    case _ =>
      onAfterStart() match {
        case Failure(e) => handleFailure(e)
        case _          => ()
      }
  }

  private def handleFailure(e: Throwable) = {
    logger.error(e.getMessage, e)
    sys.exit(1)
  }

  private lazy val terminate = {
    logger.info("Shutting down Core")
    onShutdown()
    onAfterShutdown()

    system.terminate()
    Await.result(system.whenTerminated, 30 seconds)
  }

  @tailrec
  def rebuild(): Unit = {
    Thread.sleep(1.second.toMillis)
    rebuild()
  }
}
