package nl.dpes.core

import org.apache.pekko.http.cors.scaladsl.settings.CorsSettings
import org.apache.pekko.http.scaladsl.Http
import org.apache.pekko.http.scaladsl.model.HttpMethods
import kamon.Kamon
import nl.dpes.core.api.Version
import nl.dpes.core.api.v1.customerservice.CustomerServiceLogic
import nl.dpes.core.api.v1.jobseekers.JobSeekerLogic
import nl.dpes.core.api.v1.recruiters.RecruitersLogic
import nl.dpes.core.api.v1.searches.SavedSearchLogic
import nl.dpes.core.api.v1.status.StatusLogic
import nl.dpes.core.api.v1.Routes
import nl.dpes.core.config.WebServerModule
import nl.dpes.core.jwks.JsonWebKeySet
import org.apache.pekko.http.cors.scaladsl.CorsDirectives.cors

import scala.concurrent.Await
import scala.concurrent.duration._
import scala.util.Failure

object WebServer extends App with WebServerModule with JsonWebKeySet {

  private val corsSettings = CorsSettings.defaultSettings.withAllowedMethods(
    List(HttpMethods.GET, HttpMethods.PUT, HttpMethods.DELETE, HttpMethods.POST, HttpMethods.HEAD, HttpMethods.OPTIONS)
  )

  private val version  = Version(1)
  private val prefix   = Api.basePath
  private val versionS = s"v${version.value}"

  private val statusLogic = new StatusLogic(statusService)

  private val recruitersLogic = new RecruitersLogic(
    sanDiegoEmployerProjections,
    recruiterAccountProjections,
    commandGateway,
    logger,
    salesforceIdMigrator,
    indexRecruiterFavorietProjections,
    executor
  )

  private val savedSearchLogic =
    new SavedSearchLogic(indexOpgeslagenZoekopdrachtenProjections, savedSearchService, executor, logger, prefix, versionS)

  private val customerServiceLogic = new CustomerServiceLogic(tokenService, logger, executor)

  private val jobSeekerLogic =
    new JobSeekerLogic(
      werkzoekendeAccountProjections,
      commandGateway,
      tokenService,
      passwordService,
      sanDiegoPasswordService,
      Security.ApiKey.keys,
      simClient,
      logger,
      executor
    )

  val routes = new Routes(prefix, versionS, statusLogic, savedSearchLogic, recruitersLogic, customerServiceLogic, jobSeekerLogic)

  private val apiRoutes = cors(corsSettings) {
    jsonWebKeySetRoute ~
    routes.statusRoute ~
    routes.savedSearchRoutes ~
    routes.recruitersRoutes ~
    routes.customerServiceRoutes ~
    routes.jobSeekerRoutes ~
    routes.swagger
  }

  sys.addShutdownHook(terminate)
  startServer()

  private def startServer(): Unit = {
    logger.info(s"Starting Core in '${Application.environment}' mode")

    onBeforeStart() match {
      case Failure(e) =>
        logger.error(e.getMessage, e)
        throw e
      case _ => ()
    }

    Http().newServerAt(interface = Server.host, port = Server.port).bindFlow(apiRoutes).map { binding =>
      logger.info(s"Core started - listening at ${Server.host}:${Server.port}")

      onStart() match {
        case Failure(e) =>
          binding.unbind()
          throw e
        case _ =>
          onAfterStart() match {
            case Failure(e) =>
              binding.unbind()
              throw e
            case _ => ()
          }
      }
    } onComplete {
      case Failure(e) =>
        logger.error(e.getMessage, e)
        sys.exit(1)
      case _ => ()
    }
  }

  private lazy val terminate = {
    logger.info("Shutting down Core")
    Await.ready(Kamon.stop(), 30 seconds)
    onShutdown()
    onAfterShutdown()

    system.terminate()
    Await.result(system.whenTerminated, 30 seconds)
  }
}
