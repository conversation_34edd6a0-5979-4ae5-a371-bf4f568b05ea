package nl.dpes.utils.dynamodb

import com.amazonaws.services.dynamodbv2.document.{DynamoDB, Item, RangeKeyCondition, Table}
import com.amazonaws.services.dynamodbv2.document.spec.{GetItemSpec, QuerySpec}
import com.amazonaws.services.dynamodbv2.model._
import com.amazonaws.services.dynamodbv2.model.{KeyType => DynamoDBKeyType}
import spray.json.{JsonParser, ParserInput, RootJsonFormat}

import scala.jdk.CollectionConverters._
import scala.util.{Failure, Success, Try}

object DynamoDBSupport {
  final val DefaultReadCapacityUnits  = 5L
  final val DefaultWriteCapacityUnits = 5L

  final case class Identity(key: String, value: Any)

  object KeyTypes extends Enumeration {
    type KeyType = Value
    val Hash, Sort = Value

    implicit def toDynamoDBKeyType(keyType: KeyType): DynamoDBKeyType = keyType match {
      case Hash => DynamoDBKeyType.HASH
      case Sort => DynamoDBKeyType.RANGE
    }
  }

  object DataTypes extends Enumeration {
    type DataType = Value
    val String, Number, Boolean = Value

    implicit def toDynamoDBDataType(dataType: DataType): String = dataType match {
      case String  => "S"
      case Number  => "N"
      case Boolean => "B"
    }
  }

  abstract class Field(val name: String, val dataType: DataTypes.DataType, val keyType: KeyTypes.KeyType)
      extends Product
      with Serializable {

    def toSchemaElement: KeySchemaElement = new KeySchemaElement()
      .withAttributeName(name)
      .withKeyType(keyType)
  }

  case class HashField(override val name: String, override val dataType: DataTypes.DataType) extends Field(name, dataType, KeyTypes.Hash)
  case class SortField(override val name: String, override val dataType: DataTypes.DataType) extends Field(name, dataType, KeyTypes.Sort)

  case class PrimaryIndex(key: HashField, sortKey: Option[SortField] = None)
  case class SecondaryIndex(indexName: String, key: HashField, sortKey: Option[SortField] = None)
}

trait DynamoDBSupport {
  import DynamoDBSupport._

  protected val db: DynamoDB

  protected val consistentReads: Boolean = true
  protected val readCapacityUnits: Long  = DefaultReadCapacityUnits
  protected val writeCapacityUnits: Long = DefaultWriteCapacityUnits

  protected val primaryIndex: PrimaryIndex
  protected val secondaryIndexes: List[SecondaryIndex] = Nil

  protected def table(tableName: String): Table =
    Try {
      val table = db.getTable(tableName)
      table.describe()
      table
    } recover { case _: ResourceNotFoundException =>
      val table = createTable(tableName)
      table.waitForActive()
      table
    } match {
      case Success(table) => table
      case Failure(e)     => throw e
    }

  private def createTable(tableName: String): Table = {
    val keySchema = List(Some(primaryIndex.key), primaryIndex.sortKey).flatten.map(_.toSchemaElement)

    val globalSecondaryIndexes = secondaryIndexes.map { index =>
      new GlobalSecondaryIndex()
        .withIndexName(index.indexName)
        .withKeySchema(
          List(Some(index.key), index.sortKey).flatten.map(_.toSchemaElement).asJava
        )
        .withProjection(
          new Projection()
            .withProjectionType(ProjectionType.ALL)
        )
        .withProvisionedThroughput(
          new ProvisionedThroughput()
            .withReadCapacityUnits(readCapacityUnits)
            .withWriteCapacityUnits(writeCapacityUnits)
        )
    }

    val attributeDefinitions: List[AttributeDefinition] = (
      Set(Some(primaryIndex.key), primaryIndex.sortKey).flatten ++
        secondaryIndexes.toSet.flatMap((index: SecondaryIndex) => Set(Some(index.key), index.sortKey).flatten)
    ).toList.map(field =>
      new AttributeDefinition()
        .withAttributeName(field.name)
        .withAttributeType(field.dataType)
    )

    val createRequest = new CreateTableRequest()
      .withTableName(tableName)
      .withKeySchema(keySchema.asJava)
      .withAttributeDefinitions(attributeDefinitions.asJava)
      .withProvisionedThroughput(
        new ProvisionedThroughput()
          .withReadCapacityUnits(readCapacityUnits)
          .withWriteCapacityUnits(writeCapacityUnits)
      )

    if (globalSecondaryIndexes.nonEmpty) {
      createRequest.withGlobalSecondaryIndexes(globalSecondaryIndexes.asJava)
    }

    db.createTable(createRequest)
  }

  protected def findOnPrimaryIndex[R](id: Identity, sortKeyId: Option[Identity] = None)(implicit
    tbl: Table,
    format: RootJsonFormat[R]
  ): Option[R] = {
    val item = tbl.getItem(
      sortKeyId
        .map { sortKey =>
          new GetItemSpec()
            .withPrimaryKey(id.key, id.value, sortKey.key, sortKey.value)
        }
        .getOrElse(
          new GetItemSpec()
            .withPrimaryKey(id.key, id.value)
        )
        .withAttributesToGet("document")
        .withConsistentRead(consistentReads)
    )

    Option(item).map { item =>
      format.read(
        JsonParser(ParserInput(item.getJSON("document")))
      )
    }
  }

  protected def findOnSecondaryIndex[R](indexName: String, partitionKeyId: Identity, sortKeyId: Option[Identity])(implicit
    tbl: Table,
    format: RootJsonFormat[R]
  ): List[R] = {
    val query = new QuerySpec()
      .withHashKey(partitionKeyId.key, partitionKeyId.value)
      .withAttributesToGet("document")

    sortKeyId.foreach { sId =>
      query.withRangeKeyCondition(
        new RangeKeyCondition(sId.key).eq(sId.value)
      )
    }

    tbl.getIndex(indexName).query(query).asScala.toList.map { item =>
      format.read(
        JsonParser(ParserInput(item.getJSON("document")))
      )
    }
  }

  protected def insert[T](id: Identity, document: T, secondaryId: Identity*)(implicit tbl: Table, format: RootJsonFormat[T]): Unit = {
    val item = new Item()
      .withPrimaryKey(id.key, id.value)
      .withJSON("document", format.write(document).toString)

    secondaryId.foreach { secondaryId =>
      item.withKeyComponent(secondaryId.key, secondaryId.value)
    }

    tbl.putItem(item)
  }

  protected def delete(id: Identity)(implicit tbl: Table): Unit =
    tbl.deleteItem(id.key, id.value)

  protected def count(index: String, primaryId: Identity, secondaryId: Identity*)(implicit tbl: Table): Int = {
    val query = new QuerySpec()
      .withHashKey(primaryId.key, primaryId.value)
      .withAttributesToGet("document")

    secondaryId.foreach { secondaryId =>
      query.withRangeKeyCondition(
        new RangeKeyCondition(secondaryId.key).eq(secondaryId.value)
      )
    }

    tbl.getIndex(index).query(query).asScala.size
  }
}
