package nl.dpes.utils.marshalling

import spray.json.{deserializationError, serializationError, JsArray, JsFalse, JsNumber, JsObject, JsString, JsTrue, JsValue, JsonFormat}

trait JsonMapSupport {

  implicit object AnyJsonFormat extends JsonFormat[Any] {

    def write(x: Any): JsValue =
      writeNumber orElse
      writeString orElse
      writeBoolean orElse
      writeMap orElse
      writeArray applyOrElse (x, unsupported)

    private def writeNumber: PartialFunction[Any, JsValue] = { case number: Int =>
      JsNumber(number)
    }

    private def writeString: PartialFunction[Any, JsValue] = { case string: String =>
      JsString(string)
    }

    private def writeBoolean: PartialFunction[Any, JsValue] = {
      case boolean: Boolean if boolean  => JsTrue
      case boolean: Boolean if !boolean => JsFalse
    }

    private def writeMap: PartialFunction[Any, JsValue] = { case map: Map[String, _] =>
      JsObject(map.map { case (key, value) =>
        key -> write(value)
      })
    }

    private def writeArray: PartialFunction[Any, JsValue] = {
      case vector: Vector[Any] => JsArray(vector.map(write))
      case list: List[Any]     => JsArray(list.toVector.map(write))
    }

    private def unsupported: PartialFunction[Any, JsValue] = { case x =>
      serializationError(s"Serialization of this type is not supported: ${x.toString}")
    }

    def read(value: JsValue): Any = value match {
      case JsObject(fields)  => fields.map(entry => entry._1 -> read(entry._2))
      case JsArray(elements) => elements.map(read)
      case JsNumber(number)  => number.intValue
      case JsString(string)  => string
      case JsTrue            => true
      case JsFalse           => false
      case unSupported       => deserializationError(s"Deserialization of this type is not supported: ${unSupported.toString}")
    }
  }
}
