package nl.dpes.utils.marshalling

import org.apache.pekko.http.scaladsl.marshallers.sprayjson.SprayJsonSupport
import spray.json.{DefaultJsonProtocol, DeserializationException, JsNumber, JsString, JsValue, JsonFormat}

trait JsonEnumSupport {
  this: SprayJsonSupport with DefaultJsonProtocol =>

  def jsonEnum[T <: Enumeration](enumType: T): JsonFormat[T#Value] = new JsonFormat[T#Value] {

    def write(obj: T#Value): JsValue =
      JsString(obj.toString)

    def read(json: JsValue): T#Value = json match {
      case JsString(value) => enumType.withName(value)
      case JsNumber(id) =>
        enumType.values.find(_.id == id).getOrElse(throw DeserializationException(s"Expected a value from enum $enumType"))
      case _ => throw DeserializationException(s"Expected a value from enum $enumType")
    }
  }
}
