package nl.dpes.utils.marshalling

import org.apache.pekko.http.scaladsl.marshallers.sprayjson.SprayJsonSupport
import org.joda.time.DateTime
import org.joda.time.format.ISODateTimeFormat
import spray.json._

import scala.util.{Failure, Success, Try}

trait JsonDateTimeSupport {
  this: SprayJsonSupport with DefaultJsonProtocol =>

  private def jsonDateTime(): JsonFormat[DateTime] = new JsonFormat[DateTime] {
    private val parser = ISODateTimeFormat.dateTimeNoMillis().withOffsetParsed()

    def write(date: DateTime): JsString =
      JsString(parser.print(date))

    def read(json: JsValue): DateTime = json match {
      case JsString(dateTime) =>
        Try(parser.parseDateTime(dateTime)) match {
          case Success(parsed) => parsed
          case Failure(e) =>
            deserializationError(s"Deserialization of date failed for string: $dateTime: ${e.getMessage}")
        }
      case other =>
        deserializationError(
          s"Deserialization of date failed because the value was not a JsString: ${other.getClass.getName}, ${other.toString}"
        )
    }
  }

  implicit lazy val dateTimeFormat: JsonFormat[DateTime] = jsonDateTime()
}
