package nl.dpes.utils.events.upcasting.werkzoekende

import nl.dpes.core.domain.Werkzoekende.{Geregistreerd, WachtwoordVergeten}
import org.axonframework.serialization.upcasting.event.{IntermediateEventRepresentation, SingleEventUpcaster}
import org.axonframework.serialization.{SerializedType, SimpleSerializedType}

import scala.xml.transform.{RewriteRule, RuleTransformer}
import scala.xml.{Elem, Node, Text, XML}

object UrlUpcaster {

  private final val TargetTypes: Map[SerializedType, SerializedType] = Map(
    new SimpleSerializedType(classOf[Geregistreerd].getTypeName, "1.1") -> new SimpleSerializedType(
      classOf[Geregistreerd].getTypeName,
      "1.2"
    ),
    new SimpleSerializedType(classOf[WachtwoordVergeten].getTypeName, "1.1") -> new SimpleSerializedType(
      classOf[WachtwoordVergeten].getTypeName,
      "1.2"
    )
  )

  object URLUpcasterTransformer extends RewriteRule {

    override def transform(n: Node): Seq[Node] = n match {
      case e: Elem if e.label == "verificatieUrl" || e.label == "herstelUrl" =>
        e.copy(child = Text(getUrlFromElem(e)))
      case e: Elem if e.label == "customTemplate" => e.copy(attributes = e.attributes.remove("reference"))
      case e                                      => e
    }

    private def getUrlFromElem(e: Elem): String = {
      val scheme      = (e \\ "scheme").head.text
      val host        = (e \\ "address").head.text
      val port        = (e \\ "port").map(_.text).filterNot(_ == "0").map(p => s":$p").headOption.getOrElse("")
      val path        = (e \ "value" \ "path" \\ "head").map(_.text).mkString("/")
      val queryString = (e \\ "rawQueryString" \ "value").map(s => s"?${s.text}").headOption.getOrElse("")

      s"$scheme://$host$port/$path$queryString"
    }
  }
}

class UrlUpcaster extends SingleEventUpcaster {
  import UrlUpcaster._

  override def canUpcast(intermediateRepresentation: IntermediateEventRepresentation): Boolean =
    TargetTypes.contains(intermediateRepresentation.getType)

  override def doUpcast(intermediateRepresentation: IntermediateEventRepresentation): IntermediateEventRepresentation =
    intermediateRepresentation.upcastPayload[String](
      TargetTypes(intermediateRepresentation.getType),
      classOf[String],
      payload => new RuleTransformer(URLUpcasterTransformer).transform(XML.loadString(payload)).mkString
    )
}
