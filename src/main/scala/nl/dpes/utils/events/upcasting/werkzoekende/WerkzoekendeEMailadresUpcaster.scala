package nl.dpes.utils.events.upcasting.werkzoekende

import nl.dpes.core.domain.Werkzoekende._
import org.axonframework.serialization.{SerializedType, SimpleSerializedType}
import org.axonframework.serialization.upcasting.event.{IntermediateEventRepresentation, SingleEventUpcaster}

import scala.xml.{Elem, Node, Text, XML}
import scala.xml.transform.{RewriteRule, RuleTransformer}

object WerkzoekendeEMailadresUpcaster {

  private final val TargetTypes: Map[SerializedType, SerializedType] = Map(
    new SimpleSerializedType(classOf[Geregistreerd].getTypeName, null) -> new SimpleSerializedType(
      classOf[Geregistreerd].getTypeName,
      "1.1"
    ),
    new SimpleSerializedType(classOf[AccountGeimporteerd].getTypeName, null) -> new SimpleSerializedType(
      classOf[AccountGeimporteerd].getTypeName,
      "1.1"
    ),
    new SimpleSerializedType(classOf[WachtwoordVergeten].getTypeName, null) -> new SimpleSerializedType(
      classOf[WachtwoordVergeten].getTypeName,
      "1.1"
    ),
    new SimpleSerializedType(classOf[EMailadresWijzigingVerzocht].getTypeName, null) -> new SimpleSerializedType(
      classOf[EMailadresWijzigingVerzocht].getTypeName,
      "1.1"
    ),
    new SimpleSerializedType(classOf[EMailadresGewijzigd].getTypeName, null) -> new SimpleSerializedType(
      classOf[EMailadresGewijzigd].getTypeName,
      "1.1"
    )
  )

  object EMailadresUpcasterTransformer extends RewriteRule {

    override def transform(n: Node): Seq[Node] = n match {
      case e: Elem if (e \\ "value").nonEmpty && (e.label == "eMailadres" || e.label == "nieuwEMailadres") =>
        e.copy(child = Text((e \\ "value").head.text))
      case e => e
    }
  }
}

class WerkzoekendeEMailadresUpcaster extends SingleEventUpcaster {
  import WerkzoekendeEMailadresUpcaster._

  override def canUpcast(intermediateRepresentation: IntermediateEventRepresentation): Boolean =
    TargetTypes.contains(intermediateRepresentation.getType)

  override def doUpcast(intermediateRepresentation: IntermediateEventRepresentation): IntermediateEventRepresentation =
    intermediateRepresentation.upcastPayload[String](
      TargetTypes(intermediateRepresentation.getType),
      classOf[String],
      payload => new RuleTransformer(EMailadresUpcasterTransformer).transform(XML.loadString(payload)).mkString
    )
}
