package nl.dpes.utils.events.upcasting.converter

import com.thoughtworks.xstream.converters.{Marshalling<PERSON>ontext, UnmarshallingContext}
import com.thoughtworks.xstream.converters.collections.AbstractCollectionConverter
import com.thoughtworks.xstream.io.{HierarchicalStreamReader, HierarchicalStreamWriter}
import com.thoughtworks.xstream.mapper.Mapper

import java.util
import scala.annotation.tailrec

class ListConverter(mapper: Mapper) extends AbstractCollectionConverter(mapper) {

  override def canConvert(`type`: Class[_]): Boolean =
    classOf[::[_]] == `type` || classOf[util.ArrayList[_]] == `type`

  override def marshal(source: scala.Any, writer: HierarchicalStreamWriter, context: MarshallingContext): Unit =
    source.asInstanceOf[List[_]] foreach (writeItem(_, context, writer))

  override def unmarshal(reader: HierarchicalStreamReader, context: UnmarshallingContext): AnyRef = {
    @tailrec
    def loop(acc: List[_]): List[_] =
      if (!reader.hasMoreChildren) {
        acc
      } else {
        reader.moveDown()
        val item = readItem(reader, context, acc)
        reader.moveUp()
        loop(item :: acc)
      }

    loop(List.empty).reverse
  }
}
