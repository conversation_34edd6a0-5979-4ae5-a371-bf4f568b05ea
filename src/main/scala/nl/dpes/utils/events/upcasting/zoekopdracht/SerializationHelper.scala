package nl.dpes.utils.events.upcasting.zoekopdracht

import nl.dpes.utils.events.upcasting.zoekopdracht.SerializationHelper.ListSerializeEnd

import java.io.{ObjectInputStream, ObjectOutputStream}

class SerializationHelper[A](@transient private var orig: List[A]) extends Serializable {

  private def writeObject(out: ObjectOutputStream) {
    out.defaultWriteObject()
    var xs: List[A] = orig
    while (!xs.isEmpty) {
      out.writeObject(xs.head)
      xs = xs.tail
    }
    out.writeObject(ListSerializeEnd)
  }

  // Java serialization calls this before readResolve during deserialization.
  // Read the whole list and store it in `orig`.
  private def readObject(in: ObjectInputStream) {
    in.defaultReadObject()

    val builder = List.newBuilder[A]

    while (true) {
      val v = in.readObject()

      if (v.getClass.getTypeName.endsWith("ListSerializeEnd$")) {
        orig = builder.result()
        return
      } else {
        builder += v.asInstanceOf[A]
      }
    }
  }

  // Provide the result stored in `orig` for Java serialization
  private def readResolve(): AnyRef = orig
}

object SerializationHelper {
  case object ListSerializeEnd
}
