package nl.dpes.utils.events.upcasting.zoekopdracht

import nl.dpes.axon4s.serialization.converters.{ByteArrayToXmlConverter, XmlToByteArrayConverter}
import nl.dpes.core.domain.Zoekopdracht.AangemaaktDoorRecruiter
import org.axonframework.serialization.{Serializer, SimpleSerializedObject, SimpleSerializedType}
import org.axonframework.serialization.upcasting.event.{IntermediateEventRepresentation, SingleEventUpcaster}

object AangemaaktDoorRecruiterUpcaster {

  private final val TargetType: SimpleSerializedType =
    new SimpleSerializedType(classOf[AangemaaktDoorRecruiter].getTypeName, null)
}

class AangemaaktDoorRecruiterUpcaster(serializer: Serializer) extends SingleEventUpcaster {
  import AangemaaktDoorRecruiterUpcaster._

  private val reader: ByteArrayToXmlConverter = new ByteArrayToXmlConverter
  private val writer: XmlToByteArrayConverter = new XmlToByteArrayConverter

  override protected def doUpcast(intermediateRepresentation: IntermediateEventRepresentation): IntermediateEventRepresentation =
    intermediateRepresentation.upcastPayload[Array[Byte]](
      new SimpleSerializedType(TargetType.getName, "1.1"),
      classOf[Array[Byte]],
      bytes => {
        val event = serializer.deserialize[String, AangemaaktDoorRecruiter](
          new SimpleSerializedObject[String](
            new String(bytes),
            classOf[String],
            classOf[AangemaaktDoorRecruiter].getTypeName,
            null
          )
        )

        val upcasted = event.copy(zoekparameters =
          event.zoekparameters.copy(
            zoektermen = event.zoekparameters.zoektermen.map { zoektermen =>
              zoektermen.copy(
                alles = if (zoektermen.alles.contains(Seq(""))) None else zoektermen.alles
              )
            },
            provincies = if (event.zoekparameters.provincies == null) None else event.zoekparameters.provincies,
            gewenstSalaris = if (event.zoekparameters.gewenstSalaris == null) None else event.zoekparameters.gewenstSalaris
          )
        )

        serializer.serialize[String](upcasted, classOf[String]).getData.getBytes
      }
    )

  override protected def canUpcast(intermediateRepresentation: IntermediateEventRepresentation): Boolean =
    intermediateRepresentation.getType.equals(TargetType)
}
