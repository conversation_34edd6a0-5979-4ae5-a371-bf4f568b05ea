package nl.dpes.utils.events

import nl.dpes.core.repositories.TokenRepository
import org.axonframework.common.jdbc.ConnectionProvider
import org.slf4j.Logger

import scala.annotation.tailrec
import scala.concurrent.duration._

object EventStoreMaintenance {
  private final val TokenReleaseCheckInterval = 5 seconds

  trait Task[T] {
    def execute(connectionProvider: ConnectionProvider)(implicit logger: Logger): Either[Throwable, TaskResult[T]]
  }

  final case class TaskResult[T](value: T)
  final case class TaskResults(results: Seq[TaskResult[_]])
}

final class EventStoreMaintenance(
  connectionProvider: ConnectionProvider,
  tokenRepository: TokenRepository,
  token: TokenRepository.Token = TokenRepository.EventStoreMaintenanceToken
)(implicit private val logger: Logger) {
  import EventStoreMaintenance._

  def execute(tasks: Seq[Task[_]]): Either[Throwable, TaskResults] =
    if (tasks.isEmpty) {
      Right(TaskResults(Seq.empty[TaskResult[_]]))
    } else {
      if (tokenRepository.claim(TokenRepository.EventStoreMaintenanceToken)) {
        logger.info(s"Running event store maintenance tasks; ${tasks.length} tasks to be executed...")
        val results = executeTasks(tasks)
        logger.info("Event store maintenance done")

        tokenRepository.release(TokenRepository.EventStoreMaintenanceToken)

        results.fold(
          e => Left(e),
          results => Right(TaskResults(results))
        )
      } else {
        logger.info("Waiting for event store maintenance tasks to finish...")

        while (tokenRepository.fetchToken(TokenRepository.EventStoreMaintenanceToken).nonEmpty)
          Thread.sleep(TokenReleaseCheckInterval.toMillis)

        logger.info("Event store maintenance done; continuing")
        Right(TaskResults(Seq.empty[TaskResult[_]]))
      }
    }

  @tailrec
  private def executeTasks(
    tasks: Seq[Task[_]],
    results: Seq[TaskResult[_]] = Seq.empty[TaskResult[_]]
  ): Either[Throwable, Seq[TaskResult[_]]] =
    if (tasks.isEmpty) {
      Right(results)
    } else {
      tasks.head.execute(connectionProvider) match {
        case Right(result) =>
          executeTasks(tasks.tail, results :+ result)
        case Left(e) =>
          Left(e)
      }
    }
}
