package nl.dpes.utils.events

import java.lang.reflect.{Field, Type}

import io.axoniq.gdpr.api.ReplacementValueProvider
import nl.dpes.core.services.IdentifierService

class DomainAwareReplacementValueProvider(identifierService: IdentifierService) extends ReplacementValueProvider {

  override def replacementValue(
    clazz: Class[_],
    field: Field,
    fieldType: Type,
    groupName: String,
    replacement: String,
    storedPartialValue: Array[Byte]
  ): AnyRef =
    replacement match {
      case "*randomEmailAddress*" => s"${identifierService.generateUUID()}@deleted.persgroep"
      case _                      => super.replacementValue(clazz, field, fieldType, groupName, replacement, storedPartialValue)
    }

}
