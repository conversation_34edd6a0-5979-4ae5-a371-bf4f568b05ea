package nl.dpes.utils.date

import java.time.temporal.TemporalAdjusters
import java.time.{Clock, Instant, ZoneId, ZonedDateTime}

import nl.dpes.core.domain.Frequenties

import scala.util.Random

class SavedSearchClock(clock: Clock, random: Random) {
  private val zoneId = ZoneId.of("Europe/Amsterdam")

  def getFirstScheduleTimeForRecruiter(recruiterId: String, frequentie: Frequenties.Frequentie, creationDate: Instant): Option[Instant] =
    if (frequentie == Frequenties.Wekelijks) {
      calculateSchedule(
        clock.instant().atZone(zoneId).`with`(TemporalAdjusters.previousOrSame(creationDate.atZone(zoneId).getDayOfWeek)),
        frequentie,
        recruiterId.hashCode.toLong
      )
    } else {
      calculateSchedule(clock.instant().atZone(zoneId), frequentie, recruiterId.hashCode.toLong)
    }

  def getNextScheduleTimeForRecruiter(recruiterId: String, frequentie: Frequenties.Frequentie): Option[Instant] =
    calculateSchedule(clock.instant().atZone(zoneId), frequentie, recruiterId.hashCode.toLong)

  private def calculateSchedule(zdt: ZonedDateTime, frequentie: Frequenties.Frequentie, randomNoiseSeed: Long): Option[Instant] = {
    import Frequenties._

    (frequentie match {
      case Nooit => None
      case Dagelijks =>
        Some(
          zdt.plusDays(1).withHour(4).withMinute(0).withSecond(0).withNano(0).toInstant
        )
      case Wekelijks =>
        Some(
          zdt.plusDays(7).withHour(4).withMinute(0).withSecond(0).withNano(0).toInstant
        )
    }).map { instant =>
      random.setSeed(randomNoiseSeed)

      instant.plusSeconds((random.nextInt(120) + 1) * 60)
    }
  }
}
