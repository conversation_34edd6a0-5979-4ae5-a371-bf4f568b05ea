package nl.dpes.utils

import spray.json.{JsValue, JsonFormat, RootJsonFormat}

package object sprayjson {

  implicit class FormatOps[A](val self: JsonFormat[A]) {

    def imap[B](f: A => B, g: B => A): JsonFormat[B] = new JsonFormat[B] {
      override def write(b: B): JsValue = self.write(g(b))

      override def read(json: JsValue): B = f(self.read(json))
    }
  }

  implicit class RootFormatOps[A](val self: RootJsonFormat[A]) {

    def imap[B](f: A => B, g: B => A): RootJsonFormat[B] = new RootJsonFormat[B] {
      override def write(b: B): JsValue = self.write(g(b))

      override def read(json: JsValue): B = f(self.read(json))
    }
  }
}
