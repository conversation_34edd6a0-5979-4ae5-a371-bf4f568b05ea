package nl.dpes.utils.mysql

import java.sql.Connection

import org.quartz.utils.ConnectionProvider
import scalikejdbc.ConnectionPool

class QuartzConnectionProvider extends ConnectionProvider {

  override def getConnection: Connection = {
    val connection = ConnectionPool.borrow()
    connection.setReadOnly(false)
    connection
  }

  override def shutdown(): Unit = ()

  override def initialize(): Unit = ()
}
