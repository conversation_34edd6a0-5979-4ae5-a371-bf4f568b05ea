<config xsi:schemaLocation="urn:org:jgroups http://www.jgroups.org/schema/jgroups.xsd"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xmlns="urn:org:jgroups">
  <TCP
    bind_addr="GLOBAL"
    bind_port="17801"
    recv_buf_size="5M"
    send_buf_size="5M"
    max_bundle_size="64K"

    enable_diagnostics="true"

    thread_pool.enabled="true"
    thread_naming_pattern="cl"
    thread_pool.min_threads="2"
    thread_pool.max_threads="8"
    thread_pool.keep_alive_time="5000"
  />

  <kubernetes.KUBE_PING
    namespace="default"
    labels="app=platform-core"
  />

  <MERGE3
    min_interval="10000"
    max_interval="30000"
  />

  <FD_SOCK
    start_port="7900"
    port_range="0"
  />

  <FD_ALL timeout="30000" />
  <VERIFY_SUSPECT timeout="1500" />
  <BARRIER />

  <pbcast.NAKACK2
    xmit_interval="500"
    xmit_table_num_rows="100"
    xmit_table_msgs_per_row="2000"
    xmit_table_max_compaction_time="30000"
    use_mcast_xmit="false"
    discard_delivered_msgs="true"
  />

  <UNICAST3
    xmit_interval="500"
    xmit_table_num_rows="100"
    xmit_table_msgs_per_row="2000"
    xmit_table_max_compaction_time="60000"
    conn_expiry_timeout="0"
  />

  <pbcast.STABLE
    stability_delay="1000"
    desired_avg_gossip="50000"
    max_bytes="4M"
  />

  <pbcast.GMS
    print_local_addr="true"
    join_timeout="2000"
    view_bundling="true"
  />

  <MFC
    max_credits="2M"
    min_threshold="0.4"
  />

  <FRAG2 frag_size="60K" />

  <RSVP
    resend_interval="2000"
    timeout="10000"
  />

  <pbcast.STATE_TRANSFER />
</config>
