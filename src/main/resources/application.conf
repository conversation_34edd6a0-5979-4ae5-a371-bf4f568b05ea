include "kamon"

akka {
  loglevel = DEBUG
  stdout-loglevel = off

  loggers = ["akka.event.slf4j.Slf4jLogger"]
  event-handlers = ["akka.event.slf4j.Slf4jEventHandler"]

  logger-startup-timeout = 30s

  actor {
    fork-join-executor {
      parallelism-min = 128
      parallelism-max = 128
    }

    debug {
      autoreceive = on
      lifecycle = on
      receive = on
      event-stream = on
    }
  }

  http {
    server {
      idle-timeout = 60s
      request-timeout = 20s
      bind-timeout = 2s
      max-connections = 1024
      backlog = 100
    }

    host-connection-pool {
      max-open-requests = 64
    }

    parsing {
      max-uri-length = 4k
    }
  }
}

scheduling {
  org.quartz {
    scheduler {
      instanceName = QuartzEventScheduler
      instanceId = AUTO
      skipUpdateCheck = true
    }

    threadPool {
      class = org.quartz.simpl.SimpleThreadPool
      threadCount = 2
      threadPriority = 5
    }

    jobStore {
      misfireThreshold = 60000

      class = org.quartz.impl.jdbcjobstore.JobStoreTX
      driverDelegateClass = org.quartz.impl.jdbcjobstore.StdJDBCDelegate
      dataSource = triggers
      tablePrefix = QRTZ_

      isClustered = true
      clusterCheckinInterval = 20000
    }

    dataSource {
      triggers {
        connectionProvider.class = nl.dpes.utils.mysql.QuartzConnectionProvider
      }
    }
  }
}

node.name = "local.node"
node.name = ${?POD_NAME}

nl.dpes.core {
  env = "development"
  env = ${?ENVIRONMENT}

  http {
    bind {
      host = 0.0.0.0
      host = ${?BIND_HOST}
      port = 8999
      port = ${?BIND_PORT}
    }

    api {
      host = 127.0.0.1
      host = ${?API_HOST}
      port = 8999
      port = ${?API_PORT}
      basePath = "api"
    }
  }

  db {
    connectionString = "****************************************************************************************************************************"
    connectionString = ${?DB_CONNECTION_STRING}
    user = platform_core
    user = ${?DB_USER}
    password = password
    password = ${?DB_PASSWORD}
    connectionTimeout = 60000

    connectionPool {
      initialConnections = 2
      maxConnections = 40
    }
  }

  salesforce_gateway.grpc {
    host = "b2b-salesforcegateway-acc.persgroep.digital"
    host = ${?B2B_SALESFORCE_GATEWAY_SERVICE_HOST}
    port = 11310
    port = ${?B2B_SALESFORCE_GATEWAY_SERVICE_PORT}
  }


  sim {
    host = "sim-internal-acc.persgroep.digital"
    host = ${?SIM_SERVICE_HOST}
    port = 80
    port = ${?SIM_SERVICE_PORT}
    path = "/api/v1"
  }

  dynamodb {
    // Local DynamoDB configuration; region is fixed in Docker
    // Acceptance and Production configuration must be empty!
    url = "http://localhost:4666"
    url = ${?DYNAMODB_URL}
    region = "us-west-2"
    region = ${?DYNAMODB_REGION}
  }

  elasticsearch {
    url = "localhost"
    url = ${?ELASTICSEARCH_URL}
    port = 8996
    port = ${?ELASTICSEARCH_PORT}
  }

  kinesis {
    url = "http://localhost:4666"
    url = ${?KINESIS_URL}
    region = "eu-west-1"
  }

  ndsm.service {
    jobseeker {
      baseUrl = "https://api.persgroep.digital/jss-v1/acc"
      baseUrl = ${?JOBSEEKERSERVICE_SERVICE_HOST}
      apiKey = "tn6yEjs0a59Vk00aMnavJ5Jo3LboyMqE47uzkRax"
      apiKey = ${?SERVICE_JOBSEEKERS_KEY}
    }

    mail {
      host = "mail-service-acc.persgroep.digital"
      host = ${?MAIL_SERVICE_SERVICE_HOST}
      port = 80
      port = ${?MAIL_SERVICE_SERVICE_PORT}
    }
  }

  akka.grpc.client."*" {
    use-tls = false
  }

  php {
    executable = "docker run --rm -i php:5.6-alpine"
    executable = ${?PHP_EXECUTABLE}
  }

  security {
    minimumPasswordLength = 8

    jwt {
      issuer = "dpes-platform-core"
    }

    rsa {
      id = "1"
      id = ${?RSA_KEY_ID}
      key = {
**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
        private = ${?RSA_PRIVATE_KEY}
        public = "-----BEGIN PUBLIC KEY-----\nMIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA2mMXmUdI5ZlGyQS6oXbi\nq95Q3to0zGhjRQmx6J8NitbNNojTmqwNPuN8amIHZSThDsuHmhdhNfbAcsWY2hJS\nqT28B0Ws+qpgu+pAaNaIZPDN+ZpeoEbBdZUb42pNjytTyJIryKe3srvoT7vPhjMc\nC2uVjYSPiI9/4rJVZ/iyEMtq6benUHP/N8BlEcU96v2UNpMWkamD4UHuiXZvpaw4\nEj4WzuWirTtf2Yc5E/++7qI7bg730eDgZ5d7jdLCebu+zMwZtbcM5MA8AsE5c8/D\nwEjGxyExR7WBf8x5O6gXrdFYQfFOJ8aHZdSdt3zqOvwOh62U5qUkemYr58ln/IWX\neQIDAQAB\n-----END PUBLIC KEY-----"
        public = ${?RSA_PUBLIC_KEY}
      }
    }

    apikey {
      keys=[ ${?API_KEY_PRIVACY_SERVICE} ]
    }
  }

  notifier {
    events {
      webhookUrl = ""
      webhookUrl = ${?NOTIFIER_EVENTS_WEBHOOK_URL}
      level = "WARNING"
      channel = "#dpes_events"
      username = "NDP [dev]"
      username = ${?NOTIFIER_EVENTS_USERNAME}
      icon = ":boom:"
    }

    rebuild {
      webhookUrl = ""
      webhookUrl = ${?NOTIFIER_EVENTS_WEBHOOK_URL}
      level = "INFO"
      channel = "#dpes_events"
      username = "NDP [dev]"
      username = ${?NOTIFIER_EVENTS_USERNAME}
      icon = ":hourglass:"
    }
  }

  projections {
    core {
      tokenName = "nl.dpes.core.projections.core-v5"
      tokenName = ${?PROJECTIONS_CORE_TOKENNAME}
      version = 5
      version = ${?PROJECTIONS_CORE_VERSION}
    }

    index {
      tokenName = "nl.dpes.core.projections.index-v5"
      tokenName = ${?PROJECTIONS_INDEX_TOKENNAME}
      version = 5
      version = ${?PROJECTIONS_INDEX_VERSION}
    }

    elasticsearch {
      tokenName = "nl.dpes.core.projections.elasticsearch-v4"
      tokenName = ${?PROJECTIONS_ELASTICSEARCH_TOKENNAME}
      version = 4
      version = ${?PROJECTIONS_ELASTICSEARCH_VERSION}
    }
  }

  eventPublisher {
    domain {
      streamName = "dev-domain"
      streamName = ${?EVENTPUBLISHER_DOMAIN_STREAMNAME}
    }
  }

  event_bus {
    domain_event_stream = "dev-domain"
    domain_event_stream = ${?EVENTPUBLISHER_DOMAIN_STREAMNAME}
  }

  axon {
    distributedCommandGateway = false
    distributedCommandGateway = ${?AXON_DISTRIBUTED_COMMAND_GATEWAY}
  }

  profileService {
    favoritesTableName = "favorite_profiles"
    savedSearchTableName = "saved_search"
    databaseUrl = "**************************************************************************************************************************************"
    databaseUrl = ${?PROFILE_SERVICE_DATABASE_CONNECTION_STRING}
    threadCount = 16
    databaseUsername = "profile-service"
    databaseUsername = ${?PROFILE_SERVICE_DATABASE_USER}
    databasePassword = "password"
    databasePassword = ${?PROFILE_SERVICE_DATABASE_PASSWORD}

    favoriteProfilesProjection {
      tokenName = "nl.dpes.core.projections.favoriteprofiles-v"
      tokenName = ${?FAVORITE_PROFILES_PROJECTION_TOKENNAME}
      version = 1
      version = ${?FAVORITE_PROFILES_PROJECTION_VERSION}
    }

    savedSearchProjection {
      tokenName = "nl.dpes.core.projections.savedsearch-v"
      tokenName = ${?SAVED_SEARCH_PROJECTION_TOKENNAME}
      version = 1
      version = ${?SAVED_SEARCH_PROJECTION_VERSION}
    }
  }
}
