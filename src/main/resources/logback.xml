<?xml version="1.0" encoding="UTF-8"?>
<configuration scan="false">
  <contextName>CORE</contextName>

  <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
    <encoder>
      <pattern>[%contextName] %date{ISO8601} %-5level %logger{36} %X{sourceThread} - %replace(%msg){'\n', '\\n'}%n</pattern>
    </encoder>
  </appender>

  <logger name="scalikejdbc" level="INFO" />
  <logger name="io.grpc.netty" level="INFO" />

  <root level="INFO">
    <appender-ref ref="STDOUT" />
  </root>
</configuration>
