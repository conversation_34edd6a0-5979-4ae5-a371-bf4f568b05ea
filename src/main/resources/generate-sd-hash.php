<?php

class Password {

    /**
     * The Blowfish cycle count to lower performance.
     *
     * WARNING: when changing this value, ALL passwords in the system will need to be re-generated!
     *
     * Must be in range 4-31 (inclusive).
     * Every +1 cycle doubles the running time of the algorithm. The following table indicates running time on a 2.8GHz.
     * +--------+--------+
     * | Cycles | Time   |
     * +--------+--------+
     * | 10     | 0.08s  |
     * | 11     | 0.14s  |
     * | 12     | 0.28s  |
     * | 13     | 0.57s  |
     * | 14     | 1.14s  |
     * | 15     | 2.28s  |
     * | 16     | 4.56s  |
     * +--------+--------+
     * @see http://php.net/manual/en/function.crypt.php - section CRYPT_BLOWFISH
     *
     * @var integer
     */
    const BLOWFISH_CYCLE_COUNT = 11;

    /**
     * Required length for salt.
     *
     * @var integer
     */
    const BCRYPT_SALT_LENGTH = 22;

    /**
     * Regex escaped value of allowed characters in Bcrypt salt.
     *
     * @var string
     */
    const BCRYPT_ALLOWED_SALT_CHARS = '.\/0-9A-Za-z'; // keep escaped slash '/'

    /**
     * Prepares the given string for Bcrypt salt.
     *
     * Strips invalid characters and respects salt length.
     */
    private static function makeSalt($input)
    {
        $input = 'M3e0W/' . $input;

        $salt = preg_replace(
            '/[^' . self::BCRYPT_ALLOWED_SALT_CHARS . ']+/',
            '.',
            $input
        );

        if (strlen($salt) > self::BCRYPT_SALT_LENGTH) {
            $salt = substr($salt, 0, self::BCRYPT_SALT_LENGTH);
        }
        else if ( strlen( $salt ) < self::BCRYPT_SALT_LENGTH ) {
          $salt = str_pad( $salt, self::BCRYPT_SALT_LENGTH, '$' );
        }

        return $salt;
    }

    /**
     * @param string $input
     * @param string $salt
     * @return string
     * @throws Exception
     */
    public static function hash($input, $salt)
    {
        if ($input === '') {
            throw new \Exception('The $input parameter can not be empty.');
        }

        if ($salt === '') {
            throw new \Exception('The $salt parameter can not be empty.');
        }

        $salt = self::makeSalt($salt);

        // @see http://php.net/manual/en/function.crypt.php
        $saltPrefix = sprintf(
            '$2a$%02d$',
            self::BLOWFISH_CYCLE_COUNT
        );
        $cryptSalt = $saltPrefix . $salt;

        // using Bcrypt - Blowfish with a salt
        $hash = crypt($input, $cryptSalt);

        if (empty($hash) || substr($hash, 0, strlen($saltPrefix)) != $saltPrefix) {
            throw new \Exception('Failed to generate hash from string.');
        }

        // hide algorithm and salt information
        return sha1($hash);
    }

    /**
     * @param string $input
     * @param string $hash
     * @param string $salt
     * @return bool
     * @throws Exception
     */
    public static function verify($input, $hash, $salt)
    {
        if ($hash === '') {
            throw new \Exception('The $hash parameter can not be empty.');
        }

        $hashedInput = self::hash($input, $salt);
        return ($hashedInput === $hash);
    }
}

// usage: generate-sd-hash.php salt password
// salt is the “original email address” in the sd jobseeker
try{
    $password = base64_decode($argv[1]);
    $salt = base64_decode($argv[2]);

    echo Password::hash($password, $salt);
} catch(\Exception $exception) {
    var_dump($exception);
    exit(1);
}
