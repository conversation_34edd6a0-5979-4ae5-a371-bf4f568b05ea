<?xml version="1.0" encoding="UTF-8"?>
<configuration scan="false">
  <contextName>CORE</contextName>

  <appender name="JSON" class="ch.qos.logback.core.ConsoleAppender">
    <encoder class="ch.qos.logback.core.encoder.LayoutWrappingEncoder">
      <layout class="ch.qos.logback.contrib.json.classic.JsonLayout">
        <jsonFormatter class="ch.qos.logback.contrib.jackson.JacksonJsonFormatter"></jsonFormatter>
        <timestampFormat>yyyy-MM-dd' 'HH:mm:ss.SSS</timestampFormat>
        <appendLineSeparator>true</appendLineSeparator>
      </layout>
    </encoder>
  </appender>

  <root level="${LOGGING_LEVEL:-INFO}">
    <appender-ref ref="JSON"/>
  </root>
</configuration>
