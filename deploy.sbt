import java.io.ByteArrayInputStream

import sbt.complete.DefaultParsers.spaceDelimited

import scala.annotation.tailrec
import scala.concurrent.duration._
import scala.sys.process._
import scala.util.{Failure, Success, Try}

def getKubernetesClusterContext(targetEnvironment: String): Option[String] = {
  val hosts: String = s"host api.live.$targetEnvironment.ndp.kops.cloud" !!
  val pattern = """is an alias for api\.(.*)\.""".r
  pattern.findAllIn(hosts).matchData.toSeq.headOption.map[String](_.group(1))
}

lazy val deploy = inputKey[Unit]("Deploys to Kubernetes cluster")
deploy := {
  val args: Seq[String] = spaceDelimited("<arg>").parsed
  lazy val buildNumber = args(1)
  lazy val targetEnvironment = args.head
  lazy val kubernetesFiles: List[String] = List(
    s"deployment/kubernetes/configmaps/configmap-$targetEnvironment.yml",
    s"deployment/kubernetes/platform-core-service-$targetEnvironment.yml",
    "deployment/kubernetes/platform-core-deployment.yml"
  )

  def getConfig(file: String): Option[String] = {
    val source = scala.io.Source.fromFile(file)
    val config = Try(source.mkString).map(_.replaceAll("\\{\\{BUILD_NUMBER}}", buildNumber))
    source.close()
    config match {
      case Success(result) => Some(result)
      case Failure(error) =>
        streams.value.log.error(s"Error reading config file $file: ${error.getMessage}")
        sys.exit(1)
        None
    }
  }

  val configs = (for {
    file <- kubernetesFiles
    config <- getConfig(file)
  } yield config).mkString("\n---\n")

  getKubernetesClusterContext(targetEnvironment) match {
    case Some(kubernetesContext) => {
      streams.value.log.info(
        s"kubectl --context=$kubernetesContext apply --record -f -" #< new ByteArrayInputStream(configs.getBytes()) !!
      )
      streams.value.log.info(
        s"kubectl --context=$kubernetesContext rollout status deployment platform-core" !!
      )
    }
    case None => sys.exit(1)
  }
}

lazy val rollback = inputKey[Unit]("Rollback previous deployment")
rollback := {
  val args: Seq[String] = spaceDelimited("<arg>").parsed
  lazy val targetEnvironment = args.head

  getKubernetesClusterContext(targetEnvironment) match {
    case Some(kubernetesContext) => streams.value.log.info(
      s"kubectl --context=$kubernetesContext rollout undo deployment platform-core" !!
    )
    case None => sys.exit(1)
  }
}

lazy val rebuildProjections = inputKey[Unit]("Rebuild projections to the given version")
rebuildProjections := {
  val args: Seq[String] = spaceDelimited("<arg>").parsed

  lazy val targetEnvironment = args.head
  lazy val buildNumber = args(1)
  lazy val projectionVersion = args(2)

  val jobName: String = s"platform-core-replay-$buildNumber"

  def getConfig(file: String): Option[String] = {
    val source = scala.io.Source.fromFile(file)
    val config = Try(source.mkString)
      .map(_.replaceAll("\\{\\{BUILD_NUMBER}}", buildNumber))
      .map(_.replaceAll("\\{\\{PROJECTION_VERSION}}", projectionVersion))
    source.close()
    config.toOption
  }

  val kubernetesClusterContext:Option[String] = getKubernetesClusterContext(targetEnvironment)

  kubernetesClusterContext match {
    case Some(kubernetesContext) =>
      streams.value.log.info(
        s"kubectl --context=$kubernetesContext apply -f -" #<
          new ByteArrayInputStream(getConfig("deployment/kubernetes/jobs/replay-job.yml").mkString.getBytes()) !!)
      Thread.sleep(5.second.toMillis)
      waitForCompletion(kubernetesContext)
    case None => sys.exit(1)
  }

  @tailrec
  def waitForCompletion(kubernetesContext: String): String = {
    val result: String =
      s"kubectl --context=$kubernetesContext get job $jobName --output=jsonpath={.status.conditions[].type}" !!

    result.trim match {
      case "Failed" =>
        streams.value.log.info("Job failed")
        sys.exit(1)
      case "Complete" =>
        streams.value.log.info("Job complete")
        // delete job?
        sys.exit(0)
      case x =>
        Thread.sleep(1.second.toMillis)
        waitForCompletion(kubernetesContext)
    }
  }
}

lazy val downloadAxonIqLicense = taskKey[Unit]("Download the AxonIQ license file from Kubernetes")

downloadAxonIqLicense := {
  import java.nio.charset.StandardCharsets
  import java.util.Base64

  import scala.tools.nsc.io.File
  import scala.util.{Failure, Success, Try}

  Try {
    getKubernetesClusterContext("acc").map(kubernetesContext =>
      s"kubectl --context $kubernetesContext get secret -o=jsonpath={.data.license} axoniq-gdpr" !!
      ).get
  } map { license =>
    Base64.getDecoder.decode(license.trim.getBytes(StandardCharsets.UTF_8)).map(_.toChar).mkString
  } match {
    case Success(license) =>
      File("axoniq.license").writeAll(license)
      streams.value.log.info("Successfully downloaded and parsed license file from Kubernetes")
    case Failure(e) =>
      streams.value.log.error(s"Failed to download or parse license file from Kubernetes: ${e.getMessage}")
  }
}

Compile / run := {
  downloadAxonIqLicense.value
  (Compile / run).evaluated
}

reStart := {
  downloadAxonIqLicense.value
  reStart.evaluated
}

Test / test := {
  downloadAxonIqLicense.value
  (Test / test).value
}
