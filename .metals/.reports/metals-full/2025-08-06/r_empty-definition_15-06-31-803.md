error id: file://<WORKSPACE>/src/main/scala/nl/dpes/core/domain/Zoekopdracht.scala:`<none>`.
file://<WORKSPACE>/src/main/scala/nl/dpes/core/domain/Zoekopdracht.scala
empty definition using pc, found symbol in pc: `<none>`.
empty definition using semanticdb
empty definition using fallback
non-local guesses:
	 -cats/implicits/OpgeslagenZoekopdracht#
	 -nl/dpes/core/domain/Zoekopdracht.OpgeslagenZoekopdracht#
	 -scala/concurrent/duration/OpgeslagenZoekopdracht#
	 -OpgeslagenZoekopdracht#
	 -scala/Predef.OpgeslagenZoekopdracht#
offset: 876
uri: file://<WORKSPACE>/src/main/scala/nl/dpes/core/domain/Zoekopdracht.scala
text:
```scala
package nl.dpes.core.domain

import cats.implicits._
import nl.dpes.axon4s.annotations.targetAggregateIdentifier
import nl.dpes.core.domain.Zoekopdracht._
import nl.dpes.core.services.profileservice.SearchFilters
import org.axonframework.commandhandling.CommandHandler
import org.axonframework.eventsourcing.EventSourcingHandler
import org.axonframework.modelling.command.{AggregateIdentifier, AggregateRoot}
import org.axonframework.modelling.command.AggregateLifecycle.apply
import org.axonframework.serialization.Revision
import org.slf4j.{Logger, LoggerFactory}

import scala.concurrent.duration._

@AggregateRoot
class Zoekopdracht {

  @AggregateIdentifier
  private var id: String                     = _
  private var verwijderd: Boolean            = false
  private var zoekparameters: Zoekparameters = _

  private var opgeslagenZoekopdrachten: Map[String, Opgeslage@@nZoekopdracht] =
    Map.empty[String, OpgeslagenZoekopdracht]

  @CommandHandler
  def this(command: Zoekopdracht.MaakAanDoorRecruiter) = {
    this

    apply(
      Zoekopdracht.AangemaaktDoorRecruiter(
        command.zoekopdrachtId,
        command.zoekparameters
      )
    )
  }

  @EventSourcingHandler
  def onAangemaaktDoorRecruiter(event: AangemaaktDoorRecruiter): Unit = {
    this.id = event.zoekopdrachtId
    this.zoekparameters = event.zoekparameters
  }

  @CommandHandler
  def slaOpDoorRecruiter(command: SlaOpDoorRecruiter): Unit =
    if (!this.opgeslagenZoekopdrachten.contains(command.recruiterId)) {
      apply(
        OpgeslagenDoorRecruiter(
          command.zoekopdrachtId,
          command.recruiterId,
          command.naam,
          command.frequentie,
          this.zoekparameters
        )
      )
    } else {
      this.opgeslagenZoekopdrachten.get(command.recruiterId) map { opgeslagenZoekopdrachten =>
        if (opgeslagenZoekopdrachten.frequentie != command.frequentie || opgeslagenZoekopdrachten.naam != command.naam) {
          apply(Gewijzigd(command.zoekopdrachtId, command.recruiterId, command.naam, command.frequentie))
        }
      }
    }

  @EventSourcingHandler
  def onOpgeslagenDoorRecruiter(event: OpgeslagenDoorRecruiter): Unit =
    this.opgeslagenZoekopdrachten = this.opgeslagenZoekopdrachten + (event.recruiterId -> OpgeslagenZoekopdracht(
      event.zoekopdrachtId,
      event.recruiterId,
      event.naam,
      event.frequentie
    ))

  @CommandHandler
  def verwijderDoorRecruiter(command: VerwijderDoorRecruiter): Unit = {
    if (this.opgeslagenZoekopdrachten.contains(command.recruiterId)) {
      apply(
        VerwijderdDoorRecruiter(command.zoekopdrachtId, command.recruiterId)
      )
    }

    if (this.opgeslagenZoekopdrachten.isEmpty && !this.verwijderd) {
      apply(Verwijderd(command.zoekopdrachtId))
    }
  }

  @EventSourcingHandler
  def onGewijzigd(event: Gewijzigd): Unit =
    this.opgeslagenZoekopdrachten.get(event.recruiterId) foreach { opgeslagenZoekopdrachten =>
      this.opgeslagenZoekopdrachten = this.opgeslagenZoekopdrachten ++
        Map(
          event.recruiterId ->
          OpgeslagenZoekopdracht(
            opgeslagenZoekopdrachten.zoekopdrachtId,
            opgeslagenZoekopdrachten.accountId,
            event.naam,
            event.frequentie
          )
        )
    }

  @EventSourcingHandler
  def onVerwijderdDoorRecruiter(event: VerwijderdDoorRecruiter): Unit =
    this.opgeslagenZoekopdrachten = this.opgeslagenZoekopdrachten - event.recruiterId

  @EventSourcingHandler
  def onVerwijderd(event: Verwijderd): Unit =
    this.verwijderd = true
}

object Zoekopdracht {

  sealed trait ZoekopdrachtEvent extends Event {
    val zoekopdrachtId: String
  }

  protected[domain] trait OpgeslagenZoekopdrachtEvent extends Event {
    val zoekopdrachtId: String

    def accountId: String

    def getOpgeslagenZoekopdrachtId: String = s"${zoekopdrachtId}_$accountId"
  }

  final case class MaakAanDoorRecruiter(@targetAggregateIdentifier zoekopdrachtId: String, zoekparameters: Zoekparameters)

  @Revision("1.1")
  final case class AangemaaktDoorRecruiter(zoekopdrachtId: String, zoekparameters: Zoekparameters) extends ZoekopdrachtEvent

  final case class SlaOpDoorRecruiter(
    @targetAggregateIdentifier zoekopdrachtId: String,
    recruiterId: String,
    naam: String,
    frequentie: Frequenties.Frequentie
  )

  @Revision("1.1")
  final case class OpgeslagenDoorRecruiter(
    zoekopdrachtId: String,
    recruiterId: String,
    naam: String,
    frequentie: Frequenties.Frequentie,
    zoekparameters: Zoekparameters
  ) extends OpgeslagenZoekopdrachtEvent
      with ZoekopdrachtEvent {

    override def accountId: String = recruiterId
  }

  final case class Gewijzigd(zoekopdrachtId: String, recruiterId: String, naam: String, frequentie: Frequenties.Frequentie)
      extends OpgeslagenZoekopdrachtEvent
      with ZoekopdrachtEvent {

    override def accountId: String = recruiterId
  }

  final case class VerwijderDoorRecruiter(@targetAggregateIdentifier zoekopdrachtId: String, recruiterId: String)

  final case class VerwijderdDoorRecruiter(zoekopdrachtId: String, recruiterId: String)
      extends OpgeslagenZoekopdrachtEvent
      with ZoekopdrachtEvent {

    override def accountId: String = recruiterId
  }

  final case class Verwijderd(zoekopdrachtId: String) extends ZoekopdrachtEvent

}

final case class OpgeslagenZoekopdracht(
  zoekopdrachtId: String,
  accountId: String,
  naam: String,
  frequentie: Frequenties.Frequentie
)

final case class Zoekparameters(
  zoektermen: Option[Zoektermen] = None,
  locatie: Option[String] = None,
  wijzigingsdatum: Option[String] = None,
  opleidingsniveaus: Option[Seq[String]] = None,
  aantallenUren: Option[Seq[String]] = None,
  soortenWerk: Option[Seq[String]] = None,
  beschikbaarheden: Option[Seq[String]] = None,
  rijbewijzen: Option[Seq[String]] = None,
  talen: Option[Seq[String]] = None,
  afstandTotWerklocatie: Option[String] = None,
  carriereniveau: Option[Seq[String]] = None,
  functiegroep: Option[Seq[String]] = None,
  gewenstSalaris: Option[Seq[String]] = None,
  provincies: Option[Seq[String]] = None
)

object ZoekparametersHelper {

  implicit val logger: Logger = LoggerFactory.getLogger(getClass)

  def toSearchFilters(zoekparameters: Zoekparameters): Either[Throwable, SearchFilters] =
    Either
      .catchNonFatal {
        SearchFilters(
          searchTerm = zoekparameters.zoektermen
            .flatMap(_.alles)
            .collect { case elements if elements.nonEmpty => elements.mkString(" ") },
          city = zoekparameters.locatie,
          provinces = zoekparameters.provincies,
          updatedDate = zoekparameters.wijzigingsdatum,
          functionGroups = zoekparameters.functiegroep,
          workLevels = zoekparameters.opleidingsniveaus,
          workingHours = zoekparameters.aantallenUren,
          careerLevels = zoekparameters.carriereniveau,
          requestedSalaries = zoekparameters.gewenstSalaris,
          availabilities = zoekparameters.beschikbaarheden,
          driversLicenses = zoekparameters.rijbewijzen,
          languages = zoekparameters.talen
        )
      }
      .leftMap { ex =>
        val thr = new Throwable(s"Cannot map '$zoekparameters', because of: ${getNestedExceptions(ex)}")
        logger.error(thr.getMessage)
        thr
      }

  private def getNestedExceptions(thr: Throwable): String = {
    val causes = Iterator
      .iterate[Throwable](thr)(_.getCause)
      .takeWhile(_ != null)
      .map(e => s"${e.getClass.getName}: ${e.getMessage}")
      .mkString(" -> ")
    s"$causes"
  }
}

final case class Zoektermen(
  alles: Option[Seq[String]] = None,
  opleidingNaam: Option[Seq[String]] = None,
  opleidingBeschrijving: Option[Seq[String]] = None,
  gewensteBaan: Option[Seq[String]] = None,
  functieTitel: Option[Seq[String]] = None,
  functieBeschrijving: Option[Seq[String]] = None,
  cursussen: Option[Seq[String]] = None
)

object Frequenties extends Enumeration {
  type Frequentie = Value
  val Dagelijks, Wekelijks, Nooit = Value

  implicit def frequentieToDuration(frequentie: Frequentie): Duration =
    frequentie match {
      case Nooit     => Duration.Inf
      case Dagelijks => 1 day
      case Wekelijks => 7 days
    }

  def frequentieToWijzigingsDatumFilter(frequentie: Frequentie): String =
    frequentie match {
      case Dagelijks => "Afgelopen 24 uur"
      case _         => "Afgelopen 24 uur" // default
    }
}

```


#### Short summary: 

empty definition using pc, found symbol in pc: `<none>`.