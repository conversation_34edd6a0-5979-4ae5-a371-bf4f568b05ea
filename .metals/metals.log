2025.08.06 15:01:44 INFO  Started: Metals version 1.6.1 in folders '/home/<USER>/projects/platform.core' for client Visual Studio Code 1.102.3.
2025.08.06 15:01:45 INFO  tracing is disabled for protocol mcp, to enable tracing of incoming and outgoing JSON messages create an empty file at /home/<USER>/projects/platform.core/.metals/mcp.trace.json or /home/<USER>/.cache/metals/mcp.trace.json
Aug 06, 2025 3:01:45 PM io.undertow.Undertow start
INFO: starting server: Undertow - 2.3.12.Final
Aug 06, 2025 3:01:45 PM org.xnio.Xnio <clinit>
INFO: XNIO version 3.8.16.Final
Aug 06, 2025 3:01:45 PM org.xnio.nio.NioXnio <clinit>
INFO: XNIO NIO Implementation Version 3.8.16.Final
2025.08.06 15:01:45 INFO  running '/usr/bin/sbt -Dbloop.export-jar-classifiers=sources bloopInstall'
Aug 06, 2025 3:01:45 PM org.jboss.threads.Version <clinit>
INFO: JBoss Threads version 3.5.0.Final
2025.08.06 15:01:45 INFO  Metals MCP server started on port: 37177.
2025.08.06 15:01:45 WARN  no build target for: /home/<USER>/projects/platform.core/project/metals.sbt
2025.08.06 15:01:45 WARN  no build target for: /home/<USER>/projects/platform.core/project/project/metals.sbt
2025.08.06 15:01:45 INFO  no build target found for /home/<USER>/projects/platform.core/project/project/metals.sbt. Using presentation compiler with project's scala-library version: 3.3.6
Aug 06, 2025 3:01:46 PM org.eclipse.lsp4j.jsonrpc.services.GenericEndpoint notify
INFO: Unsupported notification method: $/setTrace
Align: top-level presets deprecated; use 'preset' subsection
IndentOperator: top-level presets deprecated; use 'preset' subsection
DanglingParentheses: top-level presets deprecated; use 'preset' subsection
2025.08.06 15:01:46 INFO  [info] welcome to sbt 1.6.1 (Eclipse Adoptium Java 21.0.7)
2025.08.06 15:01:47 ERROR error:
2025.08.06 15:01:47 ERROR   bad constant pool index: 0 at pos: 48454
2025.08.06 15:01:47 ERROR      while compiling: <no file>
2025.08.06 15:01:47 ERROR         during phase: globalPhase=<no phase>, enteringPhase=<some phase>
2025.08.06 15:01:47 ERROR      library version: version 2.12.15
2025.08.06 15:01:47 ERROR     compiler version: version 2.12.15
2025.08.06 15:01:47 ERROR   reconstructed args: -classpath /home/<USER>/.sbt/boot/scala-2.12.15/lib/scala-library.jar -Yrangepos
2025.08.06 15:01:47 ERROR 
2025.08.06 15:01:47 ERROR   last tree to typer: EmptyTree
2025.08.06 15:01:47 ERROR        tree position: <unknown>
2025.08.06 15:01:47 ERROR             tree tpe: <notype>
2025.08.06 15:01:47 ERROR               symbol: null
2025.08.06 15:01:47 ERROR            call site: <none> in <none>
2025.08.06 15:01:47 ERROR 
2025.08.06 15:01:47 ERROR == Source file context for tree position ==
2025.08.06 15:01:47 ERROR 
2025.08.06 15:01:47 ERROR error:
2025.08.06 15:01:47 ERROR   bad constant pool index: 0 at pos: 48454
2025.08.06 15:01:47 ERROR      while compiling: <no file>
2025.08.06 15:01:47 ERROR         during phase: globalPhase=<no phase>, enteringPhase=<some phase>
2025.08.06 15:01:47 ERROR      library version: version 2.12.15
2025.08.06 15:01:47 ERROR     compiler version: version 2.12.15
2025.08.06 15:01:47 ERROR   reconstructed args: -classpath /home/<USER>/.sbt/boot/scala-2.12.15/lib/scala-library.jar -Yrangepos
2025.08.06 15:01:47 ERROR 
2025.08.06 15:01:47 ERROR   last tree to typer: EmptyTree
2025.08.06 15:01:47 ERROR        tree position: <unknown>
2025.08.06 15:01:47 ERROR             tree tpe: <notype>
2025.08.06 15:01:47 ERROR               symbol: null
2025.08.06 15:01:47 ERROR            call site: <none> in <none>
2025.08.06 15:01:47 ERROR 
2025.08.06 15:01:47 ERROR == Source file context for tree position ==
2025.08.06 15:01:47 ERROR 
2025.08.06 15:01:47 ERROR Exception in thread "sbt-parser-init-thread" java.lang.ExceptionInInitializerError
2025.08.06 15:01:47 ERROR 	at sbt.internal.parser.SbtParserInit$$anon$2.run(SbtParser.scala:191)
2025.08.06 15:01:47 ERROR Caused by: scala.reflect.internal.FatalError: 
2025.08.06 15:01:47 ERROR   bad constant pool index: 0 at pos: 48454
2025.08.06 15:01:47 ERROR      while compiling: <no file>
2025.08.06 15:01:47 ERROR         during phase: globalPhase=<no phase>, enteringPhase=<some phase>
2025.08.06 15:01:47 ERROR      library version: version 2.12.15
2025.08.06 15:01:47 ERROR     compiler version: version 2.12.15
2025.08.06 15:01:47 ERROR   reconstructed args: -classpath /home/<USER>/.sbt/boot/scala-2.12.15/lib/scala-library.jar -Yrangepos
2025.08.06 15:01:47 ERROR 
2025.08.06 15:01:47 ERROR   last tree to typer: EmptyTree
2025.08.06 15:01:47 ERROR        tree position: <unknown>
2025.08.06 15:01:47 ERROR             tree tpe: <notype>
2025.08.06 15:01:47 ERROR               symbol: null
2025.08.06 15:01:47 ERROR            call site: <none> in <none>
2025.08.06 15:01:47 ERROR 
2025.08.06 15:01:47 ERROR == Source file context for tree position ==
2025.08.06 15:01:47 ERROR 
2025.08.06 15:01:47 ERROR 
2025.08.06 15:01:47 ERROR 	at scala.reflect.internal.Reporting.abort(Reporting.scala:69)
2025.08.06 15:01:47 ERROR 	at scala.reflect.internal.Reporting.abort$(Reporting.scala:65)
2025.08.06 15:01:47 ERROR 	at scala.reflect.internal.SymbolTable.abort(SymbolTable.scala:28)
2025.08.06 15:01:47 ERROR 	at scala.tools.nsc.symtab.classfile.ClassfileParser$ConstantPool.errorBadIndex(ClassfileParser.scala:386)
2025.08.06 15:01:47 ERROR 	at scala.tools.nsc.symtab.classfile.ClassfileParser$ConstantPool.getExternalName(ClassfileParser.scala:250)
2025.08.06 15:01:47 ERROR 	at scala.tools.nsc.symtab.classfile.ClassfileParser.readParamNames$1(ClassfileParser.scala:841)
2025.08.06 15:01:47 ERROR 	at scala.tools.nsc.symtab.classfile.ClassfileParser.parseAttribute$1(ClassfileParser.scala:847)
2025.08.06 15:01:47 ERROR 	at scala.tools.nsc.symtab.classfile.ClassfileParser.$anonfun$parseAttributes$7(ClassfileParser.scala:921)
2025.08.06 15:01:47 ERROR 	at scala.tools.nsc.symtab.classfile.ClassfileParser.parseAttributes(ClassfileParser.scala:921)
2025.08.06 15:01:47 ERROR 	at scala.tools.nsc.symtab.classfile.ClassfileParser.parseMethod(ClassfileParser.scala:623)
2025.08.06 15:01:47 ERROR 	at scala.tools.nsc.symtab.classfile.ClassfileParser.$anonfun$parseClass$4(ClassfileParser.scala:536)
2025.08.06 15:01:47 ERROR 	at scala.tools.nsc.symtab.classfile.ClassfileParser.parseClass(ClassfileParser.scala:536)
2025.08.06 15:01:47 ERROR 	at scala.tools.nsc.symtab.classfile.ClassfileParser.$anonfun$parse$2(ClassfileParser.scala:161)
2025.08.06 15:01:47 ERROR 	at scala.tools.nsc.symtab.classfile.ClassfileParser.$anonfun$parse$1(ClassfileParser.scala:147)
2025.08.06 15:01:47 ERROR 	at scala.tools.nsc.symtab.classfile.ClassfileParser.parse(ClassfileParser.scala:130)
2025.08.06 15:01:47 ERROR 	at scala.tools.nsc.symtab.SymbolLoaders$ClassfileLoader.doComplete(SymbolLoaders.scala:343)
2025.08.06 15:01:47 ERROR 	at scala.tools.nsc.symtab.SymbolLoaders$SymbolLoader.complete(SymbolLoaders.scala:250)
2025.08.06 15:01:47 ERROR 	at scala.reflect.internal.Symbols$Symbol.completeInfo(Symbols.scala:1542)
2025.08.06 15:01:47 ERROR 	at scala.reflect.internal.Symbols$Symbol.info(Symbols.scala:1514)
2025.08.06 15:01:47 ERROR 	at scala.reflect.internal.Definitions.scala$reflect$internal$Definitions$$enterNewMethod(Definitions.scala:49)
2025.08.06 15:01:47 ERROR 	at scala.reflect.internal.Definitions$DefinitionsClass.String_$plus$lzycompute(Definitions.scala:1134)
2025.08.06 15:01:47 ERROR 	at scala.reflect.internal.Definitions$DefinitionsClass.String_$plus(Definitions.scala:1134)
2025.08.06 15:01:47 ERROR 	at scala.reflect.internal.Definitions$DefinitionsClass.syntheticCoreMethods$lzycompute(Definitions.scala:1438)
2025.08.06 15:01:47 ERROR 	at scala.reflect.internal.Definitions$DefinitionsClass.syntheticCoreMethods(Definitions.scala:1420)
2025.08.06 15:01:47 ERROR 	at scala.reflect.internal.Definitions$DefinitionsClass.symbolsNotPresentInBytecode$lzycompute(Definitions.scala:1450)
2025.08.06 15:01:47 ERROR 	at scala.reflect.internal.Definitions$DefinitionsClass.symbolsNotPresentInBytecode(Definitions.scala:1450)
2025.08.06 15:01:47 ERROR 	at scala.reflect.internal.Definitions$DefinitionsClass.init(Definitions.scala:1506)
2025.08.06 15:01:47 ERROR 	at scala.tools.nsc.Global$Run.<init>(Global.scala:1213)
2025.08.06 15:01:47 ERROR 	at sbt.internal.parser.SbtParser$.<init>(SbtParser.scala:141)
2025.08.06 15:01:47 ERROR 	at sbt.internal.parser.SbtParser$.<clinit>(SbtParser.scala)
2025.08.06 15:01:47 ERROR 	... 1 more
2025.08.06 15:01:47 INFO  no build target found for /home/<USER>/projects/platform.core/project/metals.sbt. Using presentation compiler with project's scala-library version: 3.3.6
2025.08.06 15:01:46 INFO  [info] loading global plugins from /home/<USER>/.sbt/1.0/plugins
2025.08.06 15:01:48 ERROR java.lang.NoClassDefFoundError: Could not initialize class sbt.internal.parser.SbtParser$
2025.08.06 15:01:48 ERROR 	at sbt.internal.parser.SbtParser.splitExpressions(SbtParser.scala:247)
2025.08.06 15:01:48 ERROR 	at sbt.internal.parser.SbtParser.<init>(SbtParser.scala:236)
2025.08.06 15:01:48 ERROR 	at sbt.internal.EvaluateConfigurations$.splitExpressions(EvaluateConfigurations.scala:289)
2025.08.06 15:01:48 ERROR 	at sbt.internal.EvaluateConfigurations$.parseConfiguration(EvaluateConfigurations.scala:98)
2025.08.06 15:01:48 ERROR 	at sbt.internal.EvaluateConfigurations$.evaluateSbtFile(EvaluateConfigurations.scala:147)
2025.08.06 15:01:48 ERROR 	at sbt.internal.Load$.loadSettingsFile$1(Load.scala:1120)
2025.08.06 15:01:48 ERROR 	at sbt.internal.Load$.$anonfun$discoverProjects$2(Load.scala:1130)
2025.08.06 15:01:48 ERROR 	at scala.collection.MapLike.getOrElse(MapLike.scala:131)
2025.08.06 15:01:48 ERROR 	at scala.collection.MapLike.getOrElse$(MapLike.scala:129)
2025.08.06 15:01:48 ERROR 	at scala.collection.AbstractMap.getOrElse(Map.scala:65)
2025.08.06 15:01:48 ERROR 	at sbt.internal.Load$.memoLoadSettingsFile$1(Load.scala:1129)
2025.08.06 15:01:48 ERROR 	at sbt.internal.Load$.$anonfun$discoverProjects$4(Load.scala:1137)
2025.08.06 15:01:48 ERROR 	at scala.collection.immutable.List.map(List.scala:293)
2025.08.06 15:01:48 ERROR 	at sbt.internal.Load$.loadFiles$1(Load.scala:1137)
2025.08.06 15:01:48 ERROR 	at sbt.internal.Load$.discoverProjects(Load.scala:1151)
2025.08.06 15:01:48 ERROR 	at sbt.internal.Load$.discover$1(Load.scala:903)
2025.08.06 15:01:48 ERROR 	at sbt.internal.Load$.loadTransitive(Load.scala:957)
2025.08.06 15:01:48 ERROR 	at sbt.internal.Load$.loadProjects$1(Load.scala:740)
2025.08.06 15:01:48 ERROR 	at sbt.internal.Load$.$anonfun$loadUnit$12(Load.scala:743)
2025.08.06 15:01:48 ERROR 	at sbt.internal.Load$.timed(Load.scala:1408)
2025.08.06 15:01:48 ERROR 	at sbt.internal.Load$.$anonfun$loadUnit$1(Load.scala:743)
2025.08.06 15:01:48 ERROR 	at sbt.internal.Load$.timed(Load.scala:1408)
2025.08.06 15:01:48 ERROR 	at sbt.internal.Load$.loadUnit(Load.scala:696)
2025.08.06 15:01:48 ERROR 	at sbt.internal.Load$.$anonfun$builtinLoader$4(Load.scala:494)
2025.08.06 15:01:48 ERROR 	at sbt.internal.BuildLoader$.$anonfun$componentLoader$5(BuildLoader.scala:180)
2025.08.06 15:01:48 ERROR 	at sbt.internal.BuildLoader.apply(BuildLoader.scala:245)
2025.08.06 15:01:48 ERROR 	at sbt.internal.Load$.loadURI$1(Load.scala:556)
2025.08.06 15:01:48 ERROR 	at sbt.internal.Load$.loadAll(Load.scala:572)
2025.08.06 15:01:48 ERROR 	at sbt.internal.Load$.loadURI(Load.scala:502)
2025.08.06 15:01:48 ERROR 	at sbt.internal.Load$.load(Load.scala:481)
2025.08.06 15:01:48 ERROR 	at sbt.internal.Load$.$anonfun$apply$1(Load.scala:243)
2025.08.06 15:01:48 ERROR 	at sbt.internal.Load$.timed(Load.scala:1408)
2025.08.06 15:01:48 ERROR 	at sbt.internal.Load$.apply(Load.scala:243)
2025.08.06 15:01:48 ERROR 	at sbt.internal.Load$.buildPluginDefinition(Load.scala:1325)
2025.08.06 15:01:48 ERROR 	at sbt.internal.Load$.buildPlugins(Load.scala:1255)
2025.08.06 15:01:48 ERROR 	at sbt.internal.Load$.plugins(Load.scala:1234)
2025.08.06 15:01:48 ERROR 	at sbt.internal.Load$.$anonfun$loadUnit$2(Load.scala:702)
2025.08.06 15:01:48 ERROR 	at sbt.internal.Load$.timed(Load.scala:1408)
2025.08.06 15:01:48 ERROR 	at sbt.internal.Load$.$anonfun$loadUnit$1(Load.scala:702)
2025.08.06 15:01:48 ERROR 	at sbt.internal.Load$.timed(Load.scala:1408)
2025.08.06 15:01:48 ERROR 	at sbt.internal.Load$.loadUnit(Load.scala:696)
2025.08.06 15:01:48 ERROR 	at sbt.internal.Load$.$anonfun$builtinLoader$4(Load.scala:494)
2025.08.06 15:01:48 ERROR 	at sbt.internal.BuildLoader$.$anonfun$componentLoader$5(BuildLoader.scala:180)
2025.08.06 15:01:48 ERROR 	at sbt.internal.BuildLoader.apply(BuildLoader.scala:245)
2025.08.06 15:01:48 ERROR 	at sbt.internal.Load$.loadURI$1(Load.scala:556)
2025.08.06 15:01:48 ERROR 	at sbt.internal.Load$.loadAll(Load.scala:572)
2025.08.06 15:01:48 ERROR 	at sbt.internal.Load$.loadURI(Load.scala:502)
2025.08.06 15:01:48 ERROR 	at sbt.internal.Load$.load(Load.scala:481)
2025.08.06 15:01:48 ERROR 	at sbt.internal.Load$.$anonfun$apply$1(Load.scala:243)
2025.08.06 15:01:48 ERROR 	at sbt.internal.Load$.timed(Load.scala:1408)
2025.08.06 15:01:48 ERROR 	at sbt.internal.Load$.apply(Load.scala:243)
2025.08.06 15:01:48 ERROR 	at sbt.internal.Load$.buildPluginDefinition(Load.scala:1325)
2025.08.06 15:01:48 ERROR 	at sbt.internal.Load$.buildPlugins(Load.scala:1255)
2025.08.06 15:01:48 ERROR 	at sbt.internal.Load$.plugins(Load.scala:1234)
2025.08.06 15:01:48 ERROR 	at sbt.internal.Load$.$anonfun$loadUnit$2(Load.scala:702)
2025.08.06 15:01:48 ERROR 	at sbt.internal.Load$.timed(Load.scala:1408)
2025.08.06 15:01:48 ERROR 	at sbt.internal.Load$.$anonfun$loadUnit$1(Load.scala:702)
2025.08.06 15:01:48 ERROR 	at sbt.internal.Load$.timed(Load.scala:1408)
2025.08.06 15:01:48 ERROR 	at sbt.internal.Load$.loadUnit(Load.scala:696)
2025.08.06 15:01:48 ERROR 	at sbt.internal.Load$.$anonfun$builtinLoader$4(Load.scala:494)
2025.08.06 15:01:48 ERROR 	at sbt.internal.BuildLoader$.$anonfun$componentLoader$5(BuildLoader.scala:180)
2025.08.06 15:01:48 ERROR 	at sbt.internal.BuildLoader.apply(BuildLoader.scala:245)
2025.08.06 15:01:48 ERROR 	at sbt.internal.Load$.loadURI$1(Load.scala:556)
2025.08.06 15:01:48 ERROR 	at sbt.internal.Load$.loadAll(Load.scala:572)
2025.08.06 15:01:48 ERROR 	at sbt.internal.Load$.loadURI(Load.scala:502)
2025.08.06 15:01:48 ERROR 	at sbt.internal.Load$.load(Load.scala:481)
2025.08.06 15:01:48 ERROR 	at sbt.internal.Load$.$anonfun$apply$1(Load.scala:243)
2025.08.06 15:01:48 ERROR 	at sbt.internal.Load$.timed(Load.scala:1408)
2025.08.06 15:01:48 ERROR 	at sbt.internal.Load$.apply(Load.scala:243)
2025.08.06 15:01:48 ERROR 	at sbt.internal.Load$.buildPluginDefinition(Load.scala:1325)
2025.08.06 15:01:48 ERROR 	at sbt.internal.Load$.buildPlugins(Load.scala:1255)
2025.08.06 15:01:48 ERROR 	at sbt.internal.Load$.plugins(Load.scala:1234)
2025.08.06 15:01:48 ERROR 	at sbt.internal.Load$.$anonfun$loadUnit$2(Load.scala:702)
2025.08.06 15:01:48 ERROR 	at sbt.internal.Load$.timed(Load.scala:1408)
2025.08.06 15:01:48 ERROR 	at sbt.internal.Load$.$anonfun$loadUnit$1(Load.scala:702)
2025.08.06 15:01:48 ERROR 	at sbt.internal.Load$.timed(Load.scala:1408)
2025.08.06 15:01:48 ERROR 	at sbt.internal.Load$.loadUnit(Load.scala:696)
2025.08.06 15:01:48 ERROR 	at sbt.internal.Load$.$anonfun$builtinLoader$4(Load.scala:494)
2025.08.06 15:01:48 ERROR 	at sbt.internal.BuildLoader$.$anonfun$componentLoader$5(BuildLoader.scala:180)
2025.08.06 15:01:48 ERROR 	at sbt.internal.BuildLoader.apply(BuildLoader.scala:245)
2025.08.06 15:01:48 ERROR 	at sbt.internal.Load$.loadURI$1(Load.scala:556)
2025.08.06 15:01:48 ERROR 	at sbt.internal.Load$.loadAll(Load.scala:572)
2025.08.06 15:01:48 ERROR 	at sbt.internal.Load$.loadURI(Load.scala:502)
2025.08.06 15:01:48 ERROR 	at sbt.internal.Load$.load(Load.scala:481)
2025.08.06 15:01:48 ERROR 	at sbt.internal.Load$.$anonfun$apply$1(Load.scala:243)
2025.08.06 15:01:48 ERROR 	at sbt.internal.Load$.timed(Load.scala:1408)
2025.08.06 15:01:48 ERROR 	at sbt.internal.Load$.apply(Load.scala:243)
2025.08.06 15:01:48 ERROR 	at sbt.internal.Load$.defaultLoad(Load.scala:56)
2025.08.06 15:01:48 ERROR 	at sbt.BuiltinCommands$.liftedTree1$1(Main.scala:953)
2025.08.06 15:01:48 ERROR 	at sbt.BuiltinCommands$.doLoadProject(Main.scala:953)
2025.08.06 15:01:48 ERROR 	at sbt.BuiltinCommands$.$anonfun$loadProjectImpl$2(Main.scala:906)
2025.08.06 15:01:48 ERROR 	at sbt.Command$.$anonfun$applyEffect$4(Command.scala:150)
2025.08.06 15:01:48 ERROR 	at sbt.Command$.$anonfun$applyEffect$2(Command.scala:145)
2025.08.06 15:01:48 ERROR 	at sbt.Command$.process(Command.scala:189)
2025.08.06 15:01:48 ERROR 	at sbt.MainLoop$.$anonfun$processCommand$5(MainLoop.scala:245)
2025.08.06 15:01:48 ERROR 	at scala.Option.getOrElse(Option.scala:189)
2025.08.06 15:01:48 ERROR 	at sbt.MainLoop$.process$1(MainLoop.scala:245)
2025.08.06 15:01:48 ERROR 	at sbt.MainLoop$.processCommand(MainLoop.scala:278)
2025.08.06 15:01:48 ERROR 	at sbt.MainLoop$.$anonfun$next$5(MainLoop.scala:163)
2025.08.06 15:01:48 ERROR 	at sbt.State$StateOpsImpl$.runCmd$1(State.scala:289)
2025.08.06 15:01:48 ERROR 	at sbt.State$StateOpsImpl$.process$extension(State.scala:325)
2025.08.06 15:01:48 ERROR 	at sbt.MainLoop$.$anonfun$next$4(MainLoop.scala:163)
2025.08.06 15:01:48 ERROR 	at sbt.internal.util.ErrorHandling$.wideConvert(ErrorHandling.scala:23)
2025.08.06 15:01:48 ERROR 	at sbt.MainLoop$.next(MainLoop.scala:163)
2025.08.06 15:01:48 ERROR 	at sbt.MainLoop$.run(MainLoop.scala:144)
2025.08.06 15:01:48 ERROR 	at sbt.MainLoop$.$anonfun$runWithNewLog$1(MainLoop.scala:119)
2025.08.06 15:01:48 ERROR 	at sbt.io.Using.apply(Using.scala:27)
2025.08.06 15:01:48 ERROR 	at sbt.MainLoop$.runWithNewLog(MainLoop.scala:112)
2025.08.06 15:01:48 ERROR 	at sbt.MainLoop$.runAndClearLast(MainLoop.scala:66)
2025.08.06 15:01:48 ERROR 	at sbt.MainLoop$.runLoggedLoop(MainLoop.scala:51)
2025.08.06 15:01:48 ERROR 	at sbt.MainLoop$.runLogged(MainLoop.scala:42)
2025.08.06 15:01:48 ERROR 	at sbt.StandardMain$.runManaged(Main.scala:215)
2025.08.06 15:01:48 ERROR 	at sbt.xMain$.$anonfun$run$11(Main.scala:133)
2025.08.06 15:01:48 ERROR 	at scala.util.DynamicVariable.withValue(DynamicVariable.scala:62)
2025.08.06 15:01:48 ERROR 	at scala.Console$.withIn(Console.scala:230)
2025.08.06 15:01:48 ERROR 	at sbt.internal.util.Terminal$.withIn(Terminal.scala:569)
2025.08.06 15:01:48 ERROR 	at sbt.internal.util.Terminal$.$anonfun$withStreams$1(Terminal.scala:350)
2025.08.06 15:01:48 ERROR 	at scala.util.DynamicVariable.withValue(DynamicVariable.scala:62)
2025.08.06 15:01:48 ERROR 	at scala.Console$.withOut(Console.scala:167)
2025.08.06 15:01:48 ERROR 	at sbt.internal.util.Terminal$.$anonfun$withOut$2(Terminal.scala:559)
2025.08.06 15:01:48 ERROR 	at scala.util.DynamicVariable.withValue(DynamicVariable.scala:62)
2025.08.06 15:01:48 ERROR 	at scala.Console$.withErr(Console.scala:196)
2025.08.06 15:01:48 ERROR 	at sbt.internal.util.Terminal$.withOut(Terminal.scala:559)
2025.08.06 15:01:48 ERROR 	at sbt.internal.util.Terminal$.withStreams(Terminal.scala:350)
2025.08.06 15:01:48 ERROR 	at sbt.xMain$.withStreams$1(Main.scala:87)
2025.08.06 15:01:48 ERROR 	at sbt.xMain$.run(Main.scala:121)
2025.08.06 15:01:48 ERROR 	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
2025.08.06 15:01:48 ERROR 	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
2025.08.06 15:01:48 ERROR 	at sbt.internal.XMainConfiguration.run(XMainConfiguration.java:57)
2025.08.06 15:01:48 ERROR 	at sbt.xMain.run(Main.scala:46)
2025.08.06 15:01:48 ERROR 	at xsbt.boot.Launch$.$anonfun$run$1(Launch.scala:149)
2025.08.06 15:01:48 ERROR 	at xsbt.boot.Launch$.withContextLoader(Launch.scala:176)
2025.08.06 15:01:48 ERROR 	at xsbt.boot.Launch$.run(Launch.scala:149)
2025.08.06 15:01:48 ERROR 	at xsbt.boot.Launch$.$anonfun$apply$1(Launch.scala:44)
2025.08.06 15:01:48 ERROR 	at xsbt.boot.Launch$.launch(Launch.scala:159)
2025.08.06 15:01:48 ERROR 	at xsbt.boot.Launch$.apply(Launch.scala:44)
2025.08.06 15:01:48 ERROR 	at xsbt.boot.Launch$.apply(Launch.scala:21)
2025.08.06 15:01:48 INFO  [error] java.lang.NoClassDefFoundError: Could not initialize class sbt.internal.parser.SbtParser$
2025.08.06 15:01:48 ERROR 	at xsbt.boot.Boot$.runImpl(Boot.scala:78)
2025.08.06 15:01:48 INFO  [error] Use 'last' for the full log.
2025.08.06 15:01:48 ERROR 	at xsbt.boot.Boot$.run(Boot.scala:73)
2025.08.06 15:01:48 ERROR 	at xsbt.boot.Boot$.main(Boot.scala:21)
2025.08.06 15:01:48 ERROR 	at xsbt.boot.Boot.main(Boot.scala)
2025.08.06 15:01:48 ERROR Caused by: java.lang.ExceptionInInitializerError: Exception scala.reflect.internal.FatalError: 
2025.08.06 15:01:48 ERROR   bad constant pool index: 0 at pos: 48454
2025.08.06 15:01:48 ERROR      while compiling: <no file>
2025.08.06 15:01:48 ERROR         during phase: globalPhase=<no phase>, enteringPhase=<some phase>
2025.08.06 15:01:48 ERROR      library version: version 2.12.15
2025.08.06 15:01:48 ERROR     compiler version: version 2.12.15
2025.08.06 15:01:48 ERROR   reconstructed args: -classpath /home/<USER>/.sbt/boot/scala-2.12.15/lib/scala-library.jar -Yrangepos
2025.08.06 15:01:48 ERROR 
2025.08.06 15:01:48 ERROR   last tree to typer: EmptyTree
2025.08.06 15:01:48 ERROR        tree position: <unknown>
2025.08.06 15:01:48 ERROR             tree tpe: <notype>
2025.08.06 15:01:48 ERROR               symbol: null
2025.08.06 15:01:48 ERROR            call site: <none> in <none>
2025.08.06 15:01:48 ERROR 
2025.08.06 15:01:48 ERROR == Source file context for tree position ==
2025.08.06 15:01:48 ERROR 
2025.08.06 15:01:48 ERROR  [in thread "sbt-parser-init-thread"]
2025.08.06 15:01:48 ERROR 	at scala.reflect.internal.Reporting.abort(Reporting.scala:69)
2025.08.06 15:01:48 ERROR 	at scala.reflect.internal.Reporting.abort$(Reporting.scala:65)
2025.08.06 15:01:48 ERROR 	at scala.reflect.internal.SymbolTable.abort(SymbolTable.scala:28)
2025.08.06 15:01:48 ERROR 	at scala.tools.nsc.symtab.classfile.ClassfileParser$ConstantPool.errorBadIndex(ClassfileParser.scala:386)
2025.08.06 15:01:48 ERROR 	at scala.tools.nsc.symtab.classfile.ClassfileParser$ConstantPool.getExternalName(ClassfileParser.scala:250)
2025.08.06 15:01:48 ERROR 	at scala.tools.nsc.symtab.classfile.ClassfileParser.readParamNames$1(ClassfileParser.scala:841)
2025.08.06 15:01:48 ERROR 	at scala.tools.nsc.symtab.classfile.ClassfileParser.parseAttribute$1(ClassfileParser.scala:847)
2025.08.06 15:01:48 INFO  [warn] Project loading failed: (r)etry, (q)uit, (l)ast, or (i)gnore? (default: r)
2025.08.06 15:01:48 ERROR 	at scala.tools.nsc.symtab.classfile.ClassfileParser.$anonfun$parseAttributes$7(ClassfileParser.scala:921)
2025.08.06 15:01:48 ERROR 	at scala.tools.nsc.symtab.classfile.ClassfileParser.parseAttributes(ClassfileParser.scala:921)
2025.08.06 15:01:48 ERROR 	at scala.tools.nsc.symtab.classfile.ClassfileParser.parseMethod(ClassfileParser.scala:623)
2025.08.06 15:01:48 ERROR 	at scala.tools.nsc.symtab.classfile.ClassfileParser.$anonfun$parseClass$4(ClassfileParser.scala:536)
2025.08.06 15:01:48 ERROR 	at scala.tools.nsc.symtab.classfile.ClassfileParser.parseClass(ClassfileParser.scala:536)
2025.08.06 15:01:48 ERROR 	at scala.tools.nsc.symtab.classfile.ClassfileParser.$anonfun$parse$2(ClassfileParser.scala:161)
2025.08.06 15:01:48 ERROR 	at scala.tools.nsc.symtab.classfile.ClassfileParser.$anonfun$parse$1(ClassfileParser.scala:147)
2025.08.06 15:01:48 ERROR 	at scala.tools.nsc.symtab.classfile.ClassfileParser.parse(ClassfileParser.scala:130)
2025.08.06 15:01:48 ERROR 	at scala.tools.nsc.symtab.SymbolLoaders$ClassfileLoader.doComplete(SymbolLoaders.scala:343)
2025.08.06 15:01:48 ERROR 	at scala.tools.nsc.symtab.SymbolLoaders$SymbolLoader.complete(SymbolLoaders.scala:250)
2025.08.06 15:01:48 ERROR 	at scala.reflect.internal.Symbols$Symbol.completeInfo(Symbols.scala:1542)
2025.08.06 15:01:48 ERROR 	at scala.reflect.internal.Symbols$Symbol.info(Symbols.scala:1514)
2025.08.06 15:01:48 ERROR 	at scala.reflect.internal.Definitions.scala$reflect$internal$Definitions$$enterNewMethod(Definitions.scala:49)
2025.08.06 15:01:48 ERROR 	at scala.reflect.internal.Definitions$DefinitionsClass.String_$plus$lzycompute(Definitions.scala:1134)
2025.08.06 15:01:48 ERROR 	at scala.reflect.internal.Definitions$DefinitionsClass.String_$plus(Definitions.scala:1134)
2025.08.06 15:01:48 ERROR 	at scala.reflect.internal.Definitions$DefinitionsClass.syntheticCoreMethods$lzycompute(Definitions.scala:1438)
2025.08.06 15:01:48 ERROR 	at scala.reflect.internal.Definitions$DefinitionsClass.syntheticCoreMethods(Definitions.scala:1420)
2025.08.06 15:01:48 ERROR 	at scala.reflect.internal.Definitions$DefinitionsClass.symbolsNotPresentInBytecode$lzycompute(Definitions.scala:1450)
2025.08.06 15:01:48 ERROR 	at scala.reflect.internal.Definitions$DefinitionsClass.symbolsNotPresentInBytecode(Definitions.scala:1450)
2025.08.06 15:01:48 ERROR 	at scala.reflect.internal.Definitions$DefinitionsClass.init(Definitions.scala:1506)
2025.08.06 15:01:48 ERROR 	at scala.tools.nsc.Global$Run.<init>(Global.scala:1213)
2025.08.06 15:01:48 ERROR 	at sbt.internal.parser.SbtParser$.<init>(SbtParser.scala:141)
2025.08.06 15:01:48 ERROR 	at sbt.internal.parser.SbtParser$.<clinit>(SbtParser.scala)
2025.08.06 15:01:48 ERROR 	at sbt.internal.parser.SbtParserInit$$anon$2.run(SbtParser.scala:191)
2025.08.06 15:01:48 INFO  time: ran 'sbt bloopInstall' in 2.85s
2025.08.06 15:01:48 ERROR sbt command failed: /usr/bin/sbt -Dbloop.export-jar-classifiers=sources bloopInstall
2025.08.06 15:01:53 INFO  running '/usr/bin/sbt -Dbloop.export-jar-classifiers=sources bloopInstall'
2025.08.06 15:01:53 INFO  [info] welcome to sbt 1.6.1 (Eclipse Adoptium Java 21.0.7)
2025.08.06 15:01:54 ERROR error:
2025.08.06 15:01:54 ERROR   bad constant pool index: 0 at pos: 48454
2025.08.06 15:01:54 ERROR      while compiling: <no file>
2025.08.06 15:01:54 ERROR         during phase: globalPhase=<no phase>, enteringPhase=<some phase>
2025.08.06 15:01:54 ERROR      library version: version 2.12.15
2025.08.06 15:01:54 ERROR     compiler version: version 2.12.15
2025.08.06 15:01:54 ERROR   reconstructed args: -classpath /home/<USER>/.sbt/boot/scala-2.12.15/lib/scala-library.jar -Yrangepos
2025.08.06 15:01:54 ERROR 
2025.08.06 15:01:54 ERROR   last tree to typer: EmptyTree
2025.08.06 15:01:54 ERROR        tree position: <unknown>
2025.08.06 15:01:54 ERROR             tree tpe: <notype>
2025.08.06 15:01:54 ERROR               symbol: null
2025.08.06 15:01:54 ERROR            call site: <none> in <none>
2025.08.06 15:01:54 ERROR 
2025.08.06 15:01:54 ERROR == Source file context for tree position ==
2025.08.06 15:01:54 ERROR 
2025.08.06 15:01:54 ERROR error:
2025.08.06 15:01:54 ERROR   bad constant pool index: 0 at pos: 48454
2025.08.06 15:01:54 ERROR      while compiling: <no file>
2025.08.06 15:01:54 ERROR         during phase: globalPhase=<no phase>, enteringPhase=<some phase>
2025.08.06 15:01:54 ERROR      library version: version 2.12.15
2025.08.06 15:01:54 ERROR     compiler version: version 2.12.15
2025.08.06 15:01:54 ERROR   reconstructed args: -classpath /home/<USER>/.sbt/boot/scala-2.12.15/lib/scala-library.jar -Yrangepos
2025.08.06 15:01:54 ERROR 
2025.08.06 15:01:54 ERROR   last tree to typer: EmptyTree
2025.08.06 15:01:54 ERROR        tree position: <unknown>
2025.08.06 15:01:54 ERROR             tree tpe: <notype>
2025.08.06 15:01:54 ERROR               symbol: null
2025.08.06 15:01:54 ERROR            call site: <none> in <none>
2025.08.06 15:01:54 ERROR 
2025.08.06 15:01:54 ERROR == Source file context for tree position ==
2025.08.06 15:01:54 ERROR 
2025.08.06 15:01:54 ERROR Exception in thread "sbt-parser-init-thread" java.lang.ExceptionInInitializerError
2025.08.06 15:01:54 ERROR 	at sbt.internal.parser.SbtParserInit$$anon$2.run(SbtParser.scala:191)
2025.08.06 15:01:54 ERROR Caused by: scala.reflect.internal.FatalError: 
2025.08.06 15:01:54 ERROR   bad constant pool index: 0 at pos: 48454
2025.08.06 15:01:54 ERROR      while compiling: <no file>
2025.08.06 15:01:54 ERROR         during phase: globalPhase=<no phase>, enteringPhase=<some phase>
2025.08.06 15:01:54 ERROR      library version: version 2.12.15
2025.08.06 15:01:54 ERROR     compiler version: version 2.12.15
2025.08.06 15:01:54 ERROR   reconstructed args: -classpath /home/<USER>/.sbt/boot/scala-2.12.15/lib/scala-library.jar -Yrangepos
2025.08.06 15:01:54 ERROR 
2025.08.06 15:01:54 ERROR   last tree to typer: EmptyTree
2025.08.06 15:01:54 ERROR        tree position: <unknown>
2025.08.06 15:01:54 ERROR             tree tpe: <notype>
2025.08.06 15:01:54 ERROR               symbol: null
2025.08.06 15:01:54 ERROR            call site: <none> in <none>
2025.08.06 15:01:54 ERROR 
2025.08.06 15:01:54 ERROR == Source file context for tree position ==
2025.08.06 15:01:54 ERROR 
2025.08.06 15:01:54 ERROR 
2025.08.06 15:01:54 ERROR 	at scala.reflect.internal.Reporting.abort(Reporting.scala:69)
2025.08.06 15:01:54 ERROR 	at scala.reflect.internal.Reporting.abort$(Reporting.scala:65)
2025.08.06 15:01:54 ERROR 	at scala.reflect.internal.SymbolTable.abort(SymbolTable.scala:28)
2025.08.06 15:01:54 ERROR 	at scala.tools.nsc.symtab.classfile.ClassfileParser$ConstantPool.errorBadIndex(ClassfileParser.scala:386)
2025.08.06 15:01:54 ERROR 	at scala.tools.nsc.symtab.classfile.ClassfileParser$ConstantPool.getExternalName(ClassfileParser.scala:250)
2025.08.06 15:01:54 ERROR 	at scala.tools.nsc.symtab.classfile.ClassfileParser.readParamNames$1(ClassfileParser.scala:841)
2025.08.06 15:01:54 ERROR 	at scala.tools.nsc.symtab.classfile.ClassfileParser.parseAttribute$1(ClassfileParser.scala:847)
2025.08.06 15:01:54 ERROR 	at scala.tools.nsc.symtab.classfile.ClassfileParser.$anonfun$parseAttributes$7(ClassfileParser.scala:921)
2025.08.06 15:01:54 ERROR 	at scala.tools.nsc.symtab.classfile.ClassfileParser.parseAttributes(ClassfileParser.scala:921)
2025.08.06 15:01:54 ERROR 	at scala.tools.nsc.symtab.classfile.ClassfileParser.parseMethod(ClassfileParser.scala:623)
2025.08.06 15:01:54 ERROR 	at scala.tools.nsc.symtab.classfile.ClassfileParser.$anonfun$parseClass$4(ClassfileParser.scala:536)
2025.08.06 15:01:54 ERROR 	at scala.tools.nsc.symtab.classfile.ClassfileParser.parseClass(ClassfileParser.scala:536)
2025.08.06 15:01:54 ERROR 	at scala.tools.nsc.symtab.classfile.ClassfileParser.$anonfun$parse$2(ClassfileParser.scala:161)
2025.08.06 15:01:54 ERROR 	at scala.tools.nsc.symtab.classfile.ClassfileParser.$anonfun$parse$1(ClassfileParser.scala:147)
2025.08.06 15:01:54 ERROR 	at scala.tools.nsc.symtab.classfile.ClassfileParser.parse(ClassfileParser.scala:130)
2025.08.06 15:01:54 ERROR 	at scala.tools.nsc.symtab.SymbolLoaders$ClassfileLoader.doComplete(SymbolLoaders.scala:343)
2025.08.06 15:01:54 ERROR 	at scala.tools.nsc.symtab.SymbolLoaders$SymbolLoader.complete(SymbolLoaders.scala:250)
2025.08.06 15:01:54 ERROR 	at scala.reflect.internal.Symbols$Symbol.completeInfo(Symbols.scala:1542)
2025.08.06 15:01:54 ERROR 	at scala.reflect.internal.Symbols$Symbol.info(Symbols.scala:1514)
2025.08.06 15:01:54 ERROR 	at scala.reflect.internal.Definitions.scala$reflect$internal$Definitions$$enterNewMethod(Definitions.scala:49)
2025.08.06 15:01:54 ERROR 	at scala.reflect.internal.Definitions$DefinitionsClass.String_$plus$lzycompute(Definitions.scala:1134)
2025.08.06 15:01:54 ERROR 	at scala.reflect.internal.Definitions$DefinitionsClass.String_$plus(Definitions.scala:1134)
2025.08.06 15:01:54 ERROR 	at scala.reflect.internal.Definitions$DefinitionsClass.syntheticCoreMethods$lzycompute(Definitions.scala:1438)
2025.08.06 15:01:54 ERROR 	at scala.reflect.internal.Definitions$DefinitionsClass.syntheticCoreMethods(Definitions.scala:1420)
2025.08.06 15:01:54 ERROR 	at scala.reflect.internal.Definitions$DefinitionsClass.symbolsNotPresentInBytecode$lzycompute(Definitions.scala:1450)
2025.08.06 15:01:54 ERROR 	at scala.reflect.internal.Definitions$DefinitionsClass.symbolsNotPresentInBytecode(Definitions.scala:1450)
2025.08.06 15:01:54 ERROR 	at scala.reflect.internal.Definitions$DefinitionsClass.init(Definitions.scala:1506)
2025.08.06 15:01:54 ERROR 	at scala.tools.nsc.Global$Run.<init>(Global.scala:1213)
2025.08.06 15:01:54 ERROR 	at sbt.internal.parser.SbtParser$.<init>(SbtParser.scala:141)
2025.08.06 15:01:54 ERROR 	at sbt.internal.parser.SbtParser$.<clinit>(SbtParser.scala)
2025.08.06 15:01:54 ERROR 	... 1 more
2025.08.06 15:01:53 INFO  [info] loading global plugins from /home/<USER>/.sbt/1.0/plugins
2025.08.06 15:01:55 ERROR java.lang.NoClassDefFoundError: Could not initialize class sbt.internal.parser.SbtParser$
2025.08.06 15:01:55 ERROR 	at sbt.internal.parser.SbtParser.splitExpressions(SbtParser.scala:247)
2025.08.06 15:01:55 ERROR 	at sbt.internal.parser.SbtParser.<init>(SbtParser.scala:236)
2025.08.06 15:01:55 ERROR 	at sbt.internal.EvaluateConfigurations$.splitExpressions(EvaluateConfigurations.scala:289)
2025.08.06 15:01:55 ERROR 	at sbt.internal.EvaluateConfigurations$.parseConfiguration(EvaluateConfigurations.scala:98)
2025.08.06 15:01:55 ERROR 	at sbt.internal.EvaluateConfigurations$.evaluateSbtFile(EvaluateConfigurations.scala:147)
2025.08.06 15:01:55 ERROR 	at sbt.internal.Load$.loadSettingsFile$1(Load.scala:1120)
2025.08.06 15:01:55 ERROR 	at sbt.internal.Load$.$anonfun$discoverProjects$2(Load.scala:1130)
2025.08.06 15:01:55 ERROR 	at scala.collection.MapLike.getOrElse(MapLike.scala:131)
2025.08.06 15:01:55 ERROR 	at scala.collection.MapLike.getOrElse$(MapLike.scala:129)
2025.08.06 15:01:55 ERROR 	at scala.collection.AbstractMap.getOrElse(Map.scala:65)
2025.08.06 15:01:55 ERROR 	at sbt.internal.Load$.memoLoadSettingsFile$1(Load.scala:1129)
2025.08.06 15:01:55 ERROR 	at sbt.internal.Load$.$anonfun$discoverProjects$4(Load.scala:1137)
2025.08.06 15:01:55 ERROR 	at scala.collection.immutable.List.map(List.scala:293)
2025.08.06 15:01:55 ERROR 	at sbt.internal.Load$.loadFiles$1(Load.scala:1137)
2025.08.06 15:01:55 ERROR 	at sbt.internal.Load$.discoverProjects(Load.scala:1151)
2025.08.06 15:01:55 ERROR 	at sbt.internal.Load$.discover$1(Load.scala:903)
2025.08.06 15:01:55 ERROR 	at sbt.internal.Load$.loadTransitive(Load.scala:957)
2025.08.06 15:01:55 ERROR 	at sbt.internal.Load$.loadProjects$1(Load.scala:740)
2025.08.06 15:01:55 ERROR 	at sbt.internal.Load$.$anonfun$loadUnit$12(Load.scala:743)
2025.08.06 15:01:55 ERROR 	at sbt.internal.Load$.timed(Load.scala:1408)
2025.08.06 15:01:55 ERROR 	at sbt.internal.Load$.$anonfun$loadUnit$1(Load.scala:743)
2025.08.06 15:01:55 ERROR 	at sbt.internal.Load$.timed(Load.scala:1408)
2025.08.06 15:01:55 ERROR 	at sbt.internal.Load$.loadUnit(Load.scala:696)
2025.08.06 15:01:55 ERROR 	at sbt.internal.Load$.$anonfun$builtinLoader$4(Load.scala:494)
2025.08.06 15:01:55 ERROR 	at sbt.internal.BuildLoader$.$anonfun$componentLoader$5(BuildLoader.scala:180)
2025.08.06 15:01:55 ERROR 	at sbt.internal.BuildLoader.apply(BuildLoader.scala:245)
2025.08.06 15:01:55 ERROR 	at sbt.internal.Load$.loadURI$1(Load.scala:556)
2025.08.06 15:01:55 ERROR 	at sbt.internal.Load$.loadAll(Load.scala:572)
2025.08.06 15:01:55 ERROR 	at sbt.internal.Load$.loadURI(Load.scala:502)
2025.08.06 15:01:55 ERROR 	at sbt.internal.Load$.load(Load.scala:481)
2025.08.06 15:01:55 ERROR 	at sbt.internal.Load$.$anonfun$apply$1(Load.scala:243)
2025.08.06 15:01:55 ERROR 	at sbt.internal.Load$.timed(Load.scala:1408)
2025.08.06 15:01:55 ERROR 	at sbt.internal.Load$.apply(Load.scala:243)
2025.08.06 15:01:55 ERROR 	at sbt.internal.Load$.buildPluginDefinition(Load.scala:1325)
2025.08.06 15:01:55 ERROR 	at sbt.internal.Load$.buildPlugins(Load.scala:1255)
2025.08.06 15:01:55 ERROR 	at sbt.internal.Load$.plugins(Load.scala:1234)
2025.08.06 15:01:55 ERROR 	at sbt.internal.Load$.$anonfun$loadUnit$2(Load.scala:702)
2025.08.06 15:01:55 ERROR 	at sbt.internal.Load$.timed(Load.scala:1408)
2025.08.06 15:01:55 ERROR 	at sbt.internal.Load$.$anonfun$loadUnit$1(Load.scala:702)
2025.08.06 15:01:55 ERROR 	at sbt.internal.Load$.timed(Load.scala:1408)
2025.08.06 15:01:55 ERROR 	at sbt.internal.Load$.loadUnit(Load.scala:696)
2025.08.06 15:01:55 ERROR 	at sbt.internal.Load$.$anonfun$builtinLoader$4(Load.scala:494)
2025.08.06 15:01:55 ERROR 	at sbt.internal.BuildLoader$.$anonfun$componentLoader$5(BuildLoader.scala:180)
2025.08.06 15:01:55 ERROR 	at sbt.internal.BuildLoader.apply(BuildLoader.scala:245)
2025.08.06 15:01:55 ERROR 	at sbt.internal.Load$.loadURI$1(Load.scala:556)
2025.08.06 15:01:55 ERROR 	at sbt.internal.Load$.loadAll(Load.scala:572)
2025.08.06 15:01:55 ERROR 	at sbt.internal.Load$.loadURI(Load.scala:502)
2025.08.06 15:01:55 ERROR 	at sbt.internal.Load$.load(Load.scala:481)
2025.08.06 15:01:55 ERROR 	at sbt.internal.Load$.$anonfun$apply$1(Load.scala:243)
2025.08.06 15:01:55 ERROR 	at sbt.internal.Load$.timed(Load.scala:1408)
2025.08.06 15:01:55 ERROR 	at sbt.internal.Load$.apply(Load.scala:243)
2025.08.06 15:01:55 ERROR 	at sbt.internal.Load$.buildPluginDefinition(Load.scala:1325)
2025.08.06 15:01:55 ERROR 	at sbt.internal.Load$.buildPlugins(Load.scala:1255)
2025.08.06 15:01:55 ERROR 	at sbt.internal.Load$.plugins(Load.scala:1234)
2025.08.06 15:01:55 ERROR 	at sbt.internal.Load$.$anonfun$loadUnit$2(Load.scala:702)
2025.08.06 15:01:55 ERROR 	at sbt.internal.Load$.timed(Load.scala:1408)
2025.08.06 15:01:55 ERROR 	at sbt.internal.Load$.$anonfun$loadUnit$1(Load.scala:702)
2025.08.06 15:01:55 ERROR 	at sbt.internal.Load$.timed(Load.scala:1408)
2025.08.06 15:01:55 ERROR 	at sbt.internal.Load$.loadUnit(Load.scala:696)
2025.08.06 15:01:55 ERROR 	at sbt.internal.Load$.$anonfun$builtinLoader$4(Load.scala:494)
2025.08.06 15:01:55 ERROR 	at sbt.internal.BuildLoader$.$anonfun$componentLoader$5(BuildLoader.scala:180)
2025.08.06 15:01:55 ERROR 	at sbt.internal.BuildLoader.apply(BuildLoader.scala:245)
2025.08.06 15:01:55 ERROR 	at sbt.internal.Load$.loadURI$1(Load.scala:556)
2025.08.06 15:01:55 ERROR 	at sbt.internal.Load$.loadAll(Load.scala:572)
2025.08.06 15:01:55 ERROR 	at sbt.internal.Load$.loadURI(Load.scala:502)
2025.08.06 15:01:55 ERROR 	at sbt.internal.Load$.load(Load.scala:481)
2025.08.06 15:01:55 ERROR 	at sbt.internal.Load$.$anonfun$apply$1(Load.scala:243)
2025.08.06 15:01:55 ERROR 	at sbt.internal.Load$.timed(Load.scala:1408)
2025.08.06 15:01:55 ERROR 	at sbt.internal.Load$.apply(Load.scala:243)
2025.08.06 15:01:55 ERROR 	at sbt.internal.Load$.buildPluginDefinition(Load.scala:1325)
2025.08.06 15:01:55 ERROR 	at sbt.internal.Load$.buildPlugins(Load.scala:1255)
2025.08.06 15:01:55 ERROR 	at sbt.internal.Load$.plugins(Load.scala:1234)
2025.08.06 15:01:55 ERROR 	at sbt.internal.Load$.$anonfun$loadUnit$2(Load.scala:702)
2025.08.06 15:01:55 ERROR 	at sbt.internal.Load$.timed(Load.scala:1408)
2025.08.06 15:01:55 ERROR 	at sbt.internal.Load$.$anonfun$loadUnit$1(Load.scala:702)
2025.08.06 15:01:55 ERROR 	at sbt.internal.Load$.timed(Load.scala:1408)
2025.08.06 15:01:55 ERROR 	at sbt.internal.Load$.loadUnit(Load.scala:696)
2025.08.06 15:01:55 ERROR 	at sbt.internal.Load$.$anonfun$builtinLoader$4(Load.scala:494)
2025.08.06 15:01:55 ERROR 	at sbt.internal.BuildLoader$.$anonfun$componentLoader$5(BuildLoader.scala:180)
2025.08.06 15:01:55 ERROR 	at sbt.internal.BuildLoader.apply(BuildLoader.scala:245)
2025.08.06 15:01:55 ERROR 	at sbt.internal.Load$.loadURI$1(Load.scala:556)
2025.08.06 15:01:55 ERROR 	at sbt.internal.Load$.loadAll(Load.scala:572)
2025.08.06 15:01:55 ERROR 	at sbt.internal.Load$.loadURI(Load.scala:502)
2025.08.06 15:01:55 ERROR 	at sbt.internal.Load$.load(Load.scala:481)
2025.08.06 15:01:55 ERROR 	at sbt.internal.Load$.$anonfun$apply$1(Load.scala:243)
2025.08.06 15:01:55 ERROR 	at sbt.internal.Load$.timed(Load.scala:1408)
2025.08.06 15:01:55 ERROR 	at sbt.internal.Load$.apply(Load.scala:243)
2025.08.06 15:01:55 ERROR 	at sbt.internal.Load$.defaultLoad(Load.scala:56)
2025.08.06 15:01:55 ERROR 	at sbt.BuiltinCommands$.liftedTree1$1(Main.scala:953)
2025.08.06 15:01:55 ERROR 	at sbt.BuiltinCommands$.doLoadProject(Main.scala:953)
2025.08.06 15:01:55 ERROR 	at sbt.BuiltinCommands$.$anonfun$loadProjectImpl$2(Main.scala:906)
2025.08.06 15:01:55 ERROR 	at sbt.Command$.$anonfun$applyEffect$4(Command.scala:150)
2025.08.06 15:01:55 ERROR 	at sbt.Command$.$anonfun$applyEffect$2(Command.scala:145)
2025.08.06 15:01:55 ERROR 	at sbt.Command$.process(Command.scala:189)
2025.08.06 15:01:55 ERROR 	at sbt.MainLoop$.$anonfun$processCommand$5(MainLoop.scala:245)
2025.08.06 15:01:55 ERROR 	at scala.Option.getOrElse(Option.scala:189)
2025.08.06 15:01:55 ERROR 	at sbt.MainLoop$.process$1(MainLoop.scala:245)
2025.08.06 15:01:55 ERROR 	at sbt.MainLoop$.processCommand(MainLoop.scala:278)
2025.08.06 15:01:55 ERROR 	at sbt.MainLoop$.$anonfun$next$5(MainLoop.scala:163)
2025.08.06 15:01:55 ERROR 	at sbt.State$StateOpsImpl$.runCmd$1(State.scala:289)
2025.08.06 15:01:55 ERROR 	at sbt.State$StateOpsImpl$.process$extension(State.scala:325)
2025.08.06 15:01:55 ERROR 	at sbt.MainLoop$.$anonfun$next$4(MainLoop.scala:163)
2025.08.06 15:01:55 ERROR 	at sbt.internal.util.ErrorHandling$.wideConvert(ErrorHandling.scala:23)
2025.08.06 15:01:55 ERROR 	at sbt.MainLoop$.next(MainLoop.scala:163)
2025.08.06 15:01:55 ERROR 	at sbt.MainLoop$.run(MainLoop.scala:144)
2025.08.06 15:01:55 ERROR 	at sbt.MainLoop$.$anonfun$runWithNewLog$1(MainLoop.scala:119)
2025.08.06 15:01:55 ERROR 	at sbt.io.Using.apply(Using.scala:27)
2025.08.06 15:01:55 ERROR 	at sbt.MainLoop$.runWithNewLog(MainLoop.scala:112)
2025.08.06 15:01:55 ERROR 	at sbt.MainLoop$.runAndClearLast(MainLoop.scala:66)
2025.08.06 15:01:55 ERROR 	at sbt.MainLoop$.runLoggedLoop(MainLoop.scala:51)
2025.08.06 15:01:55 ERROR 	at sbt.MainLoop$.runLogged(MainLoop.scala:42)
2025.08.06 15:01:55 ERROR 	at sbt.StandardMain$.runManaged(Main.scala:215)
2025.08.06 15:01:55 ERROR 	at sbt.xMain$.$anonfun$run$11(Main.scala:133)
2025.08.06 15:01:55 ERROR 	at scala.util.DynamicVariable.withValue(DynamicVariable.scala:62)
2025.08.06 15:01:55 ERROR 	at scala.Console$.withIn(Console.scala:230)
2025.08.06 15:01:55 ERROR 	at sbt.internal.util.Terminal$.withIn(Terminal.scala:569)
2025.08.06 15:01:55 ERROR 	at sbt.internal.util.Terminal$.$anonfun$withStreams$1(Terminal.scala:350)
2025.08.06 15:01:55 ERROR 	at scala.util.DynamicVariable.withValue(DynamicVariable.scala:62)
2025.08.06 15:01:55 ERROR 	at scala.Console$.withOut(Console.scala:167)
2025.08.06 15:01:55 ERROR 	at sbt.internal.util.Terminal$.$anonfun$withOut$2(Terminal.scala:559)
2025.08.06 15:01:55 ERROR 	at scala.util.DynamicVariable.withValue(DynamicVariable.scala:62)
2025.08.06 15:01:55 ERROR 	at scala.Console$.withErr(Console.scala:196)
2025.08.06 15:01:55 ERROR 	at sbt.internal.util.Terminal$.withOut(Terminal.scala:559)
2025.08.06 15:01:55 ERROR 	at sbt.internal.util.Terminal$.withStreams(Terminal.scala:350)
2025.08.06 15:01:55 ERROR 	at sbt.xMain$.withStreams$1(Main.scala:87)
2025.08.06 15:01:55 ERROR 	at sbt.xMain$.run(Main.scala:121)
2025.08.06 15:01:55 ERROR 	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
2025.08.06 15:01:55 ERROR 	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
2025.08.06 15:01:55 ERROR 	at sbt.internal.XMainConfiguration.run(XMainConfiguration.java:57)
2025.08.06 15:01:55 ERROR 	at sbt.xMain.run(Main.scala:46)
2025.08.06 15:01:55 ERROR 	at xsbt.boot.Launch$.$anonfun$run$1(Launch.scala:149)
2025.08.06 15:01:55 ERROR 	at xsbt.boot.Launch$.withContextLoader(Launch.scala:176)
2025.08.06 15:01:55 ERROR 	at xsbt.boot.Launch$.run(Launch.scala:149)
2025.08.06 15:01:55 ERROR 	at xsbt.boot.Launch$.$anonfun$apply$1(Launch.scala:44)
2025.08.06 15:01:55 ERROR 	at xsbt.boot.Launch$.launch(Launch.scala:159)
2025.08.06 15:01:55 ERROR 	at xsbt.boot.Launch$.apply(Launch.scala:44)
2025.08.06 15:01:55 ERROR 	at xsbt.boot.Launch$.apply(Launch.scala:21)
2025.08.06 15:01:55 ERROR 	at xsbt.boot.Boot$.runImpl(Boot.scala:78)
2025.08.06 15:01:55 ERROR 	at xsbt.boot.Boot$.run(Boot.scala:73)
2025.08.06 15:01:55 ERROR 	at xsbt.boot.Boot$.main(Boot.scala:21)
2025.08.06 15:01:55 ERROR 	at xsbt.boot.Boot.main(Boot.scala)
2025.08.06 15:01:55 ERROR Caused by: java.lang.ExceptionInInitializerError: Exception scala.reflect.internal.FatalError: 
2025.08.06 15:01:55 ERROR   bad constant pool index: 0 at pos: 48454
2025.08.06 15:01:55 ERROR      while compiling: <no file>
2025.08.06 15:01:55 ERROR         during phase: globalPhase=<no phase>, enteringPhase=<some phase>
2025.08.06 15:01:55 ERROR      library version: version 2.12.15
2025.08.06 15:01:55 ERROR     compiler version: version 2.12.15
2025.08.06 15:01:55 ERROR   reconstructed args: -classpath /home/<USER>/.sbt/boot/scala-2.12.15/lib/scala-library.jar -Yrangepos
2025.08.06 15:01:55 ERROR 
2025.08.06 15:01:55 ERROR   last tree to typer: EmptyTree
2025.08.06 15:01:55 ERROR        tree position: <unknown>
2025.08.06 15:01:55 ERROR             tree tpe: <notype>
2025.08.06 15:01:55 ERROR               symbol: null
2025.08.06 15:01:55 ERROR            call site: <none> in <none>
2025.08.06 15:01:55 ERROR 
2025.08.06 15:01:55 ERROR == Source file context for tree position ==
2025.08.06 15:01:55 ERROR 
2025.08.06 15:01:55 ERROR  [in thread "sbt-parser-init-thread"]
2025.08.06 15:01:55 ERROR 	at scala.reflect.internal.Reporting.abort(Reporting.scala:69)
2025.08.06 15:01:55 ERROR 	at scala.reflect.internal.Reporting.abort$(Reporting.scala:65)
2025.08.06 15:01:55 ERROR 	at scala.reflect.internal.SymbolTable.abort(SymbolTable.scala:28)
2025.08.06 15:01:55 ERROR 	at scala.tools.nsc.symtab.classfile.ClassfileParser$ConstantPool.errorBadIndex(ClassfileParser.scala:386)
2025.08.06 15:01:55 ERROR 	at scala.tools.nsc.symtab.classfile.ClassfileParser$ConstantPool.getExternalName(ClassfileParser.scala:250)
2025.08.06 15:01:55 ERROR 	at scala.tools.nsc.symtab.classfile.ClassfileParser.readParamNames$1(ClassfileParser.scala:841)
2025.08.06 15:01:55 ERROR 	at scala.tools.nsc.symtab.classfile.ClassfileParser.parseAttribute$1(ClassfileParser.scala:847)
2025.08.06 15:01:55 ERROR 	at scala.tools.nsc.symtab.classfile.ClassfileParser.$anonfun$parseAttributes$7(ClassfileParser.scala:921)
2025.08.06 15:01:55 ERROR 	at scala.tools.nsc.symtab.classfile.ClassfileParser.parseAttributes(ClassfileParser.scala:921)
2025.08.06 15:01:55 ERROR 	at scala.tools.nsc.symtab.classfile.ClassfileParser.parseMethod(ClassfileParser.scala:623)
2025.08.06 15:01:55 ERROR 	at scala.tools.nsc.symtab.classfile.ClassfileParser.$anonfun$parseClass$4(ClassfileParser.scala:536)
2025.08.06 15:01:55 ERROR 	at scala.tools.nsc.symtab.classfile.ClassfileParser.parseClass(ClassfileParser.scala:536)
2025.08.06 15:01:55 ERROR 	at scala.tools.nsc.symtab.classfile.ClassfileParser.$anonfun$parse$2(ClassfileParser.scala:161)
2025.08.06 15:01:55 ERROR 	at scala.tools.nsc.symtab.classfile.ClassfileParser.$anonfun$parse$1(ClassfileParser.scala:147)
2025.08.06 15:01:55 ERROR 	at scala.tools.nsc.symtab.classfile.ClassfileParser.parse(ClassfileParser.scala:130)
2025.08.06 15:01:55 ERROR 	at scala.tools.nsc.symtab.SymbolLoaders$ClassfileLoader.doComplete(SymbolLoaders.scala:343)
2025.08.06 15:01:55 ERROR 	at scala.tools.nsc.symtab.SymbolLoaders$SymbolLoader.complete(SymbolLoaders.scala:250)
2025.08.06 15:01:55 ERROR 	at scala.reflect.internal.Symbols$Symbol.completeInfo(Symbols.scala:1542)
2025.08.06 15:01:55 ERROR 	at scala.reflect.internal.Symbols$Symbol.info(Symbols.scala:1514)
2025.08.06 15:01:55 ERROR 	at scala.reflect.internal.Definitions.scala$reflect$internal$Definitions$$enterNewMethod(Definitions.scala:49)
2025.08.06 15:01:55 ERROR 	at scala.reflect.internal.Definitions$DefinitionsClass.String_$plus$lzycompute(Definitions.scala:1134)
2025.08.06 15:01:55 ERROR 	at scala.reflect.internal.Definitions$DefinitionsClass.String_$plus(Definitions.scala:1134)
2025.08.06 15:01:55 ERROR 	at scala.reflect.internal.Definitions$DefinitionsClass.syntheticCoreMethods$lzycompute(Definitions.scala:1438)
2025.08.06 15:01:55 ERROR 	at scala.reflect.internal.Definitions$DefinitionsClass.syntheticCoreMethods(Definitions.scala:1420)
2025.08.06 15:01:55 ERROR 	at scala.reflect.internal.Definitions$DefinitionsClass.symbolsNotPresentInBytecode$lzycompute(Definitions.scala:1450)
2025.08.06 15:01:55 ERROR 	at scala.reflect.internal.Definitions$DefinitionsClass.symbolsNotPresentInBytecode(Definitions.scala:1450)
2025.08.06 15:01:55 ERROR 	at scala.reflect.internal.Definitions$DefinitionsClass.init(Definitions.scala:1506)
2025.08.06 15:01:55 ERROR 	at scala.tools.nsc.Global$Run.<init>(Global.scala:1213)
2025.08.06 15:01:55 ERROR 	at sbt.internal.parser.SbtParser$.<init>(SbtParser.scala:141)
2025.08.06 15:01:55 ERROR 	at sbt.internal.parser.SbtParser$.<clinit>(SbtParser.scala)
2025.08.06 15:01:55 ERROR 	at sbt.internal.parser.SbtParserInit$$anon$2.run(SbtParser.scala:191)
2025.08.06 15:01:55 INFO  [error] java.lang.NoClassDefFoundError: Could not initialize class sbt.internal.parser.SbtParser$
2025.08.06 15:01:55 INFO  [error] Use 'last' for the full log.
2025.08.06 15:01:55 INFO  [warn] Project loading failed: (r)etry, (q)uit, (l)ast, or (i)gnore? (default: r)
2025.08.06 15:01:55 INFO  time: ran 'sbt bloopInstall' in 2.51s
2025.08.06 15:01:55 ERROR sbt command failed: /usr/bin/sbt -Dbloop.export-jar-classifiers=sources bloopInstall
2025.08.06 15:01:55 INFO  Attempting to connect to the build server...
2025.08.06 15:01:55 INFO  Found a Bloop server running
2025.08.06 15:01:55 INFO  tracing is disabled for protocol BSP, to enable tracing of incoming and outgoing JSON messages create an empty file at /home/<USER>/projects/platform.core/.metals/bsp.trace.json or /home/<USER>/.cache/metals/bsp.trace.json
2025.08.06 15:01:55 INFO  time: Connected to build server in 0.15s
2025.08.06 15:01:55 INFO  Connected to Build server: Bloop v2.0.12
2025.08.06 15:01:55 INFO  Created report: Optional.empty
2025.08.06 15:01:55 INFO  Empty build targets. Expected at least one build target identifier. (full report at: "/home/<USER>/projects/platform.core/.metals/.reports/bloop/2025-08-06/r_Empty build targets...._15-01-55-749.md")
2025.08.06 15:01:55 INFO  Empty build targets. Expected at least one build target identifier. (full report at: "/home/<USER>/projects/platform.core/.metals/.reports/bloop/2025-08-06/r_Empty build targets...._15-01-55-749.md")
2025.08.06 15:01:55 INFO  Empty build targets. Expected at least one build target identifier. (full report at: "/home/<USER>/projects/platform.core/.metals/.reports/bloop/2025-08-06/r_Empty build targets...._15-01-55-749.md")
2025.08.06 15:01:55 INFO  Empty build targets. Expected at least one build target identifier. (full report at: "/home/<USER>/projects/platform.core/.metals/.reports/bloop/2025-08-06/r_Empty build targets...._15-01-55-749.md")
2025.08.06 15:01:55 INFO  Empty build targets. Expected at least one build target identifier. (full report at: "/home/<USER>/projects/platform.core/.metals/.reports/bloop/2025-08-06/r_Empty build targets...._15-01-55-749.md")
2025.08.06 15:01:56 INFO  time: indexed workspace in 0.96s
2025.08.06 15:01:56 INFO  Shutting down server
2025.08.06 15:01:56 INFO  shutting down Metals
2025.08.06 15:01:56 INFO  Shut down connection with build server.
2025.08.06 15:01:56 INFO  [0m[31m[E][0m Caught java.lang.NullPointerException: Cannot invoke "scala.collection.immutable.List.filterNot(scala.Function1)" because "sessions" is null
2025.08.06 15:01:56 ERROR Bloop 'bsp' command exited with code 1. Something may be wrong with the current configuration.
Running the [1mclean[0m sub-command to clear the working directory and remove caches might help.
If the error persists, please report the issue as a bug and attach a log with increased verbosity by passing [1m-v -v -v[0m.
Aug 06, 2025 3:01:56 PM io.undertow.Undertow stop
INFO: stopping server: Undertow - 2.3.12.Final
2025.08.06 15:01:56 INFO  Exiting server
2025.08.06 15:02:17 INFO  Started: Metals version 1.6.1 in folders '/home/<USER>/projects/platform.core' for client Visual Studio Code - Insiders 1.103.0-insider.
2025.08.06 15:02:21 INFO  no build target found for /home/<USER>/projects/platform.core/src/main/scala/nl/dpes/core/projections/profileservice/SavedSearchEventHandler.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.08.06 15:02:21 INFO  tracing is disabled for protocol mcp, to enable tracing of incoming and outgoing JSON messages create an empty file at /home/<USER>/projects/platform.core/.metals/mcp.trace.json or /home/<USER>/.cache/metals/mcp.trace.json
2025.08.06 15:02:21 INFO  Attempting to connect to the build server...
2025.08.06 15:02:21 INFO  Found a Bloop server running
Aug 06, 2025 3:02:22 PM io.undertow.Undertow start
INFO: starting server: Undertow - 2.3.12.Final
Aug 06, 2025 3:02:22 PM org.xnio.Xnio <clinit>
INFO: XNIO version 3.8.16.Final
2025.08.06 15:02:22 INFO  tracing is disabled for protocol BSP, to enable tracing of incoming and outgoing JSON messages create an empty file at /home/<USER>/projects/platform.core/.metals/bsp.trace.json or /home/<USER>/.cache/metals/bsp.trace.json
Aug 06, 2025 3:02:22 PM org.xnio.nio.NioXnio <clinit>
INFO: XNIO NIO Implementation Version 3.8.16.Final
2025.08.06 15:02:22 INFO  time: Connected to build server in 0.22s
2025.08.06 15:02:22 INFO  Connected to Build server: Bloop v2.0.12
Aug 06, 2025 3:02:22 PM org.jboss.threads.Version <clinit>
INFO: JBoss Threads version 3.5.0.Final
2025.08.06 15:02:21 INFO  Empty build targets. Expected at least one build target identifier. (full report at: "/home/<USER>/projects/platform.core/.metals/.reports/bloop/2025-08-06/r_Empty build targets...._15-01-55-749.md")
2025.08.06 15:02:21 INFO  Empty build targets. Expected at least one build target identifier. (full report at: "/home/<USER>/projects/platform.core/.metals/.reports/bloop/2025-08-06/r_Empty build targets...._15-01-55-749.md")
2025.08.06 15:02:21 INFO  Metals MCP server started on port: 37177.
2025.08.06 15:02:21 INFO  Empty build targets. Expected at least one build target identifier. (full report at: "/home/<USER>/projects/platform.core/.metals/.reports/bloop/2025-08-06/r_Empty build targets...._15-01-55-749.md")
2025.08.06 15:02:21 INFO  Empty build targets. Expected at least one build target identifier. (full report at: "/home/<USER>/projects/platform.core/.metals/.reports/bloop/2025-08-06/r_Empty build targets...._15-01-55-749.md")
2025.08.06 15:02:21 INFO  Empty build targets. Expected at least one build target identifier. (full report at: "/home/<USER>/projects/platform.core/.metals/.reports/bloop/2025-08-06/r_Empty build targets...._15-01-55-749.md")
Align: top-level presets deprecated; use 'preset' subsection
IndentOperator: top-level presets deprecated; use 'preset' subsection
DanglingParentheses: top-level presets deprecated; use 'preset' subsection
2025.08.06 15:02:22 INFO  no build target found for /home/<USER>/projects/platform.core/src/main/scala/nl/dpes/core/projections/profileservice/SavedSearchEventHandler.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.08.06 15:02:23 INFO  no build target found for /home/<USER>/projects/platform.core/src/main/scala/nl/dpes/core/projections/profileservice/SavedSearchEventHandler.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.08.06 15:02:23 INFO  time: indexed workspace in 1.43s
2025.08.06 15:02:23 WARN  no build target for: /home/<USER>/projects/platform.core/src/main/scala/nl/dpes/core/projections/profileservice/SavedSearchEventHandler.scala
2025.08.06 15:02:23 INFO  no build target found for /home/<USER>/projects/platform.core/src/main/scala/nl/dpes/core/projections/profileservice/SavedSearchEventHandler.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.08.06 15:02:23 INFO  no build target found for /home/<USER>/projects/platform.core/src/main/scala/nl/dpes/core/projections/profileservice/SavedSearchEventHandler.scala. Using presentation compiler with project's scala-library version: 3.3.6
Aug 06, 2025 3:02:24 PM org.eclipse.lsp4j.jsonrpc.RemoteEndpoint handleCancellation
WARNING: Unmatched cancel notification for request id 9
Aug 06, 2025 3:02:24 PM org.eclipse.lsp4j.jsonrpc.RemoteEndpoint handleCancellation
WARNING: Unmatched cancel notification for request id 10
2025.08.06 15:02:24 WARN  no build target for: /home/<USER>/projects/platform.core/src/main/scala/nl/dpes/core/projections/profileservice/SavedSearchEventHandler.scala
2025.08.06 15:02:23 INFO  no build target found for /home/<USER>/projects/platform.core/src/main/scala/nl/dpes/core/projections/profileservice/SavedSearchEventHandler.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.08.06 15:02:26 INFO  no build target found for /home/<USER>/projects/platform.core/src/main/scala/nl/dpes/core/projections/profileservice/SavedSearchEventHandler.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.08.06 15:02:58 INFO  no build target found for /home/<USER>/projects/platform.core/src/main/scala/nl/dpes/core/projections/profileservice/SavedSearchEventHandler.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.08.06 15:02:58 INFO  no build target found for /home/<USER>/projects/platform.core/src/main/scala/nl/dpes/core/projections/profileservice/SavedSearchEventHandler.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.08.06 15:03:32 INFO  no build target found for /home/<USER>/projects/platform.core/src/main/scala/nl/dpes/core/projections/profileservice/SavedSearchEventHandler.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.08.06 15:03:32 INFO  no build target found for /home/<USER>/projects/platform.core/src/main/scala/nl/dpes/core/projections/profileservice/SavedSearchEventHandler.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.08.06 15:03:33 INFO  no build target found for /home/<USER>/projects/platform.core/src/main/scala/nl/dpes/core/projections/profileservice/SavedSearchEventHandler.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.08.06 15:03:33 INFO  no build target found for /home/<USER>/projects/platform.core/src/main/scala/nl/dpes/core/projections/profileservice/SavedSearchEventHandler.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.08.06 15:03:33 INFO  no build target found for /home/<USER>/projects/platform.core/src/main/scala/nl/dpes/core/projections/profileservice/SavedSearchEventHandler.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.08.06 15:03:33 INFO  no build target found for /home/<USER>/projects/platform.core/src/main/scala/nl/dpes/core/projections/profileservice/SavedSearchEventHandler.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.08.06 15:03:35 INFO  no build target found for /home/<USER>/projects/platform.core/src/main/scala/nl/dpes/core/projections/profileservice/SavedSearchEventHandler.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.08.06 15:04:04 INFO  no build target found for /home/<USER>/projects/platform.core/src/main/scala/nl/dpes/core/projections/profileservice/SavedSearchEventHandler.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.08.06 15:04:04 INFO  no build target found for /home/<USER>/projects/platform.core/src/main/scala/nl/dpes/core/projections/profileservice/SavedSearchEventHandler.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.08.06 15:04:51 INFO  no build target found for /home/<USER>/projects/platform.core/src/main/scala/nl/dpes/core/projections/profileservice/SavedSearchEventHandler.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.08.06 15:04:51 INFO  no build target found for /home/<USER>/projects/platform.core/src/main/scala/nl/dpes/core/projections/profileservice/SavedSearchEventHandler.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.08.06 15:04:52 INFO  no build target found for /home/<USER>/projects/platform.core/src/main/scala/nl/dpes/core/projections/profileservice/SavedSearchEventHandler.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.08.06 15:04:53 INFO  no build target found for /home/<USER>/projects/platform.core/src/main/scala/nl/dpes/core/projections/profileservice/SavedSearchEventHandler.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.08.06 15:04:53 INFO  Created report: Optional[file:///home/<USER>/projects/platform.core/src/main/scala/nl/dpes/core/projections/profileservice/SavedSearchEventHandler.scala]
2025.08.06 15:04:53 INFO  empty definition using pc, found symbol in pc: `<none>`. (full report at: "/home/<USER>/projects/platform.core/.metals/.reports/metals-full/2025-08-06/r_empty-definition_15-04-53-125.md")
2025.08.06 15:04:52 INFO  no build target found for /home/<USER>/projects/platform.core/src/main/scala/nl/dpes/core/projections/profileservice/SavedSearchEventHandler.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.08.06 15:04:53 INFO  no build target found for /home/<USER>/projects/platform.core/src/main/scala/nl/dpes/core/projections/profileservice/SavedSearchEventHandler.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.08.06 15:04:53 INFO  empty definition using pc, found symbol in pc: `<none>`. (full report at: "/home/<USER>/projects/platform.core/.metals/.reports/metals-full/2025-08-06/r_empty-definition_15-04-53-125.md")
2025.08.06 15:04:53 INFO  no build target found for /home/<USER>/projects/platform.core/src/main/scala/nl/dpes/core/projections/profileservice/SavedSearchEventHandler.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.08.06 15:04:53 INFO  no build target found for /home/<USER>/projects/platform.core/src/main/scala/nl/dpes/core/projections/profileservice/SavedSearchEventHandler.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.08.06 15:04:53 INFO  no build target found for /home/<USER>/projects/platform.core/src/main/scala/nl/dpes/core/projections/profileservice/SavedSearchEventHandler.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.08.06 15:04:54 INFO  no build target found for /home/<USER>/projects/platform.core/src/main/scala/nl/dpes/core/projections/profileservice/SavedSearchEventHandler.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.08.06 15:04:54 INFO  no build target found for /home/<USER>/projects/platform.core/src/main/scala/nl/dpes/core/projections/profileservice/SavedSearchEventHandler.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.08.06 15:04:54 INFO  empty definition using pc, found symbol in pc: `<none>`. (full report at: "/home/<USER>/projects/platform.core/.metals/.reports/metals-full/2025-08-06/r_empty-definition_15-04-53-125.md")
2025.08.06 15:04:57 INFO  no build target found for /home/<USER>/projects/platform.core/src/main/scala/nl/dpes/core/projections/profileservice/SavedSearchEventHandler.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.08.06 15:04:57 INFO  no build target found for /home/<USER>/projects/platform.core/src/main/scala/nl/dpes/core/projections/profileservice/SavedSearchEventHandler.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.08.06 15:05:12 INFO  no build target found for /home/<USER>/projects/platform.core/src/main/scala/nl/dpes/core/projections/profileservice/SavedSearchEventHandler.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.08.06 15:05:33 INFO  no build target found for /home/<USER>/projects/platform.core/src/main/scala/nl/dpes/core/projections/profileservice/SavedSearchEventHandler.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.08.06 15:05:33 INFO  no build target found for /home/<USER>/projects/platform.core/src/main/scala/nl/dpes/core/projections/profileservice/SavedSearchEventHandler.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.08.06 15:05:35 INFO  no build target found for /home/<USER>/projects/platform.core/src/main/scala/nl/dpes/core/projections/profileservice/SavedSearchEventHandler.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.08.06 15:05:35 INFO  no build target found for /home/<USER>/projects/platform.core/src/main/scala/nl/dpes/core/projections/profileservice/SavedSearchEventHandler.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.08.06 15:05:37 INFO  no build target found for /home/<USER>/projects/platform.core/src/main/scala/nl/dpes/core/projections/profileservice/SavedSearchEventHandler.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.08.06 15:05:37 INFO  no build target found for /home/<USER>/projects/platform.core/src/main/scala/nl/dpes/core/projections/profileservice/SavedSearchEventHandler.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.08.06 15:05:37 INFO  no build target found for /home/<USER>/projects/platform.core/src/main/scala/nl/dpes/core/projections/profileservice/SavedSearchEventHandler.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.08.06 15:05:37 INFO  no build target found for /home/<USER>/projects/platform.core/src/main/scala/nl/dpes/core/projections/profileservice/SavedSearchEventHandler.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.08.06 15:05:37 INFO  no build target found for /home/<USER>/projects/platform.core/src/main/scala/nl/dpes/core/projections/profileservice/SavedSearchEventHandler.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.08.06 15:05:38 INFO  empty definition using pc, found symbol in pc: `<none>`. (full report at: "/home/<USER>/projects/platform.core/.metals/.reports/metals-full/2025-08-06/r_empty-definition_15-04-53-125.md")
2025.08.06 15:05:38 INFO  no build target found for /home/<USER>/projects/platform.core/src/main/scala/nl/dpes/core/projections/profileservice/SavedSearchEventHandler.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.08.06 15:05:37 INFO  no build target found for /home/<USER>/projects/platform.core/src/main/scala/nl/dpes/core/projections/profileservice/SavedSearchEventHandler.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.08.06 15:05:38 INFO  empty definition using pc, found symbol in pc: `<none>`. (full report at: "/home/<USER>/projects/platform.core/.metals/.reports/metals-full/2025-08-06/r_empty-definition_15-04-53-125.md")
2025.08.06 15:05:37 INFO  no build target found for /home/<USER>/projects/platform.core/src/main/scala/nl/dpes/core/projections/profileservice/SavedSearchEventHandler.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.08.06 15:05:39 INFO  no build target found for /home/<USER>/projects/platform.core/src/main/scala/nl/dpes/core/projections/profileservice/SavedSearchEventHandler.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.08.06 15:05:41 INFO  no build target found for /home/<USER>/projects/platform.core/src/main/scala/nl/dpes/core/projections/profileservice/SavedSearchEventHandler.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.08.06 15:05:41 INFO  no build target found for /home/<USER>/projects/platform.core/src/main/scala/nl/dpes/core/projections/profileservice/SavedSearchEventHandler.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.08.06 15:05:41 INFO  no build target found for /home/<USER>/projects/platform.core/src/main/scala/nl/dpes/core/projections/profileservice/SavedSearchEventHandler.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.08.06 15:05:43 INFO  no build target found for /home/<USER>/projects/platform.core/src/main/scala/nl/dpes/core/projections/profileservice/SavedSearchEventHandler.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.08.06 15:05:43 INFO  no build target found for /home/<USER>/projects/platform.core/src/main/scala/nl/dpes/core/projections/profileservice/SavedSearchEventHandler.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.08.06 15:05:43 INFO  no build target found for /home/<USER>/projects/platform.core/src/main/scala/nl/dpes/core/projections/profileservice/SavedSearchEventHandler.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.08.06 15:05:43 INFO  no build target found for /home/<USER>/projects/platform.core/src/main/scala/nl/dpes/core/projections/profileservice/SavedSearchEventHandler.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.08.06 15:05:44 INFO  no build target found for /home/<USER>/projects/platform.core/src/main/scala/nl/dpes/core/projections/profileservice/SavedSearchEventHandler.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.08.06 15:05:44 INFO  empty definition using pc, found symbol in pc: `<none>`. (full report at: "/home/<USER>/projects/platform.core/.metals/.reports/metals-full/2025-08-06/r_empty-definition_15-04-53-125.md")
2025.08.06 15:05:44 INFO  no build target found for /home/<USER>/projects/platform.core/src/main/scala/nl/dpes/core/projections/profileservice/SavedSearchEventHandler.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.08.06 15:05:44 INFO  empty definition using pc, found symbol in pc: `<none>`. (full report at: "/home/<USER>/projects/platform.core/.metals/.reports/metals-full/2025-08-06/r_empty-definition_15-04-53-125.md")
2025.08.06 15:05:44 INFO  no build target found for /home/<USER>/projects/platform.core/src/main/scala/nl/dpes/core/projections/profileservice/SavedSearchEventHandler.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.08.06 15:05:48 INFO  no build target found for /home/<USER>/projects/platform.core/src/main/scala/nl/dpes/core/projections/profileservice/SavedSearchEventHandler.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.08.06 15:05:48 INFO  no build target found for /home/<USER>/projects/platform.core/src/main/scala/nl/dpes/core/projections/profileservice/SavedSearchEventHandler.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.08.06 15:05:49 INFO  no build target found for /home/<USER>/projects/platform.core/src/main/scala/nl/dpes/core/projections/profileservice/SavedSearchEventHandler.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.08.06 15:05:49 INFO  no build target found for /home/<USER>/projects/platform.core/src/main/scala/nl/dpes/core/projections/profileservice/SavedSearchEventHandler.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.08.06 15:05:51 INFO  no build target found for /home/<USER>/projects/platform.core/src/main/scala/nl/dpes/core/projections/profileservice/SavedSearchEventHandler.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.08.06 15:05:56 INFO  no build target found for /home/<USER>/projects/platform.core/src/main/scala/nl/dpes/core/projections/profileservice/SavedSearchEventHandler.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.08.06 15:05:56 INFO  no build target found for /home/<USER>/projects/platform.core/src/main/scala/nl/dpes/core/projections/profileservice/SavedSearchEventHandler.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.08.06 15:05:56 INFO  no build target found for /home/<USER>/projects/platform.core/src/main/scala/nl/dpes/core/projections/profileservice/SavedSearchEventHandler.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.08.06 15:05:57 INFO  no build target found for /home/<USER>/projects/platform.core/src/main/scala/nl/dpes/core/projections/profileservice/SavedSearchEventHandler.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.08.06 15:06:00 INFO  no build target found for /home/<USER>/projects/platform.core/src/main/scala/nl/dpes/core/projections/profileservice/SavedSearchEventHandler.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.08.06 15:06:03 INFO  no build target found for /home/<USER>/projects/platform.core/src/main/scala/nl/dpes/core/projections/profileservice/SavedSearchEventHandler.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.08.06 15:06:11 INFO  no build target found for /home/<USER>/projects/platform.core/src/main/scala/nl/dpes/core/projections/profileservice/SavedSearchEventHandler.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.08.06 15:06:11 INFO  no build target found for /home/<USER>/projects/platform.core/src/main/scala/nl/dpes/core/projections/profileservice/SavedSearchEventHandler.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.08.06 15:06:11 INFO  no build target found for /home/<USER>/projects/platform.core/src/main/scala/nl/dpes/core/projections/profileservice/SavedSearchEventHandler.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.08.06 15:06:11 INFO  no build target found for /home/<USER>/projects/platform.core/src/main/scala/nl/dpes/core/projections/profileservice/SavedSearchEventHandler.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.08.06 15:06:14 INFO  no build target found for /home/<USER>/projects/platform.core/src/main/scala/nl/dpes/core/projections/profileservice/SavedSearchEventHandler.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.08.06 15:06:14 INFO  no build target found for /home/<USER>/projects/platform.core/src/main/scala/nl/dpes/core/projections/profileservice/SavedSearchEventHandler.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.08.06 15:06:18 INFO  no build target found for /home/<USER>/projects/platform.core/src/main/scala/nl/dpes/core/projections/profileservice/SavedSearchEventHandler.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.08.06 15:06:22 INFO  no build target found for /home/<USER>/projects/platform.core/src/main/scala/nl/dpes/core/projections/profileservice/SavedSearchEventHandler.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.08.06 15:06:23 INFO  no build target found for /home/<USER>/projects/platform.core/src/main/scala/nl/dpes/core/projections/profileservice/SavedSearchEventHandler.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.08.06 15:06:27 WARN  no build target for: /home/<USER>/projects/platform.core/src/main/scala/nl/dpes/core/domain/Zoekopdracht.scala
2025.08.06 15:06:27 INFO  no build target found for /home/<USER>/projects/platform.core/src/main/scala/nl/dpes/core/domain/Zoekopdracht.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.08.06 15:06:27 INFO  no build target found for /home/<USER>/projects/platform.core/src/main/scala/nl/dpes/core/domain/Zoekopdracht.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.08.06 15:06:27 INFO  no build target found for /home/<USER>/projects/platform.core/src/main/scala/nl/dpes/core/domain/Zoekopdracht.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.08.06 15:06:27 INFO  no build target found for /home/<USER>/projects/platform.core/src/main/scala/nl/dpes/core/domain/Zoekopdracht.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.08.06 15:06:28 WARN  no build target for: /home/<USER>/projects/platform.core/src/main/scala/nl/dpes/core/projections/profileservice/SavedSearchEventHandler.scala
2025.08.06 15:06:28 INFO  no build target found for /home/<USER>/projects/platform.core/src/main/scala/nl/dpes/core/projections/profileservice/SavedSearchEventHandler.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.08.06 15:06:28 INFO  no build target found for /home/<USER>/projects/platform.core/src/main/scala/nl/dpes/core/projections/profileservice/SavedSearchEventHandler.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.08.06 15:06:28 INFO  no build target found for /home/<USER>/projects/platform.core/src/main/scala/nl/dpes/core/projections/profileservice/SavedSearchEventHandler.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.08.06 15:06:30 INFO  no build target found for /home/<USER>/projects/platform.core/src/main/scala/nl/dpes/core/projections/profileservice/SavedSearchEventHandler.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.08.06 15:06:30 INFO  no build target found for /home/<USER>/projects/platform.core/src/main/scala/nl/dpes/core/projections/profileservice/SavedSearchEventHandler.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.08.06 15:06:30 INFO  no build target found for /home/<USER>/projects/platform.core/src/main/scala/nl/dpes/core/projections/profileservice/SavedSearchEventHandler.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.08.06 15:06:30 INFO  no build target found for /home/<USER>/projects/platform.core/src/main/scala/nl/dpes/core/projections/profileservice/SavedSearchEventHandler.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.08.06 15:06:30 INFO  no build target found for /home/<USER>/projects/platform.core/src/main/scala/nl/dpes/core/projections/profileservice/SavedSearchEventHandler.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.08.06 15:06:30 INFO  no build target found for /home/<USER>/projects/platform.core/src/main/scala/nl/dpes/core/projections/profileservice/SavedSearchEventHandler.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.08.06 15:06:30 INFO  no build target found for /home/<USER>/projects/platform.core/src/main/scala/nl/dpes/core/projections/profileservice/SavedSearchEventHandler.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.08.06 15:06:30 INFO  no build target found for /home/<USER>/projects/platform.core/src/main/scala/nl/dpes/core/projections/profileservice/SavedSearchEventHandler.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.08.06 15:06:30 INFO  no build target found for /home/<USER>/projects/platform.core/src/main/scala/nl/dpes/core/projections/profileservice/SavedSearchEventHandler.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.08.06 15:06:30 INFO  no build target found for /home/<USER>/projects/platform.core/src/main/scala/nl/dpes/core/projections/profileservice/SavedSearchEventHandler.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.08.06 15:06:30 INFO  no build target found for /home/<USER>/projects/platform.core/src/main/scala/nl/dpes/core/projections/profileservice/SavedSearchEventHandler.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.08.06 15:06:30 INFO  no build target found for /home/<USER>/projects/platform.core/src/main/scala/nl/dpes/core/projections/profileservice/SavedSearchEventHandler.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.08.06 15:06:30 INFO  empty definition using pc, found symbol in pc: `<none>`. (full report at: "/home/<USER>/projects/platform.core/.metals/.reports/metals-full/2025-08-06/r_empty-definition_15-04-53-125.md")
2025.08.06 15:06:30 WARN  no build target for: /home/<USER>/projects/platform.core/src/main/scala/nl/dpes/core/domain/Zoekopdracht.scala
2025.08.06 15:06:31 INFO  no build target found for /home/<USER>/projects/platform.core/src/main/scala/nl/dpes/core/domain/Zoekopdracht.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.08.06 15:06:30 INFO  no build target found for /home/<USER>/projects/platform.core/src/main/scala/nl/dpes/core/domain/Zoekopdracht.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.08.06 15:06:31 INFO  no build target found for /home/<USER>/projects/platform.core/src/main/scala/nl/dpes/core/domain/Zoekopdracht.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.08.06 15:06:30 INFO  no build target found for /home/<USER>/projects/platform.core/src/main/scala/nl/dpes/core/domain/Zoekopdracht.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.08.06 15:06:31 INFO  no build target found for /home/<USER>/projects/platform.core/src/main/scala/nl/dpes/core/domain/Zoekopdracht.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.08.06 15:06:30 INFO  no build target found for /home/<USER>/projects/platform.core/src/main/scala/nl/dpes/core/domain/Zoekopdracht.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.08.06 15:06:30 INFO  no build target found for /home/<USER>/projects/platform.core/src/main/scala/nl/dpes/core/domain/Zoekopdracht.scala. Using presentation compiler with project's scala-library version: 3.3.6
Exception in thread "pool-76-thread-1" java.lang.InterruptedException
	at scala.meta.internal.metals.FutureCancelToken.checkCanceled(FutureCancelToken.scala:29)
	at scala.meta.internal.pc.CompilerAccess.onCompilerJobQueue$$anonfun$1(CompilerAccess.scala:244)
	at scala.meta.internal.pc.CompilerJobQueue$Job.run(CompilerJobQueue.scala:153)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:840)
2025.08.06 15:06:30 INFO  no build target found for /home/<USER>/projects/platform.core/src/main/scala/nl/dpes/core/domain/Zoekopdracht.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.08.06 15:06:31 INFO  no build target found for /home/<USER>/projects/platform.core/src/main/scala/nl/dpes/core/domain/Zoekopdracht.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.08.06 15:06:31 INFO  Created report: Optional[file:///home/<USER>/projects/platform.core/src/main/scala/nl/dpes/core/domain/Zoekopdracht.scala]
2025.08.06 15:06:31 INFO  empty definition using pc, found symbol in pc: `<none>`. (full report at: "/home/<USER>/projects/platform.core/.metals/.reports/metals-full/2025-08-06/r_empty-definition_15-06-31-803.md")
2025.08.06 15:06:31 INFO  no build target found for /home/<USER>/projects/platform.core/src/main/scala/nl/dpes/core/domain/Zoekopdracht.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.08.06 15:06:33 INFO  no build target found for /home/<USER>/projects/platform.core/src/main/scala/nl/dpes/core/domain/Zoekopdracht.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.08.06 15:06:34 INFO  no build target found for /home/<USER>/projects/platform.core/src/main/scala/nl/dpes/core/domain/Zoekopdracht.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.08.06 15:06:34 INFO  no build target found for /home/<USER>/projects/platform.core/src/main/scala/nl/dpes/core/domain/Zoekopdracht.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.08.06 15:06:34 INFO  no build target found for /home/<USER>/projects/platform.core/src/main/scala/nl/dpes/core/domain/Zoekopdracht.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.08.06 15:06:35 INFO  no build target found for /home/<USER>/projects/platform.core/src/main/scala/nl/dpes/core/domain/Zoekopdracht.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.08.06 15:06:35 INFO  no build target found for /home/<USER>/projects/platform.core/src/main/scala/nl/dpes/core/domain/Zoekopdracht.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.08.06 15:06:35 INFO  no build target found for /home/<USER>/projects/platform.core/src/main/scala/nl/dpes/core/domain/Zoekopdracht.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.08.06 15:06:35 INFO  no build target found for /home/<USER>/projects/platform.core/src/main/scala/nl/dpes/core/domain/Zoekopdracht.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.08.06 15:06:35 INFO  no build target found for /home/<USER>/projects/platform.core/src/main/scala/nl/dpes/core/domain/Zoekopdracht.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.08.06 15:06:35 INFO  no build target found for /home/<USER>/projects/platform.core/src/main/scala/nl/dpes/core/domain/Zoekopdracht.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.08.06 15:06:40 INFO  no build target found for /home/<USER>/projects/platform.core/src/main/scala/nl/dpes/core/domain/Zoekopdracht.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.08.06 15:06:41 INFO  no build target found for /home/<USER>/projects/platform.core/src/main/scala/nl/dpes/core/domain/Zoekopdracht.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.08.06 15:07:17 INFO  no build target found for /home/<USER>/projects/platform.core/src/main/scala/nl/dpes/core/domain/Zoekopdracht.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.08.06 15:08:00 INFO  no build target found for /home/<USER>/projects/platform.core/src/main/scala/nl/dpes/core/domain/Zoekopdracht.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.08.06 15:08:15 INFO  no build target found for /home/<USER>/projects/platform.core/src/main/scala/nl/dpes/core/domain/Zoekopdracht.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.08.06 15:08:15 INFO  no build target found for /home/<USER>/projects/platform.core/src/main/scala/nl/dpes/core/domain/Zoekopdracht.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.08.06 15:08:15 INFO  no build target found for /home/<USER>/projects/platform.core/src/main/scala/nl/dpes/core/domain/Zoekopdracht.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.08.06 15:08:15 INFO  no build target found for /home/<USER>/projects/platform.core/src/main/scala/nl/dpes/core/domain/Zoekopdracht.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.08.06 15:08:17 INFO  no build target found for /home/<USER>/projects/platform.core/src/main/scala/nl/dpes/core/domain/Zoekopdracht.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.08.06 15:08:53 INFO  no build target found for /home/<USER>/projects/platform.core/src/main/scala/nl/dpes/core/domain/Zoekopdracht.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.08.06 15:08:54 INFO  no build target found for /home/<USER>/projects/platform.core/src/main/scala/nl/dpes/core/domain/Zoekopdracht.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.08.06 15:08:54 INFO  no build target found for /home/<USER>/projects/platform.core/src/main/scala/nl/dpes/core/domain/Zoekopdracht.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.08.06 15:08:54 INFO  no build target found for /home/<USER>/projects/platform.core/src/main/scala/nl/dpes/core/domain/Zoekopdracht.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.08.06 15:08:56 INFO  no build target found for /home/<USER>/projects/platform.core/src/main/scala/nl/dpes/core/domain/Zoekopdracht.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.08.06 15:09:07 INFO  no build target found for /home/<USER>/projects/platform.core/src/main/scala/nl/dpes/core/domain/Zoekopdracht.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.08.06 15:09:07 INFO  no build target found for /home/<USER>/projects/platform.core/src/main/scala/nl/dpes/core/domain/Zoekopdracht.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.08.06 15:09:07 INFO  no build target found for /home/<USER>/projects/platform.core/src/main/scala/nl/dpes/core/domain/Zoekopdracht.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.08.06 15:10:33 INFO  no build target found for /home/<USER>/projects/platform.core/src/main/scala/nl/dpes/core/domain/Zoekopdracht.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.08.06 15:11:05 INFO  no build target found for /home/<USER>/projects/platform.core/src/main/scala/nl/dpes/core/domain/Zoekopdracht.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.08.06 15:11:05 INFO  no build target found for /home/<USER>/projects/platform.core/src/main/scala/nl/dpes/core/domain/Zoekopdracht.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.08.06 15:11:05 INFO  no build target found for /home/<USER>/projects/platform.core/src/main/scala/nl/dpes/core/domain/Zoekopdracht.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.08.06 15:11:08 INFO  no build target found for /home/<USER>/projects/platform.core/src/main/scala/nl/dpes/core/domain/Zoekopdracht.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.08.06 15:11:44 INFO  no build target found for /home/<USER>/projects/platform.core/src/main/scala/nl/dpes/core/domain/Zoekopdracht.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.08.06 15:12:40 INFO  no build target found for /home/<USER>/projects/platform.core/src/main/scala/nl/dpes/core/domain/Zoekopdracht.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.08.06 15:12:40 INFO  no build target found for /home/<USER>/projects/platform.core/src/main/scala/nl/dpes/core/domain/Zoekopdracht.scala. Using presentation compiler with project's scala-library version: 3.3.6
2025.08.06 15:14:08 INFO  Shutting down server
2025.08.06 15:14:08 INFO  shutting down Metals
2025.08.06 15:14:08 INFO  Shut down connection with build server.
2025.08.06 15:14:08 INFO  [0m[31m[E][0m Caught java.lang.NullPointerException: Cannot invoke "scala.collection.immutable.List.filterNot(scala.Function1)" because "sessions" is null
2025.08.06 15:14:08 ERROR Bloop 'bsp' command exited with code 1. Something may be wrong with the current configuration.
Running the [1mclean[0m sub-command to clear the working directory and remove caches might help.
If the error persists, please report the issue as a bug and attach a log with increased verbosity by passing [1m-v -v -v[0m.
Aug 06, 2025 3:14:08 PM io.undertow.Undertow stop
INFO: stopping server: Undertow - 2.3.12.Final
2025.08.06 15:14:08 INFO  Exiting server
2025.08.07 10:27:38 INFO  Started: Metals version 1.6.1 in folders '/home/<USER>/projects/platform.core' for client Visual Studio Code 1.102.3.
2025.08.07 10:27:39 INFO  tracing is disabled for protocol mcp, to enable tracing of incoming and outgoing JSON messages create an empty file at /home/<USER>/projects/platform.core/.metals/mcp.trace.json or /home/<USER>/.cache/metals/mcp.trace.json
2025.08.07 10:27:39 INFO  Attempting to connect to the build server...
2025.08.07 10:27:39 INFO  No running Bloop server found, starting one.
Aug 07, 2025 10:27:39 AM io.undertow.Undertow start
INFO: starting server: Undertow - 2.3.12.Final
Aug 07, 2025 10:27:39 AM org.xnio.Xnio <clinit>
INFO: XNIO version 3.8.16.Final
Aug 07, 2025 10:27:39 AM org.xnio.nio.NioXnio <clinit>
INFO: XNIO NIO Implementation Version 3.8.16.Final
Aug 07, 2025 10:27:39 AM org.jboss.threads.Version <clinit>
INFO: JBoss Threads version 3.5.0.Final
2025.08.07 10:27:39 INFO  Metals MCP server started on port: 37177.
2025.08.07 10:27:40 INFO  Starting compilation server
Align: top-level presets deprecated; use 'preset' subsection
IndentOperator: top-level presets deprecated; use 'preset' subsection
DanglingParentheses: top-level presets deprecated; use 'preset' subsection
Aug 07, 2025 10:27:40 AM org.eclipse.lsp4j.jsonrpc.services.GenericEndpoint notify
INFO: Unsupported notification method: $/setTrace
2025.08.07 10:27:41 INFO  tracing is disabled for protocol BSP, to enable tracing of incoming and outgoing JSON messages create an empty file at /home/<USER>/projects/platform.core/.metals/bsp.trace.json or /home/<USER>/.cache/metals/bsp.trace.json
2025.08.07 10:27:41 INFO  time: Connected to build server in 2.1s
2025.08.07 10:27:41 INFO  Connected to Build server: Bloop v2.0.12
2025.08.07 10:27:41 INFO  Empty build targets. Expected at least one build target identifier. (full report at: "/home/<USER>/projects/platform.core/.metals/.reports/bloop/2025-08-06/r_Empty build targets...._15-01-55-749.md")
2025.08.07 10:27:41 INFO  Empty build targets. Expected at least one build target identifier. (full report at: "/home/<USER>/projects/platform.core/.metals/.reports/bloop/2025-08-06/r_Empty build targets...._15-01-55-749.md")
2025.08.07 10:27:41 INFO  Empty build targets. Expected at least one build target identifier. (full report at: "/home/<USER>/projects/platform.core/.metals/.reports/bloop/2025-08-06/r_Empty build targets...._15-01-55-749.md")
2025.08.07 10:27:41 INFO  Empty build targets. Expected at least one build target identifier. (full report at: "/home/<USER>/projects/platform.core/.metals/.reports/bloop/2025-08-06/r_Empty build targets...._15-01-55-749.md")
2025.08.07 10:27:41 INFO  Empty build targets. Expected at least one build target identifier. (full report at: "/home/<USER>/projects/platform.core/.metals/.reports/bloop/2025-08-06/r_Empty build targets...._15-01-55-749.md")
2025.08.07 10:27:42 INFO  time: indexed workspace in 1.42s
