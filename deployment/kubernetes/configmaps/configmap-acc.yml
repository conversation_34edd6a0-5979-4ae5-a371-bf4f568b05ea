kind: ConfigMap
apiVersion: v1
metadata:
  name: platform-core-config
  labels:
    environment: acceptance
data:
  environment: "acceptance"
  api.host: "core-internal-acc.persgroep.digital"
  api.port: "80"
  grpc.port: "10600"
  db.connectionstring: "****************************************************************************************************************************************************"
  service.jobseekers.url: "http://jobseekerservice/api/v1"
  logging.level: "INFO"
  notifier.events.username: "NDP [acc]"
  elasticsearch.host: "es-recruiter-acc.persgroep.digital"
  elasticsearch.port: "9200"
  projections.core.tokenName: "nl.dpes.core.projections.core-v5"
  projections.core.version: "5"
  projections.index.tokenName: "nl.dpes.core.projections.index-v5"
  projections.index.version: "5"
  projections.elasticsearch.tokenName: "nl.dpes.core.projections.elasticsearch-v4"
  projections.elasticsearch.version: "4"
  eventPublisher.domain.streamName: "acc-domain"
  profileService.databaseUrl: "*********************************************************************************************************************************************************************************"
  profileService.favoriteProfilesProjection.tokenName: "nl.dpes.core.projections.favoriteprofiles-v"
  profileService.favoriteProfilesProjection.version: "1"
  profileService.savedSearchProjection.tokenName: "nl.dpes.core.projections.savedsearch-v"
  profileService.savedSearchProjection.version: "1"
