apiVersion: v1
kind: Service
metadata:
  name: platform-core
spec:
  ports:
    - port: 80
      targetPort: 8999
      protocol: TCP
      name: http
    - port: 10600
      targetPort: 10600
      protocol: TCP
      name: grpc
  selector:
    app: platform-core
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: platform-core
  namespace: default
spec:
  ingressClassName: nginx-internal
  rules:
  - host: core-internal-acc.persgroep.digital
    http:
      paths:
      - path:
        pathType: ImplementationSpecific
        backend:
          service:
            name: platform-core
            port:
              number: 80
