apiVersion: apps/v1
kind: Deployment
metadata:
  name: platform-core
  namespace: default
spec:
  replicas: 2
  revisionHistoryLimit: 5
  progressDeadlineSeconds: 300
  selector:
    matchLabels:
      app: platform-core
  strategy:
    rollingUpdate:
      maxSurge: 2
      maxUnavailable: 0
    type: RollingUpdate
  template:
    metadata:
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "9095"
        prometheus.io/scheme: "http"
        prometheus.io/path: ""
      labels:
        app: platform-core
    spec:
      volumes:
        - name: axoniq-gdpr
          secret:
            secretName: axoniq-gdpr
            items:
              - key: license
                path: axoniq.license
      containers:
        - name: platform-core
          image: 674201978047.dkr.ecr.eu-west-1.amazonaws.com/platform-core:{{BUILD_NUMBER}}
          command: ["/opt/docker/bin/web-server"]
          resources:
            limits:
              memory: 1000Mi
            requests:
              cpu: 50m
              memory: 750Mi
          ports:
            - name: app-port
              containerPort: 8999
          volumeMounts:
            - name: axoniq-gdpr
              mountPath: /usr/local/etc/axoniq
              readOnly: true
          env:
            - name: POD_NAME
              valueFrom:
                fieldRef:
                  fieldPath: metadata.name
            - name: AWS_ACCESS_KEY_ID
              valueFrom:
                secretKeyRef:
                  name: platform-core-aws
                  key: access-key-id
            - name: AWS_SECRET_ACCESS_KEY
              valueFrom:
                secretKeyRef:
                  name: platform-core-aws
                  key: secret-access-key
            - name: API_HOST
              valueFrom:
                configMapKeyRef:
                  name: platform-core-config
                  key: api.host
            - name: API_PORT
              valueFrom:
                configMapKeyRef:
                  name: platform-core-config
                  key: api.port
            - name: GRPC_PORT
              valueFrom:
                configMapKeyRef:
                  name: platform-core-config
                  key: grpc.port
            - name: ENVIRONMENT
              valueFrom:
                configMapKeyRef:
                  name: platform-core-config
                  key: environment
            - name: DB_CONNECTION_STRING
              valueFrom:
                configMapKeyRef:
                  name: platform-core-config
                  key: db.connectionstring
            - name: DB_USER
              valueFrom:
                secretKeyRef:
                  name: platform-core-rds-secret
                  key: username
            - name: DB_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: platform-core-rds-secret
                  key: password
            - name: SERVICE_JOBSEEKERS_URL
              valueFrom:
                configMapKeyRef:
                  name: platform-core-config
                  key: service.jobseekers.url
            - name: SERVICE_JOBSEEKERS_KEY
              valueFrom:
                secretKeyRef:
                  name: platform-core-api-gateway-secret
                  key: key
            - name: DYNAMODB_URL
              value: ""
            - name: DYNAMODB_REGION
              value: ""
            - name: KINESIS_URL
              value: ""
            - name: AXON_DISTRIBUTED_COMMAND_GATEWAY
              value: "false"
            - name: PHP_EXECUTABLE
              value: "php"
            - name: LOGGING_LEVEL
              valueFrom:
                configMapKeyRef:
                  name: platform-core-config
                  key: logging.level
            - name: NOTIFIER_EVENTS_WEBHOOK_URL
              value: "*****************************************************************************"
            - name: NOTIFIER_EVENTS_USERNAME
              valueFrom:
                configMapKeyRef:
                  name: platform-core-config
                  key: notifier.events.username
            - name: ELASTICSEARCH_URL
              valueFrom:
                configMapKeyRef:
                  name: platform-core-config
                  key: elasticsearch.host
            - name: ELASTICSEARCH_PORT
              valueFrom:
                configMapKeyRef:
                  name: platform-core-config
                  key: elasticsearch.port
            - name: PROJECTIONS_CORE_TOKENNAME
              valueFrom:
                configMapKeyRef:
                  name: platform-core-config
                  key: projections.core.tokenName
            - name: PROJECTIONS_CORE_VERSION
              valueFrom:
                configMapKeyRef:
                  name: platform-core-config
                  key: projections.core.version
            - name: PROJECTIONS_INDEX_TOKENNAME
              valueFrom:
                configMapKeyRef:
                  name: platform-core-config
                  key: projections.index.tokenName
            - name: PROJECTIONS_INDEX_VERSION
              valueFrom:
                configMapKeyRef:
                  name: platform-core-config
                  key: projections.index.version
            - name: PROJECTIONS_ELASTICSEARCH_TOKENNAME
              valueFrom:
                configMapKeyRef:
                  name: platform-core-config
                  key: projections.elasticsearch.tokenName
            - name: PROJECTIONS_ELASTICSEARCH_VERSION
              valueFrom:
                configMapKeyRef:
                  name: platform-core-config
                  key: projections.elasticsearch.version
            - name: AXONIQ_GDPR_LICENSE_FILE
              value: /usr/local/etc/axoniq/axoniq.license
            - name: EVENTPUBLISHER_DOMAIN_STREAMNAME
              valueFrom:
                configMapKeyRef:
                  name: platform-core-config
                  key: eventPublisher.domain.streamName
            - name: PROFILE_SERVICE_DATABASE_CONNECTION_STRING
              valueFrom:
                configMapKeyRef:
                  name: platform-core-config
                  key: profileService.databaseUrl
            - name: FAVORITE_PROFILES_PROJECTION_TOKENNAME
              valueFrom:
                configMapKeyRef:
                  name: platform-core-config
                  key: profileService.favoriteProfilesProjection.tokenName
            - name: FAVORITE_PROFILES_PROJECTION_VERSION
              valueFrom:
                configMapKeyRef:
                  name: platform-core-config
                  key: profileService.favoriteProfilesProjection.version
            - name: SAVED_SEARCH_PROJECTION_TOKENNAME
              valueFrom:
                configMapKeyRef:
                  name: platform-core-config
                  key: profileService.savedSearchProjection.tokenName
            - name: SAVED_SEARCH_PROJECTION_VERSION
              valueFrom:
                configMapKeyRef:
                  name: platform-core-config
                  key: profileService.savedSearchProjection.version
            - name: KINESIS_URL
              value: ""
            - name: RSA_KEY_ID
              valueFrom:
                secretKeyRef:
                  name: platform-core-rsa-keys
                  key: rsa-key-id
            - name: RSA_PRIVATE_KEY
              valueFrom:
                secretKeyRef:
                  name: platform-core-rsa-keys
                  key: rsa-private-key
            - name: RSA_PUBLIC_KEY
              valueFrom:
                secretKeyRef:
                  name: platform-core-rsa-keys
                  key: rsa-public-key
            - name: AWS_CBOR_DISABLE
              value: "1"
            - name: API_KEY_PRIVACY_SERVICE
              valueFrom:
                secretKeyRef:
                  name: platform-core
                  key: api-key.privacy-service
            - name: PROFILE_SERVICE_DATABASE_USER
              valueFrom:
                secretKeyRef:
                  name: profile-service
                  key: mysql.user
            - name: PROFILE_SERVICE_DATABASE_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: profile-service
                  key: mysql.password
          readinessProbe:
            httpGet:
              path: /api/v1/status
              port: app-port
            initialDelaySeconds: 5
            periodSeconds: 5
            timeoutSeconds: 5
          livenessProbe:
            httpGet:
              path: /api/v1/status
              port: app-port
            initialDelaySeconds: 300
            periodSeconds: 10
            timeoutSeconds: 11
      restartPolicy: Always
      dnsPolicy: Default
