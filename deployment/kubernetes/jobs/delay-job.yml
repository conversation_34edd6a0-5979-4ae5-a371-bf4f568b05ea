apiVersion: batch/v1
kind: Job
metadata:
  name: "delay-{{BUILD_NUMBER}}"
spec:
  backoffLimit: 1
  template:
    metadata:
      name: "delay-{{BUILD_NUMBER}}"
    spec:
      containers:
        - name: platform-core
          image: 674201978047.dkr.ecr.eu-west-1.amazonaws.com/platform-core:latest
          command: ["/opt/docker/bin/test-application", "finishFast"]
          resources:
            limits:
              cpu: 200m
              memory: 800Mi
            requests:
              cpu: 20m
              memory: 800Mi
          env:
            - name: POD_NAME
              valueFrom:
                fieldRef:
                  fieldPath: metadata.name
            - name: AWS_ACCESS_KEY_ID
              valueFrom:
                secretKeyRef:
                  name: platform-core-aws
                  key: access-key-id
            - name: AWS_SECRET_ACCESS_KEY
              valueFrom:
                secretKeyRef:
                  name: platform-core-aws
                  key: secret-access-key
            - name: ENVIRONMENT
              valueFrom:
                configMapKeyRef:
                  name: platform-core-config
                  key: environment
            - name: DB_CONNECTION_STRING
              valueFrom:
                configMapKeyRef:
                  name: platform-core-config
                  key: db.connectionstring
            - name: DB_USER
              valueFrom:
                secretKeyRef:
                  name: platform-core-rds-secret
                  key: username
            - name: DB_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: platform-core-rds-secret
                  key: password
            - name: DYNAMODB_URL
              value: ""
            - name: DYNAMODB_REGION
              value: ""
            - name: LOGGING_LEVEL
              valueFrom:
                configMapKeyRef:
                  name: platform-core-config
                  key: logging.level
            - name: NOTIFIER_EVENTS_WEBHOOK_URL
              value: "*****************************************************************************"
            - name: NOTIFIER_EVENTS_USERNAME
              valueFrom:
                configMapKeyRef:
                  name: platform-core-config
                  key: notifier.events.username
            - name: PROJECTIONS_CORE_TOKENNAME
              value: "nl.dpes.core.projections.core-v{{PROJECTION_VERSION}}"
            - name: PROJECTIONS_CORE_VERSION
              value: "{{PROJECTION_VERSION}}"
            - name: PROJECTIONS_INDEX_TOKENNAME
              value: "nl.dpes.core.projections.index-v{{PROJECTION_VERSION}}"
            - name: PROJECTIONS_INDEX_VERSION
              value: "{{PROJECTION_VERSION}}"
      restartPolicy: Never
