apiVersion: batch/v1
kind: Job
metadata:
  name: "platform-core-replay-{{BUILD_NUMBER}}"
spec:
  backoffLimit: 2
  template:
    metadata:
      name: "platform-core-replay-{{BUILD_NUMBER}}"
    spec:
      volumes:
      - name: axoniq-gdpr
        secret:
          secretName: axoniq-gdpr
          items:
          - key: license
            path: axoniq.license
      containers:
        - name: platform-core
          image: 674201978047.dkr.ecr.eu-west-1.amazonaws.com/platform-core:{{BUILD_NUMBER}}
          command: ["/opt/docker/bin/event-tracker"]
          resources:
            limits:
              cpu: 200m
              memory: 800Mi
            requests:
              cpu: 20m
              memory: 800Mi
          volumeMounts:
          - name: axoniq-gdpr
            mountPath: /usr/local/etc/axoniq
            readOnly: true
          env:
            - name: POD_NAME
              valueFrom:
                fieldRef:
                  fieldPath: metadata.name
            - name: AWS_ACCESS_KEY_ID
              valueFrom:
                secretKeyRef:
                  name: platform-core-aws
                  key: access-key-id
            - name: AWS_SECRET_ACCESS_KEY
              valueFrom:
                secretKeyRef:
                  name: platform-core-aws
                  key: secret-access-key
            - name: ENVIRONMENT
              valueFrom:
                configMapKeyRef:
                  name: platform-core-config
                  key: environment
            - name: DB_CONNECTION_STRING
              valueFrom:
                configMapKeyRef:
                  name: platform-core-config
                  key: db.connectionstring
            - name: DB_USER
              valueFrom:
                secretKeyRef:
                  name: platform-core-rds-secret
                  key: username
            - name: DB_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: platform-core-rds-secret
                  key: password
            - name: DYNAMODB_URL
              value: ""
            - name: DYNAMODB_REGION
              value: ""
            - name: AXONIQ_GDPR_LICENSE_FILE
              value: /usr/local/etc/axoniq/axoniq.license
            - name: LOGGING_LEVEL
              valueFrom:
                configMapKeyRef:
                  name: platform-core-config
                  key: logging.level
            - name: NOTIFIER_EVENTS_WEBHOOK_URL
              value: "*****************************************************************************"
            - name: NOTIFIER_EVENTS_USERNAME
              valueFrom:
                configMapKeyRef:
                  name: platform-core-config
                  key: notifier.events.username
            - name: PROJECTIONS_CORE_TOKENNAME
              value: "nl.dpes.core.projections.core-v{{PROJECTION_VERSION}}"
            - name: PROJECTIONS_CORE_VERSION
              value: "{{PROJECTION_VERSION}}"
            - name: PROJECTIONS_INDEX_TOKENNAME
              value: "nl.dpes.core.projections.index-v{{PROJECTION_VERSION}}"
            - name: PROJECTIONS_INDEX_VERSION
              value: "{{PROJECTION_VERSION}}"
            - name: ELASTICSEARCH_URL
              valueFrom:
                configMapKeyRef:
                  name: platform-core-config
                  key: elasticsearch.host
            - name: ELASTICSEARCH_PORT
              valueFrom:
                configMapKeyRef:
                  name: platform-core-config
                  key: elasticsearch.port
            - name: PROJECTIONS_ELASTICSEARCH_VERSION
              value: "{{PROJECTION_VERSION}}"
      restartPolicy: Never
