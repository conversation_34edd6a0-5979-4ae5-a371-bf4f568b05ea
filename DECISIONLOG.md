Decision Log
=====

### Introduction
The point of this document is to provide information for future developers about various development and architectural decisions.

### #001 Keep track of activity timestamps for users
#### Context
We need to delete users after a certain period of inactivity.
For this we need to store the timestamp of the latest login of the user. In the future this might be extended for other user flows (e.g. subscription to a newsletter).

#### Solution
We will be storing the timestamp of the latest activity of a job seeker in the Dynamodb base CoreWerkzoekendeAccountProjections projection. The data itself will be not be event sourced as it is not deemed business critical and also it might come from sources outside event sourcing.
