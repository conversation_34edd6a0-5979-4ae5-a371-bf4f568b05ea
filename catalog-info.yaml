apiVersion: backstage.io/v1alpha1
kind: Component
metadata:
  name: platform-core
  annotations:
    github.com/project-slug: vnumedia/platform-core
  links:
    - url: https://grafana-pro.persgroep.digital/d/9e8Qwsx0k/platform-core
      title: Grafana Dashboard
      icon: dashboard
spec:
  owner: stream-team
  type: component
  lifecycle: production
  providesApis:
    - platform-core-api
  consumesApis:
    - mail-service-api
    - sim-api
---
apiVersion: backstage.io/v1alpha1
kind: API
metadata:
  name: platform-core-api
spec:
  type: openapi
  lifecycle: production
  owner: stream-team
  definition: |
    openapi: 3.0.3
    info:
      title: Platform Core
      version: '1.0'
    paths:
