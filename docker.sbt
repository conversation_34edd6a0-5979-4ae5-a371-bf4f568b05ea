import com.typesafe.sbt.packager.docker.Cmd

enablePlugins(
  DockerPlugin,
  JavaAppPackaging,
  AshScriptPlugin
)

dockerRepository := Some("674201978047.dkr.ecr.eu-west-1.amazonaws.com")
dockerBaseImage := "openjdk:11-jre-slim"

Docker / version := scala.util.Properties.propOrNone("version").getOrElse("latest")
Docker / maintainer := "Kraanspoor <<EMAIL>>"

dockerCommands := dockerCommands.value.filterNot{
  case Cmd("USER", args@_*) if args contains "1001:0" => true
  case _ => false
}
dockerCommands ++= Seq(
  Cmd("RUN apt-get update && apt-get install -y php && rm -rf /var/lib/apt/lists/*"),
  Cmd("USER", "1001:0")
)
