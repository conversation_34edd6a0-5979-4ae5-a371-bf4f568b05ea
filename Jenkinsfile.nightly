pipeline {
  agent {
    label 'ndp'
  }
  stages {
    stage('Analyse') {
      steps {
        ansiColor('xterm') {
          sh "sbt -mem 4096 -Drevision=${BUILD_NUMBER} clean scalafmtAll startTestingContainers coverage test coverageReport"
          sh "sbt -Drevision=${BUILD_NUMBER} sonar"
        }
      }
      post {
        always {
          ansiColor('xterm') {
            sh "sbt stopTestingContainers"
          }
        }
      }
    }
  }
}
