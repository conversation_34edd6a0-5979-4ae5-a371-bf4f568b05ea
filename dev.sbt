import scala.sys.process._

lazy val startContainers = taskKey[Unit]("Start the necessary Docker containers for DEV")

startContainers := {
  streams.value.log.info(
    "docker-compose up -d" !!
  )
}

lazy val setupStreams = taskKey[Unit]("Create the Kinesis stream for DEV")
setupStreams := {
  import com.amazonaws.client.builder.AwsClientBuilder.EndpointConfiguration
  import com.amazonaws.services.kinesis.{AmazonKinesis, AmazonKinesisClient}

  import scala.collection.JavaConverters._
  import scala.util.{Failure, Try, Success}

  scala.util.Properties.setProp("com.amazonaws.sdk.disableCbor", "1")

  lazy val log = streams.value.log

  def retry[T](n: Int)(fn: => T): T = {
    Try {
      fn
    } match {
      case Success(t) => t
      case Failure(_) if n > 1 =>
        Thread.sleep(1000)
        retry(n-1)(fn)
    }
  }

  retry(5) {
    val kinesisClient: AmazonKinesis = AmazonKinesisClient.builder()
      .withEndpointConfiguration(new EndpointConfiguration("http://127.0.0.1:4666", "eu-west-1"))
      .build()

    if (!kinesisClient.listStreams().getStreamNames.asScala.contains("dev-domain")) {
      log.info("Creating Kinesis stream for development")
      kinesisClient.createStream("dev-domain", 1)
    }
  }
}

Compile / run := {
  setupStreams.value
  (Compile / run).evaluated
}

reStart := {
  setupStreams.value
  reStart.evaluated
}

Test / test := {
  setupStreams.value
  (Test / test).value
}
